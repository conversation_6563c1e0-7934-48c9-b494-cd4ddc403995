<?php
/**
 * Add unique constraint to phone column and remove duplicate contacts
 */

require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "🔍 Checking for duplicate phone numbers...\n";
    
    // Find duplicate phone numbers
    $duplicates = $db->fetchAll("
        SELECT phone, COUNT(*) as count, GROUP_CONCAT(id) as ids
        FROM contacts 
        WHERE phone != '' AND phone IS NOT NULL
        GROUP BY phone 
        HAVING COUNT(*) > 1
        ORDER BY phone
    ");
    
    if (empty($duplicates)) {
        echo "✅ No duplicate phone numbers found.\n";
    } else {
        echo "⚠️ Found " . count($duplicates) . " duplicate phone numbers:\n";
        
        foreach ($duplicates as $duplicate) {
            $phone = $duplicate['phone'];
            $count = $duplicate['count'];
            $ids = explode(',', $duplicate['ids']);
            
            echo "📞 Phone: $phone ($count duplicates)\n";
            echo "   IDs: " . implode(', ', $ids) . "\n";
            
            // Keep the first contact (oldest), delete the rest
            $keepId = array_shift($ids);
            echo "   ✅ Keeping contact ID: $keepId\n";
            
            foreach ($ids as $deleteId) {
                echo "   🗑️ Deleting duplicate contact ID: $deleteId\n";
                $db->query("DELETE FROM contacts WHERE id = ?", [$deleteId]);
            }
            echo "\n";
        }
        
        echo "✅ Removed duplicate contacts.\n";
    }
    
    // Check if unique constraint already exists
    $constraints = $db->fetchAll("
        SELECT CONSTRAINT_NAME 
        FROM information_schema.TABLE_CONSTRAINTS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'contacts' 
        AND CONSTRAINT_TYPE = 'UNIQUE'
        AND CONSTRAINT_NAME LIKE '%phone%'
    ");
    
    if (empty($constraints)) {
        echo "🔧 Adding unique constraint to phone column...\n";
        
        // Add unique constraint
        $db->query("ALTER TABLE contacts ADD CONSTRAINT unique_phone UNIQUE (phone)");
        
        echo "✅ Unique constraint added to phone column.\n";
    } else {
        echo "ℹ️ Unique constraint already exists on phone column.\n";
    }
    
    echo "🎉 Database cleanup completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
