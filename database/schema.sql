-- Contact Sphere Organizer - MySQL Database Schema
-- This database stores all contacts, attachments, media, files, reminders, and other data

-- Create the database
CREATE DATABASE IF NOT EXISTS contact_sphere_organizer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE contact_sphere_organizer;

-- Users table (for future authentication)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Categories table
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    color VARCHAR(7) DEFAULT '#3B82F6', -- Hex color code
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contacts table (main contacts table)
CREATE TABLE contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    state VARCHAR(50),
    country VARCHAR(50) DEFAULT 'India',
    postal_code VARCHAR(10),
    company VARCHAR(100),
    job_title VARCHAR(100),
    website VARCHAR(255),
    notes TEXT,
    build_type ENUM('Residential', 'Commercial', 'Industrial', 'Mixed', 'Other') DEFAULT 'Residential',
    budget_range VARCHAR(50),
    project_status ENUM('Prospect', 'In Discussion', 'Shortlisted', 'Confirmed', 'Completed', 'Lost') DEFAULT 'Prospect',
    source VARCHAR(100),
    is_favorite BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    is_shortlisted BOOLEAN DEFAULT FALSE,
    has_active_reminder BOOLEAN DEFAULT FALSE,
    date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_name (name),
    INDEX idx_city (city),
    INDEX idx_state (state),
    INDEX idx_is_favorite (is_favorite),
    INDEX idx_is_pinned (is_pinned),
    INDEX idx_is_shortlisted (is_shortlisted),
    INDEX idx_project_status (project_status),
    INDEX idx_build_type (build_type)
);

-- Contact Categories (many-to-many relationship)
CREATE TABLE contact_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contact_category (contact_id, category_id)
);

-- Files/Attachments table
CREATE TABLE files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_type ENUM('image', 'video', 'document', 'audio', 'other') NOT NULL,
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by)
);

-- Contact Files (many-to-many relationship)
CREATE TABLE contact_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    file_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    UNIQUE KEY unique_contact_file (contact_id, file_id)
);

-- Reminders table
CREATE TABLE reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    reminder_date DATETIME NOT NULL,
    reminder_type ENUM('call', 'meeting', 'follow_up', 'deadline', 'other') DEFAULT 'follow_up',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    is_completed BOOLEAN DEFAULT FALSE,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern VARCHAR(100), -- 'daily', 'weekly', 'monthly', 'yearly'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_is_completed (is_completed),
    INDEX idx_priority (priority),
    INDEX idx_contact_id (contact_id)
);

-- WhatsApp Messages table
CREATE TABLE whatsapp_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    message_text TEXT NOT NULL,
    message_type ENUM('text', 'image', 'video', 'document', 'audio', 'bulk') DEFAULT 'text',
    status ENUM('sent', 'delivered', 'read', 'failed', 'pending') DEFAULT 'pending',
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    read_at TIMESTAMP NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,
    is_bulk_message BOOLEAN DEFAULT FALSE,
    bulk_group_id VARCHAR(100), -- For grouping bulk messages
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    INDEX idx_contact_id (contact_id),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_is_bulk_message (is_bulk_message)
);

-- Message Attachments table
CREATE TABLE message_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    file_id INT NOT NULL,
    attachment_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES whatsapp_messages(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    INDEX idx_message_id (message_id),
    INDEX idx_file_id (file_id)
);

-- Message Templates table
CREATE TABLE message_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_is_active (is_active)
);

-- Template Attachments table
CREATE TABLE template_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    template_id INT NOT NULL,
    file_id INT NOT NULL,
    attachment_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES message_templates(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE,
    INDEX idx_template_id (template_id),
    INDEX idx_file_id (file_id)
);

-- Activity Log table
CREATE TABLE activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- 'contact', 'message', 'reminder', 'file', etc.
    entity_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity_type (entity_type),
    INDEX idx_created_at (created_at)
);

-- Settings table
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT INTO categories (name, color, description) VALUES
('Hot Lead', '#EF4444', 'High priority prospects'),
('Warm Lead', '#F59E0B', 'Medium priority prospects'),
('Cold Lead', '#6B7280', 'Low priority prospects'),
('Client', '#10B981', 'Existing clients'),
('Vendor', '#8B5CF6', 'Suppliers and vendors'),
('Partner', '#3B82F6', 'Business partners'),
('Family', '#EC4899', 'Family contacts'),
('Friend', '#06B6D4', 'Personal friends');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Contact Sphere Organizer', 'string', 'Company name'),
('default_currency', 'INR', 'string', 'Default currency'),
('timezone', 'Asia/Kolkata', 'string', 'Default timezone'),
('date_format', 'DD/MM/YYYY', 'string', 'Date format'),
('max_file_size', '16777216', 'number', 'Maximum file size in bytes (16MB)'),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","txt","mp4","avi","mov","mp3","wav"]', 'json', 'Allowed file types'),
('whatsapp_bulk_delay', '2000', 'number', 'Delay between bulk messages in milliseconds'),
('auto_backup_enabled', 'true', 'boolean', 'Enable automatic database backup'),
('backup_frequency', 'daily', 'string', 'Backup frequency (daily, weekly, monthly)');

-- Create indexes for better performance
CREATE INDEX idx_contacts_search ON contacts(name, phone, email, city, state);
CREATE INDEX idx_reminders_active ON reminders(reminder_date, is_completed) WHERE is_completed = FALSE;
CREATE INDEX idx_messages_recent ON whatsapp_messages(sent_at, contact_id);
CREATE INDEX idx_files_recent ON files(created_at, file_type);

-- Create a view for contact summary
CREATE VIEW contact_summary AS
SELECT 
    c.id,
    c.name,
    c.phone,
    c.email,
    c.city,
    c.state,
    c.build_type,
    c.project_status,
    c.is_favorite,
    c.is_pinned,
    c.is_shortlisted,
    c.has_active_reminder,
    c.created_at,
    COUNT(DISTINCT cf.category_id) as category_count,
    COUNT(DISTINCT m.id) as message_count,
    COUNT(DISTINCT r.id) as reminder_count,
    COUNT(DISTINCT f.id) as file_count
FROM contacts c
LEFT JOIN contact_categories cf ON c.id = cf.contact_id
LEFT JOIN whatsapp_messages m ON c.id = m.contact_id
LEFT JOIN reminders r ON c.id = r.contact_id
LEFT JOIN contact_files f ON c.id = f.contact_id
GROUP BY c.id;

-- Create a view for active reminders
CREATE VIEW active_reminders AS
SELECT 
    r.id,
    r.title,
    r.description,
    r.reminder_date,
    r.reminder_type,
    r.priority,
    r.is_completed,
    c.id as contact_id,
    c.name as contact_name,
    c.phone as contact_phone,
    c.email as contact_email
FROM reminders r
JOIN contacts c ON r.contact_id = c.id
WHERE r.is_completed = FALSE AND r.reminder_date >= NOW()
ORDER BY r.reminder_date ASC;

-- Create a view for recent messages
CREATE VIEW recent_messages AS
SELECT 
    m.id,
    m.message_text,
    m.message_type,
    m.status,
    m.sent_at,
    m.is_bulk_message,
    c.id as contact_id,
    c.name as contact_name,
    c.phone as contact_phone
FROM whatsapp_messages m
JOIN contacts c ON m.contact_id = c.id
ORDER BY m.sent_at DESC
LIMIT 100;

-- Grant permissions (adjust as needed for your hosting environment)
-- GRANT ALL PRIVILEGES ON contact_sphere_organizer.* TO 'your_username'@'localhost';
-- FLUSH PRIVILEGES; 