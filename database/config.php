<?php
/**
 * Database Configuration for Contact Sphere Organizer
 * This file contains database connection settings for shared hosting
 */

// Database configuration
define('DB_HOST', 'localhost');           // Usually 'localhost' for shared hosting
define('DB_NAME', 'webwaaio_contact_sphere_organize');  // Your database name
define('DB_USER', 'webwaaio_contact_sphere_organizer');    // Your database username
define('DB_PASS', '@Sadiqis007');    // Your database password
define('DB_CHARSET', 'utf8mb4');          // Character set

// File upload settings
define('UPLOAD_DIR', '../uploads/');      // Directory for file uploads
define('MAX_FILE_SIZE', 16 * 1024 * 1024); // 16MB max file size
define('ALLOWED_TYPES', [
    'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'video' => ['mp4', 'avi', 'mov', 'mkv', 'webm'],
    'document' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
    'audio' => ['mp3', 'wav', 'ogg', 'm4a']
]);

// Application settings
define('APP_NAME', 'Contact Sphere Organizer');
define('APP_VERSION', '1.0.0');
define('TIMEZONE', 'Asia/Kolkata');

// Security settings
define('JWT_SECRET', 'your-super-secret-jwt-key-change-this'); // Change this!
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds

// WhatsApp settings
define('WHATSAPP_BULK_DELAY', 2000); // Delay between bulk messages (ms)
define('WHATSAPP_MAX_RETRIES', 3);   // Maximum retry attempts

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            throw new Exception("Database query failed");
        }
    }
    
    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }
    
    public function insert($table, $data) {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        $this->query($sql, $data);
        
        return $this->connection->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = implode(', ', array_map(fn($key) => "$key = :$key", array_keys($data)));
        $sql = "UPDATE $table SET $setClause WHERE $where";
        
        $params = array_merge($data, $whereParams);
        $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM $table WHERE $where";
        $this->query($sql, $params);
    }

    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $result = $stmt->execute($params);
            return $result;
        } catch (PDOException $e) {
            error_log("Database execute failed: " . $e->getMessage());
            throw new Exception("Database execute failed: " . $e->getMessage());
        }
    }
}

/**
 * Utility Functions
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validatePhone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    // Check if it's a valid Indian phone number (10-12 digits)
    return strlen($phone) >= 10 && strlen($phone) <= 12;
}

function generateUniqueId() {
    return uniqid() . '_' . time();
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

function getFileType($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if (in_array($extension, ALLOWED_TYPES['image'])) {
        return 'image';
    } elseif (in_array($extension, ALLOWED_TYPES['video'])) {
        return 'video';
    } elseif (in_array($extension, ALLOWED_TYPES['document'])) {
        return 'document';
    } elseif (in_array($extension, ALLOWED_TYPES['audio'])) {
        return 'audio';
    } else {
        return 'other';
    }
}

function createUploadDirectory() {
    if (!file_exists(UPLOAD_DIR)) {
        mkdir(UPLOAD_DIR, 0755, true);
    }
    
    // Create subdirectories
    $subdirs = ['images', 'videos', 'documents', 'audio', 'temp'];
    foreach ($subdirs as $dir) {
        $path = UPLOAD_DIR . $dir;
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }
    }
}

function logActivity($action, $entityType, $entityId = null, $description = '') {
    try {
        $db = Database::getInstance();
        $data = [
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'description' => $description,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $db->insert('activity_log', $data);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Set timezone
date_default_timezone_set(TIMEZONE);

// Create upload directory if it doesn't exist
createUploadDirectory();

// Error reporting (disable in production)
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?> 