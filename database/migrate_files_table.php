<?php
/**
 * Database Migration: Add missing columns to files table
 * This script will update the files table to include all required columns
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h2>🔄 Database Migration: Files Table</h2>";

try {
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check current table structure
    echo "<h3>📋 Current Files Table Structure:</h3>";
    $columns = $db->fetchAll("DESCRIBE files");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Define required columns
    $requiredColumns = [
        'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
        'filename' => 'VARCHAR(255) NOT NULL',
        'original_name' => 'VARCHAR(255) NOT NULL', 
        'file_path' => 'VARCHAR(500) NOT NULL',
        'file_size' => 'BIGINT NOT NULL',
        'mime_type' => 'VARCHAR(100) NOT NULL',
        'file_type' => "ENUM('image', 'video', 'document', 'audio', 'other') NOT NULL",
        'folder' => "VARCHAR(100) DEFAULT 'Default'",
        'uploaded_by' => 'INT DEFAULT NULL',
        'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
        'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    echo "<h3>🔧 Adding Missing Columns:</h3>";
    
    $columnsAdded = 0;
    foreach ($requiredColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $existingColumns)) {
            echo "<p>Adding column: <strong>$columnName</strong></p>";
            
            try {
                $sql = "ALTER TABLE files ADD COLUMN $columnName $columnDefinition";
                $db->query($sql);
                echo "<p style='color: green;'>✅ Added column '$columnName'</p>";
                $columnsAdded++;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to add column '$columnName': " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ Column '$columnName' already exists</p>";
        }
    }
    
    // Add indexes if they don't exist
    echo "<h3>📊 Adding Indexes:</h3>";
    
    try {
        // Check if indexes exist
        $indexes = $db->fetchAll("SHOW INDEX FROM files");
        $existingIndexes = array_column($indexes, 'Key_name');
        
        if (!in_array('idx_file_type', $existingIndexes)) {
            $db->query("ALTER TABLE files ADD INDEX idx_file_type (file_type)");
            echo "<p style='color: green;'>✅ Added index on file_type</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Index idx_file_type already exists</p>";
        }
        
        if (!in_array('idx_folder', $existingIndexes)) {
            $db->query("ALTER TABLE files ADD INDEX idx_folder (folder)");
            echo "<p style='color: green;'>✅ Added index on folder</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Index idx_folder already exists</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Index creation warning: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h3>✅ Migration Complete!</h3>";
    
    if ($columnsAdded > 0) {
        echo "<p style='color: green;'><strong>Successfully added $columnsAdded missing columns!</strong></p>";
        echo "<p>The files table is now ready for use.</p>";
    } else {
        echo "<p style='color: blue;'>No columns needed to be added. Table structure is up to date.</p>";
    }
    
    // Show final table structure
    echo "<h3>📋 Updated Files Table Structure:</h3>";
    $updatedColumns = $db->fetchAll("DESCRIBE files");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($updatedColumns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='../'>← Back to App</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Migration Failed</h3>";
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    
    echo "<h4>🔧 Troubleshooting:</h4>";
    echo "<ul>";
    echo "<li>Check database connection settings</li>";
    echo "<li>Ensure database user has ALTER privileges</li>";
    echo "<li>Verify files table exists</li>";
    echo "</ul>";
}
?>
