<?php
/**
 * Quick Database Setup Script
 * Run this script to quickly set up your database
 */

// Enable error reporting for setup
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🗄️ Contact Sphere Organizer - Database Setup</h1>";

// Check if config file exists
if (!file_exists('config.php')) {
    echo "<p style='color: red;'>❌ config.php file not found. Please create it first.</p>";
    echo "<p>Copy the sample config and update with your database credentials.</p>";
    exit;
}

require_once 'config.php';

try {
    echo "<h2>🔗 Testing Database Connection...</h2>";
    
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Check if tables exist
    echo "<h2>📋 Checking Database Tables...</h2>";
    
    $tables = [
        'users', 'categories', 'contacts', 'contact_categories',
        'files', 'contact_files', 'reminders', 'whatsapp_messages',
        'message_attachments', 'message_templates', 'template_attachments',
        'activity_log', 'settings'
    ];
    
    $missingTables = [];
    
    foreach ($tables as $table) {
        $result = $db->fetchOne("SHOW TABLES LIKE :table", ['table' => $table]);
        if ($result) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "<h2>🚨 Missing Tables Detected</h2>";
        echo "<p>Please import the schema.sql file using phpMyAdmin or run the following command:</p>";
        echo "<pre>mysql -u " . DB_USER . " -p " . DB_NAME . " < schema.sql</pre>";
        echo "<p><a href='../DATABASE_SETUP_GUIDE.md' target='_blank'>📖 View Setup Guide</a></p>";
    } else {
        echo "<h2>✅ Database Setup Complete!</h2>";
        
        // Check default data
        $contactCount = $db->fetchOne("SELECT COUNT(*) as count FROM contacts")['count'];
        $categoryCount = $db->fetchOne("SELECT COUNT(*) as count FROM categories")['count'];
        
        echo "<p>📊 Database Statistics:</p>";
        echo "<ul>";
        echo "<li>Contacts: $contactCount</li>";
        echo "<li>Categories: $categoryCount</li>";
        echo "</ul>";
        
        // Test upload directory
        echo "<h2>📁 Testing Upload Directory...</h2>";
        if (is_dir(UPLOAD_DIR) && is_writable(UPLOAD_DIR)) {
            echo "<p style='color: green;'>✅ Upload directory is accessible</p>";
        } else {
            echo "<p style='color: red;'>❌ Upload directory issue</p>";
            echo "<p>Please create the uploads directory and set proper permissions:</p>";
            echo "<pre>mkdir -p " . UPLOAD_DIR . " && chmod 755 " . UPLOAD_DIR . "</pre>";
        }
        
        echo "<h2>🎉 Setup Complete!</h2>";
        echo "<p>Your database is ready to use. You can now:</p>";
        echo "<ul>";
        echo "<li>📱 Start your React frontend</li>";
        echo "<li>🔧 Configure your backend to use this database</li>";
        echo "<li>📤 Upload files and manage contacts</li>";
        echo "</ul>";
        
        echo "<p><strong>⚠️ Security Note:</strong> Delete this setup file after confirming everything works!</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Setup Failed</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    
    echo "<h3>🔧 Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li>Check your database credentials in config.php</li>";
    echo "<li>Ensure MySQL is running on your hosting</li>";
    echo "<li>Verify database name, username, and password</li>";
    echo "<li>Check if your hosting allows external database connections</li>";
    echo "</ol>";
    
    echo "<p><a href='../DATABASE_SETUP_GUIDE.md' target='_blank'>📖 View Detailed Setup Guide</a></p>";
}

// Display current configuration (without sensitive data)
echo "<h2>⚙️ Current Configuration</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>Database Host</td><td>" . DB_HOST . "</td></tr>";
echo "<tr><td>Database Name</td><td>" . DB_NAME . "</td></tr>";
echo "<tr><td>Database User</td><td>" . DB_USER . "</td></tr>";
echo "<tr><td>Charset</td><td>" . DB_CHARSET . "</td></tr>";
echo "<tr><td>Upload Directory</td><td>" . UPLOAD_DIR . "</td></tr>";
echo "<tr><td>Max File Size</td><td>" . formatFileSize(MAX_FILE_SIZE) . "</td></tr>";
echo "<tr><td>Timezone</td><td>" . TIMEZONE . "</td></tr>";
echo "</table>";

echo "<hr>";
echo "<p><small>Generated on: " . date('Y-m-d H:i:s') . "</small></p>";
?> 