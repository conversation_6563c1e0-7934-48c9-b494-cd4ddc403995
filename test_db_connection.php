<?php
/**
 * Database Connection Test Script
 * Upload this file to your server to test database connectivity
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration (same as in config.php)
$host = 'localhost';
$dbname = 'webwaaio_contact_sphere_organize';
$username = 'webwaaio_contact_sphere_organizer';
$password = '@Sadiqis007';
$charset = 'utf8mb4';

echo "<h2>Database Connection Test</h2>";
echo "<p><strong>Testing connection to:</strong></p>";
echo "<ul>";
echo "<li>Host: " . htmlspecialchars($host) . "</li>";
echo "<li>Database: " . htmlspecialchars($dbname) . "</li>";
echo "<li>Username: " . htmlspecialchars($username) . "</li>";
echo "<li>Password: " . (strlen($password) > 0 ? str_repeat('*', strlen($password)) : 'EMPTY') . "</li>";
echo "</ul>";

try {
    // Test basic connection
    echo "<h3>Step 1: Testing PDO Connection</h3>";

    // First, try connecting without specifying database
    echo "<p>Testing connection to MySQL server without database...</p>";
    $dsn_no_db = "mysql:host=$host;charset=$charset";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];

    try {
        $pdo_no_db = new PDO($dsn_no_db, $username, $password, $options);
        echo "<p style='color: green;'>✅ MySQL server connection successful!</p>";

        // Check if database exists
        $stmt = $pdo_no_db->query("SHOW DATABASES LIKE '$dbname'");
        $dbExists = $stmt->fetch();

        if ($dbExists) {
            echo "<p style='color: green;'>✅ Database '$dbname' exists!</p>";
        } else {
            echo "<p style='color: red;'>❌ Database '$dbname' does not exist!</p>";
            echo "<p><strong>Available databases:</strong></p>";
            $stmt = $pdo_no_db->query("SHOW DATABASES");
            $databases = $stmt->fetchAll();
            echo "<ul>";
            foreach ($databases as $db) {
                echo "<li>" . htmlspecialchars($db['Database']) . "</li>";
            }
            echo "</ul>";
        }

    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ MySQL server connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        throw $e; // Re-throw to skip database-specific test
    }

    // Now test with database
    echo "<p>Testing connection to specific database...</p>";
    $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test database selection
    echo "<h3>Step 2: Testing Database Access</h3>";
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ Current database: " . htmlspecialchars($result['current_db']) . "</p>";
    
    // Test table existence
    echo "<h3>Step 3: Checking Required Tables</h3>";
    $tables = ['contacts', 'files', 'categories', 'contact_files', 'contact_categories'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Count records
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
                $count = $stmt->fetch()['count'];
                echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Table '$table' does not exist</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error checking table '$table': " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // Test API endpoint
    echo "<h3>Step 4: Testing API Endpoint</h3>";
    $api_url = 'https://saamrajyam.com/api/contacts.php';
    echo "<p>Testing: <a href='$api_url' target='_blank'>$api_url</a></p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    if ($curl_error) {
        echo "<p style='color: red;'>❌ CURL Error: " . htmlspecialchars($curl_error) . "</p>";
    } else {
        echo "<p>HTTP Status: $http_code</p>";
        if ($http_code == 200) {
            echo "<p style='color: green;'>✅ API endpoint is accessible</p>";
            echo "<p><strong>Response:</strong></p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        } else {
            echo "<p style='color: red;'>❌ API endpoint returned HTTP $http_code</p>";
        }
    }
    
    echo "<h3>Step 5: Server Information</h3>";
    echo "<ul>";
    echo "<li>PHP Version: " . phpversion() . "</li>";
    echo "<li>PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "</li>";
    echo "<li>PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "</li>";
    echo "<li>Server Time: " . date('Y-m-d H:i:s') . "</li>";
    echo "<li>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
    echo "<li>Current Directory: " . __DIR__ . "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database Connection Failed!</p>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    
    // Common solutions
    echo "<h3>Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check if database name is correct</li>";
    echo "<li>Check if username and password are correct</li>";
    echo "<li>Check if database user has proper permissions</li>";
    echo "<li>Check if MySQL service is running</li>";
    echo "<li>Contact your hosting provider if the issue persists</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ General Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><em>Test completed at: " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p><strong>Note:</strong> Delete this file after testing for security reasons.</p>";
?>
