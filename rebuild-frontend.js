#!/usr/bin/env node

// Rebuild Frontend Script
// This script rebuilds the frontend with MySQL-only storage

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting frontend rebuild with MySQL-only storage...');

try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
    console.log('✅ Previous build cleaned');
  }

  // Run TypeScript compilation and build
  console.log('🔨 Building production version...');
  execSync('npm run build', { stdio: 'inherit' });

  // Check if build was successful
  if (fs.existsSync('dist')) {
    console.log('✅ Production build completed successfully!');
    console.log('📁 Build output is in the "dist" directory');
    
    // List build contents
    const distContents = fs.readdirSync('dist');
    console.log('\n📂 Build contents:');
    distContents.forEach(item => {
      const stats = fs.statSync(path.join('dist', item));
      const size = stats.isFile() ? `(${Math.round(stats.size / 1024)}KB)` : '(dir)';
      console.log(`  - ${item} ${size}`);
    });

    console.log('\n📋 Build Summary:');
    console.log('- Frontend: React + TypeScript + Vite');
    console.log('- Storage: MySQL Database Only (https://webwaa.io/contact-sphere-organizer)');
    console.log('- No local storage fallbacks');
    console.log('- Ready for deployment');
    console.log('\n🎉 Production build ready for deployment!');
  } else {
    console.error('❌ Build failed - dist directory not created');
    process.exit(1);
  }

} catch (error) {
  console.error('❌ Build failed with error:', error.message);
  process.exit(1);
}
