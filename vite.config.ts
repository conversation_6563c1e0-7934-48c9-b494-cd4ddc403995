import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Base path for deployment (root of domain)
  base: '/',
  
  // Build configuration for shared hosting
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Disable sourcemaps for production
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['lucide-react', '@radix-ui/react-dialog'],
        },
      },
    },
  },
  
  // Server configuration for development
  server: {
    port: 5173,
    host: true, // Allow external connections for testing
    cors: true,
  },
  
  // Preview configuration
  preview: {
    port: 4173,
    host: true,
  },
  
  // Environment variables
  envPrefix: 'VITE_',
  
  // Ensure proper handling of SPA routing
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
  },
});
