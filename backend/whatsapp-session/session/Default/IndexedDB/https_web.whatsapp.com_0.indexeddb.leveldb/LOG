2025/07/05-10:19:20.532 29303 Creating DB /Users/<USER>/Library/CloudStorage/OneDrive-Personal/Desktop/Working Folder/D Drive/Git Hub/Contact_and_whatsapp/contact-sphere-organizer-Uploaded /backend/whatsapp-session/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/07/05-10:19:20.598 29303 Reusing MANIFEST /Users/<USER>/Library/CloudStorage/OneDrive-Personal/Desktop/Working Folder/D Drive/Git Hub/Contact_and_whatsapp/contact-sphere-organizer-Uploaded /backend/whatsapp-session/session/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/07/05-10:19:30.687 29803 Level-0 table #5: started
2025/07/05-10:19:30.724 29803 Level-0 table #5: 17077 bytes OK
2025/07/05-10:19:30.780 29803 Delete type=0 #3
2025/07/05-10:19:30.781 29803 Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:30.818 29803 Level-0 table #7: started
2025/07/05-10:19:30.836 29803 Level-0 table #7: 878 bytes OK
2025/07/05-10:19:30.891 29803 Delete type=0 #4
2025/07/05-10:19:30.910 29803 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:30.910 29803 Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 1079 : 0
2025/07/05-10:19:30.911 29803 Compacting 1@1 + 1@2 files
2025/07/05-10:19:30.949 29803 Generated table #8@1: 314 keys, 8399 bytes
2025/07/05-10:19:30.949 29803 Compacted 1@1 + 1@2 files => 8399 bytes
2025/07/05-10:19:31.007 29803 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.008 29803 Delete type=2 #7
2025/07/05-10:19:31.026 ad03 Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 1079 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.027 29803 Level-0 table #10: started
2025/07/05-10:19:31.046 29803 Level-0 table #10: 972 bytes OK
2025/07/05-10:19:31.101 29803 Delete type=0 #6
2025/07/05-10:19:31.101 29803 Delete type=2 #5
2025/07/05-10:19:31.101 29803 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.101 29803 Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x05\x00\x00\x05' @ 1126 : 0
2025/07/05-10:19:31.101 29803 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.120 29803 Generated table #11@1: 323 keys, 8371 bytes
2025/07/05-10:19:31.120 29803 Compacted 1@1 + 1@2 files => 8371 bytes
2025/07/05-10:19:31.157 29803 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.157 29803 Delete type=2 #10
2025/07/05-10:19:31.158 29803 Manual compaction at level-1 from '\x00\x05\x00\x00\x05' @ 1126 : 0 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.158 29803 Level-0 table #13: started
2025/07/05-10:19:31.196 29803 Level-0 table #13: 6046 bytes OK
2025/07/05-10:19:31.235 29803 Delete type=2 #8
2025/07/05-10:19:31.235 29803 Delete type=0 #9
2025/07/05-10:19:31.254 29803 Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.254 ad03 Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x06\x00\x00\x05' @ 1343 : 0
2025/07/05-10:19:31.254 ad03 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.273 ad03 Generated table #14@1: 510 keys, 13588 bytes
2025/07/05-10:19:31.273 ad03 Compacted 1@1 + 1@2 files => 13588 bytes
2025/07/05-10:19:31.310 ad03 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.310 ad03 Delete type=2 #13
2025/07/05-10:19:31.311 ad03 Manual compaction at level-1 from '\x00\x06\x00\x00\x05' @ 1343 : 0 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.330 22503 Level-0 table #16: started
2025/07/05-10:19:31.348 22503 Level-0 table #16: 1782 bytes OK
2025/07/05-10:19:31.386 22503 Delete type=0 #12
2025/07/05-10:19:31.386 22503 Delete type=2 #11
2025/07/05-10:19:31.405 22503 Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.405 22503 Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0f\x00\x00\x05' @ 1426 : 1
2025/07/05-10:19:31.405 22503 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.425 22503 Generated table #17@1: 571 keys, 15063 bytes
2025/07/05-10:19:31.425 22503 Compacted 1@1 + 1@2 files => 15063 bytes
2025/07/05-10:19:31.481 22503 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.481 22503 Delete type=2 #16
2025/07/05-10:19:31.482 22503 Manual compaction at level-1 from '\x00\x0f\x00\x00\x05' @ 1426 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.482 22503 Level-0 table #19: started
2025/07/05-10:19:31.501 22503 Level-0 table #19: 2822 bytes OK
2025/07/05-10:19:31.539 22503 Delete type=0 #15
2025/07/05-10:19:31.539 22503 Delete type=2 #14
2025/07/05-10:19:31.540 22503 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.540 22503 Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1600 : 1
2025/07/05-10:19:31.540 22503 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.560 22503 Generated table #20@1: 725 keys, 16592 bytes
2025/07/05-10:19:31.560 22503 Compacted 1@1 + 1@2 files => 16592 bytes
2025/07/05-10:19:31.599 22503 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.599 22503 Delete type=2 #19
2025/07/05-10:19:31.599 22503 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1600 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.600 22503 Level-0 table #22: started
2025/07/05-10:19:31.620 22503 Level-0 table #22: 249 bytes OK
2025/07/05-10:19:31.659 22503 Delete type=2 #17
2025/07/05-10:19:31.659 22503 Delete type=0 #18
2025/07/05-10:19:31.660 22503 Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.660 22303 Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 1620 : 0
2025/07/05-10:19:31.660 22303 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.679 22303 Generated table #23@1: 719 keys, 16518 bytes
2025/07/05-10:19:31.680 22303 Compacted 1@1 + 1@2 files => 16518 bytes
2025/07/05-10:19:31.716 22303 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.717 22303 Delete type=2 #22
2025/07/05-10:19:31.717 22303 Manual compaction at level-1 from '\x00\x09\x00\x00\x05' @ 1620 : 0 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.717 22303 Level-0 table #25: started
2025/07/05-10:19:31.738 22303 Level-0 table #25: 249 bytes OK
2025/07/05-10:19:31.792 22303 Delete type=2 #20
2025/07/05-10:19:31.792 22303 Delete type=0 #21
2025/07/05-10:19:31.793 22303 Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.793 22303 Manual compaction at level-1 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 1626 : 0
2025/07/05-10:19:31.793 22303 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.813 22303 Generated table #26@1: 713 keys, 16435 bytes
2025/07/05-10:19:31.813 22303 Compacted 1@1 + 1@2 files => 16435 bytes
2025/07/05-10:19:31.851 22303 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.851 22303 Delete type=2 #25
2025/07/05-10:19:31.852 22303 Manual compaction at level-1 from '\x00\x0d\x00\x00\x05' @ 1626 : 0 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.852 22303 Level-0 table #28: started
2025/07/05-10:19:31.872 22303 Level-0 table #28: 249 bytes OK
2025/07/05-10:19:31.908 22303 Delete type=2 #23
2025/07/05-10:19:31.908 22303 Delete type=0 #24
2025/07/05-10:19:31.909 22303 Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.909 22303 Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 1632 : 0
2025/07/05-10:19:31.909 22303 Compacting 1@1 + 1@2 files
2025/07/05-10:19:31.929 22303 Generated table #29@1: 707 keys, 16468 bytes
2025/07/05-10:19:31.929 22303 Compacted 1@1 + 1@2 files => 16468 bytes
2025/07/05-10:19:31.967 22303 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:31.967 22303 Delete type=2 #28
2025/07/05-10:19:31.967 22303 Manual compaction at level-1 from '\x00\x0b\x00\x00\x05' @ 1632 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:31.968 29603 Level-0 table #31: started
2025/07/05-10:19:31.989 29603 Level-0 table #31: 249 bytes OK
2025/07/05-10:19:32.029 29603 Delete type=2 #26
2025/07/05-10:19:32.029 29603 Delete type=0 #27
2025/07/05-10:19:32.029 ad03 Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:32.029 ad03 Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1638 : 0
2025/07/05-10:19:32.029 ad03 Compacting 1@1 + 1@2 files
2025/07/05-10:19:32.049 ad03 Generated table #32@1: 701 keys, 16274 bytes
2025/07/05-10:19:32.049 ad03 Compacted 1@1 + 1@2 files => 16274 bytes
2025/07/05-10:19:32.087 ad03 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:32.088 ad03 Delete type=2 #31
2025/07/05-10:19:32.088 ad03 Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1638 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:32.088 22703 Level-0 table #34: started
2025/07/05-10:19:32.106 22703 Level-0 table #34: 249 bytes OK
2025/07/05-10:19:32.145 22703 Delete type=2 #29
2025/07/05-10:19:32.145 22703 Delete type=0 #30
2025/07/05-10:19:32.146 22703 Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:32.146 22703 Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1644 : 0
2025/07/05-10:19:32.146 22703 Compacting 1@1 + 1@2 files
2025/07/05-10:19:32.166 22703 Generated table #35@1: 695 keys, 16138 bytes
2025/07/05-10:19:32.166 22703 Compacted 1@1 + 1@2 files => 16138 bytes
2025/07/05-10:19:32.203 22703 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:32.204 22703 Delete type=2 #34
2025/07/05-10:19:32.204 22703 Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1644 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:32.205 2a003 Level-0 table #37: started
2025/07/05-10:19:32.224 2a003 Level-0 table #37: 2075 bytes OK
2025/07/05-10:19:32.262 2a003 Delete type=2 #32
2025/07/05-10:19:32.262 2a003 Delete type=0 #33
2025/07/05-10:19:32.263 2a003 Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/05-10:19:32.263 2a003 Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1875 : 0
2025/07/05-10:19:32.263 2a003 Compacting 1@1 + 1@2 files
2025/07/05-10:19:32.281 2a003 Generated table #38@1: 464 keys, 12495 bytes
2025/07/05-10:19:32.281 2a003 Compacted 1@1 + 1@2 files => 12495 bytes
2025/07/05-10:19:32.318 2a003 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/05-10:19:32.318 2a003 Delete type=2 #37
2025/07/05-10:19:32.319 2a003 Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1875 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
