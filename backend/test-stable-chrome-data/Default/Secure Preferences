{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/137.0.7151.122/Resources/web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "/Applications/Google Chrome.app/Contents/Frameworks/Google Chrome Framework.framework/Versions/137.0.7151.122/Resources/pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "0F25021495862975FB4C031FCEB82E8005A201BFB655C92FBD3A31DB4FBD12C3"}, "default_search_provider_data": {"template_url_data": "3AB046C147F332A7684602DE8AC6CEF7F926A3165105AB7CC1BEB92448BB56FF"}, "enterprise_signin": {"policy_recovery_token": "BE29E2E92D55A54831F6E325E23F19BF37E357A3E60ADF364B8F06EE1C85B655"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "E032C0BF232754788BBB912060074AF2B03CB6BD09EB718222DE379EB5EFC34E", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "DB08109F67E15803A95932FA87911F99F8139CD8A35EAB4275DC363217D78943"}, "ui": {"developer_mode": "BA5B06F8983C4B926806BAC6BCFCD3B910679DA5E42159EC4C062399AF538618"}}, "google": {"services": {"account_id": "B6A2643A077AF8FD4938A456E2F3B8CF15F21143E88BE3E82E8052A2FF758204", "last_signed_in_username": "B8B6CB71D104F271ACF344F82B4F805ADBD866B0DBBDA3E3E7B4FE84E6A8AB99", "last_username": "E40707FCDFA986E4763F50AD59002E92643307FEE842F37490996EAC36F2508F"}}, "homepage": "D14852F8306E9AED16110FE52F02E955577C8B5776393F74917793B2BACFA30A", "homepage_is_newtabpage": "EE611967880D4DC60DF11A8B37EE665976D8C4A4862B8964168A9FB22E224355", "media": {"storage_id_salt": "919921CFBED7B71C8959FC60316FADC7FD95EFEF7D93ECBB91B5AF261053FD27"}, "pinned_tabs": "EB85988E0C012DE8032158D25ECCC6899132FD41D0D8ECB86FE661470E3ECF65", "prefs": {"preference_reset_time": "6DD01E7D70DBA8329AA39725B71253216F7D973A704A671C47F1001375792945"}, "safebrowsing": {"incidents_sent": "F4D3CB003CF0087C3F96AEC0FA6EAFE56FF02CA3940BADDFFB3D58E809125712"}, "search_provider_overrides": "35B696FCF087CEA4BB12499EA8A289E14F759C8E86BE6194C44EC6344A50ABB8", "session": {"restore_on_startup": "16933C40BECA87010A25420F073E65A273628E4191C61FAB58016CF10D1737A9", "startup_urls": "7CA3ACF13C859525590262086423436CCB24446CC96BEE3EC660FCA28001B98C"}}, "super_mac": "ED44F42DC5F2B152C63373B601767201F6EE1BE513F8CBB6D96CF446BCF1D7A9"}}