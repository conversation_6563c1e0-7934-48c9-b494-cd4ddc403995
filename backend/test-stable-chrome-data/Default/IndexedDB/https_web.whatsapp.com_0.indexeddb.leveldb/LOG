2025/06/30-14:56:16.951 8ab0b Creating DB /Users/<USER>/Library/CloudStorage/OneDrive-Personal/Desktop/Working Folder/D Drive/Git Hub/Contact_and_whatsapp/contact-sphere-organizer-main/backend/test-stable-chrome-data/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb since it was missing.
2025/06/30-14:56:16.953 8ab0b Reusing MANIFEST /Users/<USER>/Library/CloudStorage/OneDrive-Personal/Desktop/Working Folder/D Drive/Git Hub/Contact_and_whatsapp/contact-sphere-organizer-main/backend/test-stable-chrome-data/Default/IndexedDB/https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/30-14:56:17.660 8ab1f Level-0 table #5: started
2025/06/30-14:56:17.667 8ab1f Level-0 table #5: 17431 bytes OK
2025/06/30-14:56:17.669 8ab1f Delete type=0 #3
2025/06/30-14:56:17.671 8ab1e Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.671 8ab0b Level-0 table #7: started
2025/06/30-14:56:17.673 8ab0b Level-0 table #7: 1049 bytes OK
2025/06/30-14:56:17.676 8ab0b Delete type=0 #4
2025/06/30-14:56:17.677 8ab0b Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.677 8ab0b Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\x05' @ 1087 : 0
2025/06/30-14:56:17.678 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.681 8ab0b Generated table #8@1: 318 keys, 8596 bytes
2025/06/30-14:56:17.681 8ab0b Compacted 1@1 + 1@2 files => 8596 bytes
2025/06/30-14:56:17.683 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.683 8ab0b Delete type=2 #7
2025/06/30-14:56:17.684 8ab0b Manual compaction at level-1 from '\x00\x04\x00\x00\x05' @ 1087 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.685 8ab0b Level-0 table #10: started
2025/06/30-14:56:17.686 8ab0b Level-0 table #10: 886 bytes OK
2025/06/30-14:56:17.688 8ab0b Delete type=0 #6
2025/06/30-14:56:17.688 8ab0b Delete type=2 #5
2025/06/30-14:56:17.689 8ab0b Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.689 8ab0b Manual compaction at level-1 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x05\x00\x00\x05' @ 1126 : 0
2025/06/30-14:56:17.689 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.692 8ab0b Generated table #11@1: 323 keys, 8648 bytes
2025/06/30-14:56:17.692 8ab0b Compacted 1@1 + 1@2 files => 8648 bytes
2025/06/30-14:56:17.694 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.695 8ab0b Delete type=2 #10
2025/06/30-14:56:17.695 8ab0b Manual compaction at level-1 from '\x00\x05\x00\x00\x05' @ 1126 : 0 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.696 8ab0d Level-0 table #13: started
2025/06/30-14:56:17.697 8ab0d Level-0 table #13: 251 bytes OK
2025/06/30-14:56:17.701 8ab0d Delete type=2 #8
2025/06/30-14:56:17.701 8ab0d Delete type=0 #9
2025/06/30-14:56:17.702 8ab1f Manual compaction at level-0 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.702 8ab0b Manual compaction at level-1 from '\x00\x06\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at '\x00\x06\x00\x00\x05' @ 1132 : 0
2025/06/30-14:56:17.702 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.704 8ab0b Generated table #14@1: 317 keys, 8559 bytes
2025/06/30-14:56:17.705 8ab0b Compacted 1@1 + 1@2 files => 8559 bytes
2025/06/30-14:56:17.706 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.707 8ab0b Delete type=2 #13
2025/06/30-14:56:17.708 8ab0b Manual compaction at level-1 from '\x00\x06\x00\x00\x05' @ 1132 : 0 .. '\x00\x07\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.709 8ab0d Level-0 table #16: started
2025/06/30-14:56:17.710 8ab0d Level-0 table #16: 1589 bytes OK
2025/06/30-14:56:17.714 8ab0d Delete type=0 #12
2025/06/30-14:56:17.715 8ab0d Delete type=2 #11
2025/06/30-14:56:17.715 8ab0d Manual compaction at level-0 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.716 8ab0d Manual compaction at level-1 from '\x00\x07\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1196 : 1
2025/06/30-14:56:17.716 8ab0d Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.719 8ab0d Generated table #17@1: 369 keys, 9595 bytes
2025/06/30-14:56:17.719 8ab0d Compacted 1@1 + 1@2 files => 9595 bytes
2025/06/30-14:56:17.721 8ab0d compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.722 8ab0d Delete type=2 #16
2025/06/30-14:56:17.722 8ab1f Manual compaction at level-1 from '\x00\x0e\x02\x02\x03\x00\x00\x00\x00\x00\x00\xf0?' @ 1196 : 1 .. '\x00\x08\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.724 8ab0d Level-0 table #19: started
2025/06/30-14:56:17.726 8ab0d Level-0 table #19: 2880 bytes OK
2025/06/30-14:56:17.728 8ab0d Delete type=0 #15
2025/06/30-14:56:17.728 8ab0d Delete type=2 #14
2025/06/30-14:56:17.729 8ab0d Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.729 8ab0d Manual compaction at level-1 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1378 : 1
2025/06/30-14:56:17.729 8ab0d Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.732 8ab0d Generated table #20@1: 526 keys, 12066 bytes
2025/06/30-14:56:17.732 8ab0d Compacted 1@1 + 1@2 files => 12066 bytes
2025/06/30-14:56:17.736 8ab0d compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.736 8ab0d Delete type=2 #19
2025/06/30-14:56:17.737 8ab0b Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1378 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.738 8ab0b Level-0 table #22: started
2025/06/30-14:56:17.739 8ab0b Level-0 table #22: 251 bytes OK
2025/06/30-14:56:17.742 8ab0b Delete type=2 #17
2025/06/30-14:56:17.742 8ab0b Delete type=0 #18
2025/06/30-14:56:17.743 8ab0b Manual compaction at level-0 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.743 8ab0b Manual compaction at level-1 from '\x00\x09\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at '\x00\x09\x00\x00\x05' @ 1398 : 0
2025/06/30-14:56:17.743 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.746 8ab0b Generated table #23@1: 520 keys, 11921 bytes
2025/06/30-14:56:17.746 8ab0b Compacted 1@1 + 1@2 files => 11921 bytes
2025/06/30-14:56:17.747 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.747 8ab0b Delete type=2 #22
2025/06/30-14:56:17.748 8ab0b Manual compaction at level-1 from '\x00\x09\x00\x00\x05' @ 1398 : 0 .. '\x00\x0a\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.748 8ab0b Level-0 table #25: started
2025/06/30-14:56:17.749 8ab0b Level-0 table #25: 249 bytes OK
2025/06/30-14:56:17.750 8ab0b Delete type=2 #20
2025/06/30-14:56:17.750 8ab0b Delete type=0 #21
2025/06/30-14:56:17.751 8ab0b Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.751 8ab0b Manual compaction at level-1 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0a\x00\x00\x05' @ 1404 : 0
2025/06/30-14:56:17.751 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.753 8ab0b Generated table #26@1: 514 keys, 11703 bytes
2025/06/30-14:56:17.753 8ab0b Compacted 1@1 + 1@2 files => 11703 bytes
2025/06/30-14:56:17.754 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.754 8ab0b Delete type=2 #25
2025/06/30-14:56:17.754 8ab0b Manual compaction at level-1 from '\x00\x0a\x00\x00\x05' @ 1404 : 0 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.755 8ab0b Level-0 table #28: started
2025/06/30-14:56:17.756 8ab0b Level-0 table #28: 251 bytes OK
2025/06/30-14:56:17.758 8ab0b Delete type=2 #23
2025/06/30-14:56:17.758 8ab0b Delete type=0 #24
2025/06/30-14:56:17.759 8ab0b Manual compaction at level-0 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.759 8ab0b Manual compaction at level-1 from '\x00\x0b\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0b\x00\x00\x05' @ 1410 : 0
2025/06/30-14:56:17.759 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.762 8ab0b Generated table #29@1: 508 keys, 11631 bytes
2025/06/30-14:56:17.763 8ab0b Compacted 1@1 + 1@2 files => 11631 bytes
2025/06/30-14:56:17.764 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.765 8ab0b Delete type=2 #28
2025/06/30-14:56:17.766 8ab0b Manual compaction at level-1 from '\x00\x0b\x00\x00\x05' @ 1410 : 0 .. '\x00\x0c\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.767 8ab0b Level-0 table #31: started
2025/06/30-14:56:17.768 8ab0b Level-0 table #31: 251 bytes OK
2025/06/30-14:56:17.770 8ab0b Delete type=2 #26
2025/06/30-14:56:17.770 8ab0b Delete type=0 #27
2025/06/30-14:56:17.771 8ab0b Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.771 8ab0b Manual compaction at level-1 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0c\x00\x00\x05' @ 1416 : 0
2025/06/30-14:56:17.771 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.773 8ab0b Generated table #32@1: 502 keys, 11528 bytes
2025/06/30-14:56:17.773 8ab0b Compacted 1@1 + 1@2 files => 11528 bytes
2025/06/30-14:56:17.774 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.774 8ab0b Delete type=2 #31
2025/06/30-14:56:17.774 8ab0b Manual compaction at level-1 from '\x00\x0c\x00\x00\x05' @ 1416 : 0 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.775 8ab0b Level-0 table #34: started
2025/06/30-14:56:17.776 8ab0b Level-0 table #34: 249 bytes OK
2025/06/30-14:56:17.777 8ab0b Delete type=2 #29
2025/06/30-14:56:17.777 8ab0b Delete type=0 #30
2025/06/30-14:56:17.778 8ab0b Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.778 8ab0b Manual compaction at level-1 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at '\x00\x0d\x00\x00\x05' @ 1422 : 0
2025/06/30-14:56:17.778 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.780 8ab0b Generated table #35@1: 496 keys, 11425 bytes
2025/06/30-14:56:17.780 8ab0b Compacted 1@1 + 1@2 files => 11425 bytes
2025/06/30-14:56:17.781 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.782 8ab0b Delete type=2 #34
2025/06/30-14:56:17.783 8ab0b Manual compaction at level-1 from '\x00\x0d\x00\x00\x05' @ 1422 : 0 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.784 8ab0b Level-0 table #37: started
2025/06/30-14:56:17.786 8ab0b Level-0 table #37: 1191 bytes OK
2025/06/30-14:56:17.788 8ab0b Delete type=2 #32
2025/06/30-14:56:17.788 8ab0b Delete type=0 #33
2025/06/30-14:56:17.789 8ab0b Manual compaction at level-0 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/30-14:56:17.789 8ab0b Manual compaction at level-1 from '\x00\x10\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at '\x00\x10\x00\x00\x05' @ 1542 : 0
2025/06/30-14:56:17.789 8ab0b Compacting 1@1 + 1@2 files
2025/06/30-14:56:17.793 8ab0b Generated table #38@1: 376 keys, 9680 bytes
2025/06/30-14:56:17.793 8ab0b Compacted 1@1 + 1@2 files => 9680 bytes
2025/06/30-14:56:17.794 8ab0b compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/30-14:56:17.795 8ab0b Delete type=2 #37
2025/06/30-14:56:17.796 8ab0b Manual compaction at level-1 from '\x00\x10\x00\x00\x05' @ 1542 : 0 .. '\x00\x11\x00\x00\x00' @ 0 : 0; will stop at (end)
