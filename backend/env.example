# Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration (Your Shared Hosting MySQL)
DB_HOST=localhost
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=your_database_name
DB_PORT=3306

# Server Configuration
PORT=3001
NODE_ENV=production

# CORS Configuration (Your shared hosting domain)
FRONTEND_URL=https://yourdomain.com

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=./whatsapp-session

# Microlink.io API (for link previews)
MICROLINK_API_KEY=your_microlink_api_key_optional

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# Security
JWT_SECRET=your_jwt_secret_here_change_this

# Example values:
# DB_HOST=sql123.your-hosting-provider.com
# DB_USER=username_contacts
# DB_PASSWORD=your_strong_password
# DB_NAME=username_contactsdb
# FRONTEND_URL=https://yoursite.com

# Chromium Path (optional - for custom Chromium installation)
# CHROMIUM_PATH=/usr/bin/chromium-browser

# Logging Level
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILES=10

# CORS Settings
CORS_ORIGIN=* 