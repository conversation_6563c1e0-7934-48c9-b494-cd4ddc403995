Contact Sphere Organizer - Backend (Windows)
=============================================

This folder contains the backend server for WhatsApp and contact management.

# 1. Prerequisites
- Node.js (https://nodejs.org/) installed on your PC
- Google Chrome or Chromium installed (for WhatsApp automation)

# 2. How to Run

1. Open Command Prompt (cmd)
2. Navigate to this backend folder. Example:
   cd C:\path\to\your\backend

3. Install dependencies (first time only):
   npm install

4. Start the backend:
   node server-minimal.js

Or just double-click the provided `start-backend-windows.bat` file (see below).

# 3. Configuration
- By default, the backend uses the local SQLite database in `data/contacts.db`.
- To use a remote MySQL database, update the config in your backend code as needed.

# 4. WhatsApp Connection
- When you start the backend, a Chrome window will open for WhatsApp Web.
- Scan the QR code with your phone to connect.

# 5. Stopping the Backend
- Press Ctrl+C in the Command Prompt window.

# 6. Troubleshooting
- If Chrome doesn't open, make sure it's installed and accessible in your PATH.
- If you see errors about missing modules, run `npm install` again.
- For more help, contact your developer or support.

# 7. Quick Start (Batch File)
- Use the `start-backend-windows.bat` file in the main project folder to start the backend with a double-click. 