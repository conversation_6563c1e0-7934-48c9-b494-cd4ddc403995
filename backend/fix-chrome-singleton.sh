#!/bin/bash

echo "🔧 Chrome Singleton Lock Fix Script"
echo "==================================="

# Function to safely remove Chrome singleton locks
remove_chrome_locks() {
    local chrome_data_dir="$1"
    echo "🧹 Cleaning Chrome data directory: $chrome_data_dir"
    
    # Remove singleton lock files
    find "$chrome_data_dir" -name "SingletonLock" -type f -delete 2>/dev/null
    find "$chrome_data_dir" -name "SingletonSocket" -type s -delete 2>/dev/null
    find "$chrome_data_dir" -name "SingletonSocket*" -type s -delete 2>/dev/null
    
    # Remove other potential lock files
    find "$chrome_data_dir" -name "*.lock" -type f -delete 2>/dev/null
    find "$chrome_data_dir" -name "lockfile" -type f -delete 2>/dev/null
    
    echo "✅ Removed singleton locks from $chrome_data_dir"
}

# Kill all Chrome processes
echo "🔄 Stopping all Chrome processes..."
pkill -f "Google Chrome" 2>/dev/null || echo "No Google Chrome processes found"
pkill -f "Chromium" 2>/dev/null || echo "No Chromium processes found"
pkill -f "chrome-mac" 2>/dev/null || echo "No chrome-mac processes found"
pkill -f "puppeteer" 2>/dev/null || echo "No puppeteer processes found"

# Wait for processes to terminate
echo "⏳ Waiting for processes to terminate..."
sleep 3

# Clean Chrome user data directories
echo "🗑️ Cleaning Chrome user data directories..."

# Main chrome-user-data directory
if [ -d "chrome-user-data" ]; then
    remove_chrome_locks "chrome-user-data"
fi

# Test chrome-user-data directory
if [ -d "test-chrome-user-data" ]; then
    remove_chrome_locks "test-chrome-user-data"
fi

# System Chrome user data (be careful with this)
echo "🔍 Checking system Chrome user data..."
SYSTEM_CHROME_DATA="$HOME/Library/Application Support/Google/Chrome"
if [ -d "$SYSTEM_CHROME_DATA" ]; then
    echo "⚠️ Found system Chrome data at: $SYSTEM_CHROME_DATA"
    echo "💡 If you're still having issues, you can manually clear this directory"
    echo "   (This will log you out of Chrome but may fix the singleton issue)"
fi

# Clean WhatsApp session data
echo "🗑️ Cleaning WhatsApp session data..."
rm -rf whatsapp-session 2>/dev/null || echo "No WhatsApp session data to clear"
rm -rf test-whatsapp-session 2>/dev/null || echo "No test WhatsApp session data to clear"
rm -rf .wwebjs_cache 2>/dev/null || echo "No cache to clear"

# Wait for cleanup
sleep 2

# Check if any Chrome processes are still running
echo "🔍 Checking for remaining Chrome processes..."
REMAINING_PROCESSES=$(pgrep -f "Google Chrome\|Chromium\|chrome-mac" 2>/dev/null | wc -l)
if [ "$REMAINING_PROCESSES" -gt 0 ]; then
    echo "⚠️ Found $REMAINING_PROCESSES remaining Chrome processes"
    echo "🔄 Force killing remaining processes..."
    pkill -9 -f "Google Chrome" 2>/dev/null
    pkill -9 -f "Chromium" 2>/dev/null
    pkill -9 -f "chrome-mac" 2>/dev/null
    sleep 2
else
    echo "✅ No remaining Chrome processes found"
fi

# Verify Chrome installation
echo "🔍 Verifying Chrome installation..."
if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
    echo "✅ Google Chrome found"
    CHROME_VERSION=$(/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version 2>/dev/null | head -1)
    echo "📋 Chrome version: $CHROME_VERSION"
elif [ -f "/Applications/Chromium.app/Contents/MacOS/Chromium" ]; then
    echo "✅ Chromium found"
    CHROME_VERSION=$(/Applications/Chromium.app/Contents/MacOS/Chromium --version 2>/dev/null | head -1)
    echo "📋 Chromium version: $CHROME_VERSION"
else
    echo "❌ Chrome not found in standard locations"
    echo "💡 Please install Google Chrome from: https://www.google.com/chrome/"
    exit 1
fi

echo ""
echo "🎉 Chrome singleton lock cleanup completed!"
echo ""
echo "💡 Next steps:"
echo "1. Run your WhatsApp backend server"
echo "2. If issues persist, try restarting your computer"
echo "3. Make sure Chrome is updated to the latest version"
echo ""
echo "🚀 Ready to start WhatsApp backend!" 