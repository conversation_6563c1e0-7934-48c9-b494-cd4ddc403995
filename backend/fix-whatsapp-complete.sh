#!/bin/bash

echo "🔧 Complete WhatsApp Backend Fix Script"
echo "========================================"

# Kill existing processes
echo "🧹 Stopping existing processes..."
pkill -f "node server-minimal.js" 2>/dev/null || echo "No existing Node.js processes found"
pkill -f "Google Chrome" 2>/dev/null || echo "No existing Chrome processes found"
pkill -f "Chromium" 2>/dev/null || echo "No existing Chromium processes found"
pkill -f "chrome-mac" 2>/dev/null || echo "No existing Chrome-mac processes found"

# Wait for processes to fully terminate
echo "⏳ Waiting for processes to terminate..."
sleep 5

# Clean session data
echo "🗑️ Clearing WhatsApp session data..."
rm -rf whatsapp-session 2>/dev/null || echo "No session data to clear"
rm -rf chrome-user-data 2>/dev/null || echo "No chrome user data to clear"
rm -rf .wwebjs_cache 2>/dev/null || echo "No cache to clear"

# Wait for cleanup
sleep 3

# Check Chrome installation
echo "🔍 Checking Chrome installation..."
if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
    echo "✅ Google Chrome found"
    CHROME_VERSION=$(/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version 2>/dev/null | head -1)
    echo "📋 Chrome version: $CHROME_VERSION"
elif [ -f "/Applications/Chromium.app/Contents/MacOS/Chromium" ]; then
    echo "✅ Chromium found"
    CHROME_VERSION=$(/Applications/Chromium.app/Contents/MacOS/Chromium --version 2>/dev/null | head -1)
    echo "📋 Chromium version: $CHROME_VERSION"
else
    echo "⚠️ Chrome not found in standard locations"
    echo "💡 Please install Google Chrome from: https://www.google.com/chrome/"
    echo "💡 Or install Chromium from: https://www.chromium.org/getting-involved/download-chromium/"
    exit 1
fi

# Check Node.js installation
echo "🔍 Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js found: $NODE_VERSION"
else
    echo "❌ Node.js not found"
    echo "💡 Please install Node.js from: https://nodejs.org/"
    exit 1
fi

# Check npm dependencies
echo "📦 Checking npm dependencies..."
if [ -f "package.json" ]; then
    echo "✅ package.json found"
    if [ -d "node_modules" ]; then
        echo "✅ node_modules found"
    else
        echo "📦 Installing dependencies..."
        npm install
    fi
else
    echo "❌ package.json not found"
    exit 1
fi

# Check available memory
echo "💾 Checking system resources..."
TOTAL_MEM=$(sysctl -n hw.memsize | awk '{print $0/1024/1024/1024 " GB"}')
echo "📊 Total memory: $TOTAL_MEM"

# Check available disk space
DISK_SPACE=$(df -h . | tail -1 | awk '{print $4}')
echo "💿 Available disk space: $DISK_SPACE"

echo ""
echo "🚀 Starting WhatsApp backend with improved configuration..."
echo "💡 The server will now use enhanced Chrome stability settings"
echo "💡 If you still experience issues, try:"
echo "   1. Restart your computer"
echo "   2. Update Chrome to the latest version"
echo "   3. Check if any antivirus is blocking Chrome"
echo ""

# Start server
node server-minimal.js 