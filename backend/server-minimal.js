const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const QRCode = require('qrcode');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5173', 'https://saamrajyam.com', 'http://*************:5173'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// WhatsApp client setup
let client = null;
let qrCodeData = null;
let isInitializing = false;
let connectionStatus = {
  isConnected: false,
  qrCode: null,
  error: null,
  phoneNumber: null,
  profileName: null,
  deviceInfo: null,
  lastSeen: null,
  serverTime: new Date().toISOString(),
  initializationInProgress: false
};

// Initialize WhatsApp client
function initializeWhatsAppClient() {
  if (client) {
    client.destroy();
  }

  client = new Client({
    authStrategy: new LocalAuth({
      dataPath: path.join(__dirname, 'whatsapp-session')
    }),
    puppeteer: {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    }
  });

  // Event handlers
  client.on('qr', (qr) => {
    console.log('📱 QR Code received');
    QRCode.toDataURL(qr, (err, url) => {
      if (!err) {
        qrCodeData = url;
        connectionStatus.qrCode = url;
        connectionStatus.error = null;
        console.log('✅ QR Code generated successfully');
      } else {
        console.error('❌ Error generating QR code:', err);
        connectionStatus.error = 'Failed to generate QR code';
      }
    });
  });

  client.on('ready', async () => {
    console.log('✅ WhatsApp client is ready!');
    connectionStatus.isConnected = true;
    connectionStatus.qrCode = null;
    connectionStatus.error = null;
    connectionStatus.initializationInProgress = false;

    try {
      const info = client.info;
      connectionStatus.phoneNumber = info.wid.user;
      connectionStatus.profileName = info.pushname;
      connectionStatus.deviceInfo = {
        phoneNumber: info.wid.user,
        profileName: info.pushname,
        platform: info.platform,
        deviceType: 'web'
      };
    } catch (error) {
      console.error('❌ Error getting client info:', error);
    }
  });

  client.on('authenticated', () => {
    console.log('✅ WhatsApp client authenticated');
    connectionStatus.error = null;
  });

  client.on('auth_failure', (msg) => {
    console.error('❌ WhatsApp authentication failed:', msg);
    connectionStatus.error = 'Authentication failed';
    connectionStatus.isConnected = false;
    connectionStatus.initializationInProgress = false;
  });

  client.on('disconnected', (reason) => {
    console.log('📱 WhatsApp client disconnected:', reason);
    connectionStatus.isConnected = false;
    connectionStatus.qrCode = null;
    connectionStatus.phoneNumber = null;
    connectionStatus.profileName = null;
    connectionStatus.deviceInfo = null;
    connectionStatus.initializationInProgress = false;
  });

  return client;
}

// API Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'WhatsApp Backend'
  });
});

// Get WhatsApp status
app.get('/api/status', (req, res) => {
  connectionStatus.serverTime = new Date().toISOString();
  res.json(connectionStatus);
});

// Connect to WhatsApp
app.post('/api/connect', async (req, res) => {
  try {
    if (isInitializing) {
      return res.json({
        success: false,
        message: 'WhatsApp initialization already in progress'
      });
    }

    if (connectionStatus.isConnected) {
      return res.json({
        success: true,
        message: 'WhatsApp is already connected',
        status: connectionStatus
      });
    }

    console.log('🔄 Initializing WhatsApp connection...');
    isInitializing = true;
    connectionStatus.initializationInProgress = true;
    connectionStatus.error = null;
    qrCodeData = null;

    initializeWhatsAppClient();

    // Initialize the client
    await client.initialize();

    res.json({
      success: true,
      message: 'WhatsApp initialization started'
    });

  } catch (error) {
    console.error('❌ Error connecting to WhatsApp:', error);
    isInitializing = false;
    connectionStatus.initializationInProgress = false;
    connectionStatus.error = error.message;

    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Disconnect from WhatsApp
app.post('/api/disconnect', async (req, res) => {
  try {
    if (client) {
      await client.destroy();
      client = null;
    }

    connectionStatus.isConnected = false;
    connectionStatus.qrCode = null;
    connectionStatus.phoneNumber = null;
    connectionStatus.profileName = null;
    connectionStatus.deviceInfo = null;
    connectionStatus.error = null;
    connectionStatus.initializationInProgress = false;
    isInitializing = false;
    qrCodeData = null;

    res.json({
      success: true,
      message: 'WhatsApp disconnected successfully'
    });
  } catch (error) {
    console.error('❌ Error disconnecting from WhatsApp:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Send single message
app.post('/api/send-message', async (req, res) => {
  try {
    if (!client || !connectionStatus.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'WhatsApp not connected'
      });
    }

    const { phone, text, urls, attachments } = req.body;

    if (!phone || !text) {
      return res.status(400).json({
        success: false,
        error: 'Phone number and text are required'
      });
    }

    // Format phone number
    const formattedPhone = phone.replace(/\D/g, '');
    const chatId = `${formattedPhone}@c.us`;

    // Prepare message text with URLs
    let messageText = text;
    if (urls && urls.length > 0) {
      messageText += '\n\n' + urls.join('\n');
    }

    // Send text message
    const message = await client.sendMessage(chatId, messageText);

    // Send attachments if any
    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        try {
          const media = MessageMedia.fromFilePath(attachment.path);
          await client.sendMessage(chatId, media, {
            caption: attachment.caption || ''
          });
        } catch (attachError) {
          console.error('❌ Error sending attachment:', attachError);
        }
      }
    }

    res.json({
      success: true,
      messageId: message.id._serialized,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error sending message:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Send bulk messages
app.post('/api/send-bulk-messages', async (req, res) => {
  try {
    if (!client || !connectionStatus.isConnected) {
      return res.status(400).json({
        success: false,
        error: 'WhatsApp not connected'
      });
    }

    const { contacts, text, urls, attachments } = req.body;

    if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Contacts array is required'
      });
    }

    if (!text) {
      return res.status(400).json({
        success: false,
        error: 'Message text is required'
      });
    }

    const results = [];

    for (const contact of contacts) {
      try {
        // Format phone number
        const formattedPhone = contact.phone.replace(/\D/g, '');
        const chatId = `${formattedPhone}@c.us`;

        // Prepare message text with URLs
        let messageText = text;
        if (urls && urls.length > 0) {
          messageText += '\n\n' + urls.join('\n');
        }

        // Send text message
        const message = await client.sendMessage(chatId, messageText);

        // Send attachments if any
        if (attachments && attachments.length > 0) {
          for (const attachment of attachments) {
            try {
              const media = MessageMedia.fromFilePath(attachment.path);
              await client.sendMessage(chatId, media, {
                caption: attachment.caption || ''
              });
            } catch (attachError) {
              console.error('❌ Error sending attachment to', contact.name, ':', attachError);
            }
          }
        }

        results.push({
          contact: contact.name,
          phone: contact.phone,
          success: true,
          messageId: message.id._serialized,
          timestamp: new Date().toISOString()
        });

        // Add delay between messages
        await new Promise(resolve => setTimeout(resolve, 3000));

      } catch (error) {
        console.error('❌ Error sending message to', contact.name, ':', error);
        results.push({
          contact: contact.name,
          phone: contact.phone,
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    res.json({
      success: true,
      results: results,
      totalSent: results.filter(r => r.success).length,
      totalFailed: results.filter(r => !r.success).length
    });

  } catch (error) {
    console.error('❌ Error sending bulk messages:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// File upload endpoint
app.post('/api/upload', upload.array('files'), (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      path: file.path,
      size: file.size,
      mimetype: file.mimetype,
      url: `/uploads/${file.filename}`
    }));

    res.json({
      success: true,
      files: uploadedFiles
    });

  } catch (error) {
    console.error('❌ Error uploading files:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Serve uploaded files
app.use('/uploads', express.static(uploadsDir));

// Clear WhatsApp session
app.post('/api/clear-session', async (req, res) => {
  try {
    // Disconnect client first
    if (client) {
      await client.destroy();
      client = null;
    }

    // Clear session directory
    const sessionPath = path.join(__dirname, 'whatsapp-session');
    if (fs.existsSync(sessionPath)) {
      fs.rmSync(sessionPath, { recursive: true, force: true });
    }

    // Reset connection status
    connectionStatus = {
      isConnected: false,
      qrCode: null,
      error: null,
      phoneNumber: null,
      profileName: null,
      deviceInfo: null,
      lastSeen: null,
      serverTime: new Date().toISOString(),
      initializationInProgress: false
    };

    isInitializing = false;
    qrCodeData = null;

    res.json({
      success: true,
      message: 'WhatsApp session cleared successfully'
    });

  } catch (error) {
    console.error('❌ Error clearing session:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 WhatsApp Backend Server started successfully!');
  console.log(`📡 Server running on: http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://0.0.0.0:${PORT}`);
  console.log('📱 WhatsApp Web integration ready');
  console.log('⏰ Server started at:', new Date().toISOString());
  console.log('');
  console.log('🔗 Available endpoints:');
  console.log(`   GET  /api/health - Health check`);
  console.log(`   GET  /api/status - WhatsApp status`);
  console.log(`   POST /api/connect - Connect to WhatsApp`);
  console.log(`   POST /api/disconnect - Disconnect from WhatsApp`);
  console.log(`   POST /api/send-message - Send single message`);
  console.log(`   POST /api/send-bulk-messages - Send bulk messages`);
  console.log(`   POST /api/upload - Upload files`);
  console.log(`   POST /api/clear-session - Clear WhatsApp session`);
  console.log('');
  console.log('✅ Ready to accept connections!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down WhatsApp Backend Server...');

  if (client) {
    try {
      await client.destroy();
      console.log('✅ WhatsApp client disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting WhatsApp client:', error);
    }
  }

  console.log('👋 Server stopped');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');

  if (client) {
    try {
      await client.destroy();
      console.log('✅ WhatsApp client disconnected');
    } catch (error) {
      console.error('❌ Error disconnecting WhatsApp client:', error);
    }
  }

  console.log('👋 Server stopped');
  process.exit(0);
});