const express = require('express');
const cors = require('cors');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration
const corsOptions = {
  origin: process.env.FRONTEND_URL || '*',
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Serve uploaded files statically
app.use('/uploads', express.static(uploadsDir));

// Store uploaded files in memory (for simple implementation)
let uploadedFiles = [];

// Store contacts in memory (for server-side storage)
let contacts = [];
let categories = [];
let settings = {};

// Load existing data from files if they exist
try {
  const contactsFile = path.join(__dirname, 'data', 'contacts.json');
  if (fs.existsSync(contactsFile)) {
    contacts = JSON.parse(fs.readFileSync(contactsFile, 'utf8'));
    console.log('📋 Loaded', contacts.length, 'contacts from server storage');
  }
} catch (error) {
  console.log('⚠️ Could not load contacts from file:', error.message);
}

try {
  const categoriesFile = path.join(__dirname, 'data', 'categories.json');
  if (fs.existsSync(categoriesFile)) {
    categories = JSON.parse(fs.readFileSync(categoriesFile, 'utf8'));
    console.log('📂 Loaded', categories.length, 'categories from server storage');
  }
} catch (error) {
  console.log('⚠️ Could not load categories from file:', error.message);
}

try {
  const settingsFile = path.join(__dirname, 'data', 'settings.json');
  if (fs.existsSync(settingsFile)) {
    settings = JSON.parse(fs.readFileSync(settingsFile, 'utf8'));
    console.log('⚙️ Loaded settings from server storage');
  }
} catch (error) {
  console.log('⚠️ Could not load settings from file:', error.message);
}

// Helper function to save data to files
function saveDataToFile(data, filename) {
  try {
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    const filePath = path.join(dataDir, filename);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving data to file:', error);
    return false;
  }
}

// WhatsApp client - ONLY for WhatsApp connection
let whatsappClient = null;
let qrCodeData = null;
let clientStatus = 'disconnected';
let initializationInProgress = false;

// Initialize WhatsApp client with improved error handling
function initializeWhatsAppClient() {
  try {
    if (initializationInProgress) {
      console.log('⚠️ WhatsApp initialization already in progress...');
      return;
    }

    initializationInProgress = true;
    console.log('🔄 Initializing WhatsApp client...');

    // Destroy existing client if any
    if (whatsappClient) {
      try {
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        // Check if browser exists before destroying
        if (whatsappClient.pupBrowser) {
          whatsappClient.destroy();
        } else {
          whatsappClient = null;
        }
      } catch (destroyError) {
        console.log('🧹 Cleaned up previous client instance');
        whatsappClient = null;
      }
    }

    // Reset status
    clientStatus = 'disconnected';
    qrCodeData = null;

    // Cross-platform Puppeteer configuration for Mac and Windows
    const puppeteerConfig = {
      headless: false,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-backgrounding-occluded-windows',
        '--disable-background-timer-throttling',
        '--disable-renderer-backgrounding',
        '--disable-ipc-flooding-protection',
        '--disable-features=VizDisplayCompositor',
        '--disable-background-networking',
        '--disable-background-downloads',
        '--disable-default-apps',
        '--disable-sync',
        '--disable-translate',
        '--disable-extensions',
        '--no-default-browser-check',
        '--no-first-run',
        '--no-pings',
        '--user-data-dir=./chrome-user-data',
        '--window-size=1366,768',
        // Essential flags to prevent browser from closing due to inactivity
        '--disable-add-to-shelf',
        '--disable-client-side-phishing-detection',
        '--disable-datasaver-prompt',
        '--disable-domain-reliability',
        '--disable-features=TranslateUI',
        '--disable-hang-monitor',
        '--disable-prompt-on-repost',
        '--disable-speech-api',
        '--disable-tab-for-desktop-share',
        '--disable-voice-input',
        '--disable-wake-on-wifi',
        '--enable-async-dns',
        '--enable-simple-cache-backend',
        '--enable-tcp-fast-open',
        '--media-cache-size=33554432',
        '--memory-pressure-off',
        '--max_old_space_size=4096',
        '--disable-blink-features=AutomationControlled',
        '--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer',
        '--run-all-compositor-stages-before-draw',
        '--disable-threaded-animation',
        '--disable-threaded-scrolling',
        '--disable-checker-imaging',
        '--disable-new-content-rendering-timeout',
        '--disable-image-animation-resync',
        '--disable-partial-raster',
        '--use-gl=swiftshader',
        '--disable-software-rasterizer',
        '--disable-accelerated-2d-canvas',
        '--no-zygote'
      ],
      timeout: 0, // No timeout
      protocolTimeout: 0, // No protocol timeout
      ignoreDefaultArgs: ['--disable-extensions'],
      handleSIGINT: false,
      handleSIGTERM: false,
      handleSIGHUP: false,
      slowMo: 100 // Add small delay for stability
    };

    // Cross-platform Chrome path detection
    const os = require('os');
    const platform = os.platform();
    
    let chromePath = null;
    
    if (platform === 'darwin') {
      // macOS
      const macPaths = [
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/Applications/Chromium.app/Contents/MacOS/Chromium',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser'
      ];
      
      for (const path of macPaths) {
        if (fs.existsSync(path)) {
          chromePath = path;
          console.log(`🔍 Found Chrome on macOS at: ${chromePath}`);
          break;
        }
      }
    } else if (platform === 'win32') {
      // Windows
      const winPaths = [
        'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe',
        'C:\\Program Files\\Chromium\\Application\\chrome.exe',
        'C:\\Program Files (x86)\\Chromium\\Application\\chrome.exe'
      ];
      
      for (const path of winPaths) {
        if (fs.existsSync(path)) {
          chromePath = path;
          console.log(`🔍 Found Chrome on Windows at: ${chromePath}`);
          break;
        }
      }
    } else {
      // Linux
      const linuxPaths = [
        '/usr/bin/google-chrome-stable',
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser',
        '/usr/bin/chromium',
        '/snap/bin/chromium'
      ];
      
      for (const path of linuxPaths) {
        if (fs.existsSync(path)) {
          chromePath = path;
          console.log(`🔍 Found Chrome on Linux at: ${chromePath}`);
          break;
        }
      }
    }

    if (chromePath) {
      puppeteerConfig.executablePath = chromePath;
      console.log(`✅ Using Chrome at: ${chromePath}`);
    } else {
      console.log('⚠️ Chrome not found in common paths, using system default');
      console.log(`🌐 Platform detected: ${platform}`);
    }

    // Create WhatsApp client with error handling
    try {
      whatsappClient = new Client({
        authStrategy: new LocalAuth({
          dataPath: './whatsapp-session'
        }),
        puppeteer: puppeteerConfig
      });
    } catch (error) {
      console.error('❌ Failed to create WhatsApp client:', error.message);
      initializationInProgress = false;
      throw error;
    }

    // Enhanced event handlers with better error handling
    whatsappClient.on('qr', (qr) => {
      console.log('\n' + '📱'.repeat(50));
      console.log('📱 WHATSAPP QR CODE READY!');
      console.log('📱'.repeat(50));
      console.log('🔗 A Chrome window should now be open with WhatsApp Web');
      console.log('📱 Scan the QR code in the browser window with your phone');
      console.log('📱 Once scanned, you can close the browser window');
      console.log('📱'.repeat(50));
      qrCodeData = qr;
      clientStatus = 'qr';
      initializationInProgress = false;
    });

    whatsappClient.on('ready', () => {
      const networkIPs = getNetworkIPs();
      console.log('\n' + '🎉'.repeat(35));
      console.log('✅ WHATSAPP CLIENT IS READY!');
      console.log('🎉'.repeat(35));
      console.log('📱 WhatsApp is now connected and ready to send messages!');
      console.log('🔗 You can now close the Chrome browser window');
      console.log('📱 The connection will remain active in the background');
      
      // Log connection info for debugging
      try {
        const info = whatsappClient.info;
        console.log('📋 Connection Debug Info:');
        console.log('- Client Info:', JSON.stringify(info, null, 2));
        console.log('- WID:', info?.wid);
        console.log('- Me:', info?.me);
        console.log('- Push Name:', info?.pushname);
        console.log('- Platform:', info?.platform);
      } catch (error) {
        console.log('⚠️ Could not log connection info:', error.message);
      }
      
      console.log('\n🔗 FRONTEND CONNECTION URLs (copy to your app):');
      console.log(`🏠 Local:   http://localhost:${PORT}`);
      if (networkIPs.length > 0) {
        console.log(`🌐 Network: http://${networkIPs[0]}:${PORT}`);
      }
      console.log('🎉'.repeat(35) + '\n');
      
      clientStatus = 'ready';
      qrCodeData = null;
      initializationInProgress = false;
      
      // Keep-alive mechanism to prevent browser from closing due to inactivity
      if (whatsappClient && whatsappClient.pupBrowser) {
        console.log('🔒 Setting up keep-alive mechanism to prevent browser closure...');
        
        // Keep the browser alive by periodically checking its status
        const keepAliveInterval = setInterval(async () => {
          try {
            if (whatsappClient && whatsappClient.pupBrowser) {
              // Check if browser is still connected
              const pages = await whatsappClient.pupBrowser.pages();
              if (pages.length === 0) {
                console.log('⚠️ No pages found, browser may be closing...');
                clearInterval(keepAliveInterval);
                return;
              }
              
              // Keep the connection alive by checking page title
              const mainPage = pages[0];
              if (mainPage && !mainPage.isClosed()) {
                await mainPage.title(); // This keeps the connection active
              }
            } else {
              console.log('🔒 WhatsApp client or browser no longer available, stopping keep-alive');
              clearInterval(keepAliveInterval);
            }
          } catch (error) {
            console.log('⚠️ Keep-alive check failed:', error.message);
            // Don't clear interval on minor errors, only on browser closure
            if (error.message.includes('Target closed') || error.message.includes('Session closed')) {
              console.log('🔒 Browser closed, stopping keep-alive mechanism');
              clearInterval(keepAliveInterval);
            }
          }
        }, 30000); // Check every 30 seconds
        
        // Store the interval reference for cleanup
        whatsappClient.keepAliveInterval = keepAliveInterval;
        
        console.log('✅ Keep-alive mechanism activated - browser will stay open');
      }
    });

    whatsappClient.on('authenticated', () => {
      console.log('🔐 WhatsApp client authenticated successfully');
      clientStatus = 'authenticated';
    });

    whatsappClient.on('disconnected', (reason) => {
      console.log(`❌ WhatsApp client disconnected: ${reason}`);
      console.log('💡 Restart the server to reconnect');
      
      // Clean up keep-alive interval
      if (whatsappClient && whatsappClient.keepAliveInterval) {
        clearInterval(whatsappClient.keepAliveInterval);
        console.log('🔒 Keep-alive interval cleared');
      }
      
      clientStatus = 'disconnected';
      qrCodeData = null;
      initializationInProgress = false;
    });

    whatsappClient.on('auth_failure', (msg) => {
      console.error('❌ Authentication failed:', msg);
      clientStatus = 'auth_failure';
      initializationInProgress = false;
    });

    // Enhanced error handler with retry mechanism
    whatsappClient.on('error', (error) => {
      console.error('❌ WhatsApp client error:', error.message);
      clientStatus = 'error';
      initializationInProgress = false;
      
      // Handle specific Chrome/Protocol errors
      if (error.message.includes('Target closed') || 
          error.message.includes('Protocol error') ||
          error.message.includes('Session closed') ||
          error.message.includes('Network.setUserAgentOverride') ||
          error.message.includes('Navigation failed') ||
          error.message.includes('Page crashed') ||
          error.message.includes('Connection closed') ||
          error.message.includes('Browser closed')) {
        console.log('🛑 Chrome/Puppeteer stability issue detected.');
        console.log('💡 Solutions to try:');
        console.log('1. rm -rf backend/whatsapp-session');
        console.log('2. rm -rf backend/chrome-user-data');
        console.log('3. pkill -f "Google Chrome"');
        console.log('4. Restart the server');
        console.log('5. Check Chrome installation');
        
        // Clean up the client
        if (whatsappClient) {
          try {
            // Clean up keep-alive interval
            if (whatsappClient.keepAliveInterval) {
              clearInterval(whatsappClient.keepAliveInterval);
              console.log('🔒 Keep-alive interval cleared');
            }
            whatsappClient.destroy();
          } catch (e) {
            console.log('🧹 Cleanup during error');
          }
          whatsappClient = null;
        }
        return; // Don't auto-retry for these errors
      }
      
      // Only restart for other types of errors
      setTimeout(() => {
        console.log('🔄 Attempting to restart WhatsApp client...');
        initializeWhatsAppClient();
      }, 15000); // Longer delay for better stability
    });

    // Longer initialization timeout
    const initTimeout = setTimeout(() => {
      if (initializationInProgress) {
        console.log('⏰ WhatsApp initialization timeout - stopping retry loop');
        initializationInProgress = false;
        clientStatus = 'timeout';
        if (whatsappClient) {
          try {
            // Clean up keep-alive interval
            if (whatsappClient.keepAliveInterval) {
              clearInterval(whatsappClient.keepAliveInterval);
              console.log('🔒 Keep-alive interval cleared');
            }
            whatsappClient.destroy();
          } catch (e) {
            console.log('🧹 Cleanup during timeout');
          }
        }
        console.log('💡 Manual restart required. Try clearing the session folder.');
      }
    }, 60000); // 60 second timeout

    whatsappClient.initialize().then(() => {
      clearTimeout(initTimeout);
      console.log('✅ WhatsApp client initialization started successfully');
      console.log('🔗 Chrome browser window should open shortly...');
      console.log('📱 Look for the WhatsApp Web interface in the browser');
    }).catch((error) => {
      clearTimeout(initTimeout);
      console.error('❌ Failed to initialize WhatsApp client:', error.message);
      clientStatus = 'error';
      initializationInProgress = false;
      
      if (error.message.includes('Target closed') || 
          error.message.includes('Protocol error') ||
          error.message.includes('Session closed') ||
          error.message.includes('Network.setUserAgentOverride') ||
          error.message.includes('Navigation failed') ||
          error.message.includes('Page crashed') ||
          error.message.includes('Connection closed') ||
          error.message.includes('Browser closed') ||
          error.message.includes('Launch failed') ||
          error.message.includes('Timeout')) {
        console.log('🛑 Chrome stability issue. Try these solutions:');
        console.log('1. rm -rf backend/whatsapp-session');
        console.log('2. rm -rf backend/chrome-user-data');
        console.log('3. pkill -f "Google Chrome"');
        console.log('4. Restart the server');
        console.log('5. Check Chrome installation');
        
        // Clean up the client
        if (whatsappClient) {
          try {
            // Clean up keep-alive interval
            if (whatsappClient.keepAliveInterval) {
              clearInterval(whatsappClient.keepAliveInterval);
              console.log('🔒 Keep-alive interval cleared');
            }
            whatsappClient.destroy();
          } catch (e) {
            console.log('🧹 Cleanup during initialization error');
          }
          whatsappClient = null;
        }
      }
    });

  } catch (error) {
    console.error('❌ Error in initializeWhatsAppClient:', error.message);
    clientStatus = 'error';
    initializationInProgress = false;
  }
}

// API Routes - ONLY WhatsApp related

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'WhatsApp Connection Only',
    timestamp: new Date().toISOString() 
  });
});

// Connect endpoint (for frontend compatibility)
app.post('/api/connect', (req, res) => {
  try {
    console.log('🔄 Manual connect request received...');
    
    // Reset initialization flag to allow new connection
    initializationInProgress = false;
    
    if (whatsappClient) {
      try {
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        // Check if browser exists before destroying
        if (whatsappClient.pupBrowser) {
          whatsappClient.destroy();
        } else {
          whatsappClient = null;
        }
      } catch (destroyError) {
        console.log('🧹 Cleaned up previous client instance');
        whatsappClient = null;
      }
    }
    
    // Wait a moment before starting new initialization
    setTimeout(() => {
      initializeWhatsAppClient();
    }, 1000);
    
    res.json({ 
      success: true, 
      message: 'WhatsApp client initialization started' 
    });
  } catch (error) {
    console.error('Error initializing WhatsApp:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to initialize WhatsApp client' 
    });
  }
});

// Status endpoint (for frontend compatibility)
app.get('/api/status', async (req, res) => {
  try {
    let qrCodeImage = null;
    let connectionInfo = null;
    
    if (qrCodeData) {
      qrCodeImage = await qrcode.toDataURL(qrCodeData);
    }
    
    // Get connection info when ready
    if (clientStatus === 'ready' && whatsappClient) {
      try {
        // Try multiple methods to get user info
        const info = whatsappClient.info;
        
        let phoneNumber = 'Connected';
        let profileName = 'WhatsApp User';
        
        console.log('🔍 Debugging WhatsApp Info:', {
          hasInfo: !!info,
          infoKeys: info ? Object.keys(info) : [],
          wid: info?.wid,
          me: info?.me,
          pushname: info?.pushname,
          platform: info?.platform
        });
        
        // Try to get phone number from various sources
        if (info?.wid?.user) {
          phoneNumber = '+' + info.wid.user;
          console.log('📞 Phone from wid.user:', phoneNumber);
        } else if (info?.me?.user) {
          phoneNumber = '+' + info.me.user;
          console.log('📞 Phone from me.user:', phoneNumber);
        } else if (info?.wid?._serialized) {
          const serialized = info.wid._serialized;
          if (serialized.includes('@')) {
            phoneNumber = '+' + serialized.split('@')[0];
            console.log('📞 Phone from wid._serialized:', phoneNumber);
          }
        }
        
        // Try to get profile name from various sources
        if (info?.pushname) {
          profileName = info.pushname;
          console.log('👤 Name from pushname:', profileName);
        } else if (info?.me?.name) {
          profileName = info.me.name;
          console.log('👤 Name from me.name:', profileName);
        } else if (info?.me?.pushname) {
          profileName = info.me.pushname;
          console.log('👤 Name from me.pushname:', profileName);
        }
        
        // Try alternative method to get contact info
        try {
          const contact = await whatsappClient.getContactById(info?.wid?._serialized || info?.me?._serialized);
          if (contact) {
            if (contact.pushname) {
              profileName = contact.pushname;
              console.log('👤 Name from contact.pushname:', profileName);
            }
            if (contact.number) {
              phoneNumber = '+' + contact.number;
              console.log('📞 Phone from contact.number:', phoneNumber);
            }
          }
        } catch (contactError) {
          console.log('⚠️ Could not get contact info:', contactError.message);
        }
        
        connectionInfo = {
          phoneNumber: phoneNumber,
          profileName: profileName,
          platform: info?.platform || 'WhatsApp Web',
          deviceType: 'Mobile Device',
          businessName: info?.business?.name || null,
          verifiedName: info?.verifiedName || null,
          status: 'Connected'
        };
        
        console.log('📱 Final Connection Info:', {
          phone: phoneNumber,
          name: profileName,
          platform: info?.platform
        });
        
      } catch (error) {
        console.log('Could not get client info:', error.message);
        
        // Fallback - try to get basic info without detailed properties
        try {
          const basicInfo = whatsappClient.info || {};
          connectionInfo = {
            phoneNumber: basicInfo.wid?.user ? '+' + basicInfo.wid.user : 'Connected',
            profileName: basicInfo.pushname || 'WhatsApp User',
            platform: 'WhatsApp Web',
            deviceType: 'Mobile Device',
            businessName: null,
            verifiedName: null,
            status: 'Connected'
          };
        } catch (fallbackError) {
          // Last resort fallback
          connectionInfo = {
            phoneNumber: 'Connected',
            profileName: 'WhatsApp User',
            platform: 'WhatsApp Web',
            deviceType: 'Mobile Device',
            businessName: null,
            verifiedName: null,
            status: 'Connected'
          };
        }
      }
    }
    
    res.json({
      connected: clientStatus === 'ready',
      status: clientStatus,
      qrCode: qrCodeImage,
      connectionInfo: connectionInfo
    });
  } catch (error) {
    console.error('Error getting status:', error);
    res.status(500).json({ 
      connected: false, 
      error: 'Failed to get status' 
    });
  }
});

// Disconnect endpoint (for frontend compatibility)
app.post('/api/disconnect', async (req, res) => {
  try {
    if (whatsappClient) {
      try {
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        // Check if browser exists before destroying
        if (whatsappClient.pupBrowser) {
          await whatsappClient.destroy();
        }
      } catch (destroyError) {
        console.log('🧹 Cleaned up previous client instance');
      }
      whatsappClient = null;
    }
    clientStatus = 'disconnected';
    qrCodeData = null;
    
    res.json({ 
      success: true, 
      message: 'WhatsApp disconnected successfully' 
    });
  } catch (error) {
    console.error('Error disconnecting:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to disconnect' 
    });
  }
});

// Enhanced WhatsApp status endpoint with detailed connection info
app.get('/api/whatsapp/status', async (req, res) => {
  try {
    console.log('📊 WhatsApp status request received');
    console.log('📊 Current clientStatus:', clientStatus);
    console.log('📊 WhatsApp client exists:', !!whatsappClient);
    
    let qrCodeImage = null;
    let connectionInfo = null;
    let lastSeen = null;
    
    if (qrCodeData) {
      qrCodeImage = await qrcode.toDataURL(qrCodeData);
      console.log('📊 QR code generated');
    }
    
    // Get connection info when ready
    if (clientStatus === 'ready' && whatsappClient) {
      console.log('📊 Client is ready, getting connection info...');
      try {
        const info = whatsappClient.info;
        console.log('📊 Client info retrieved:', !!info);
        
        let phoneNumber = 'Connected';
        let profileName = 'WhatsApp User';
        
        // Try to get phone number from various sources
        if (info?.wid?.user) {
          phoneNumber = '+' + info.wid.user;
        } else if (info?.me?.user) {
          phoneNumber = '+' + info.me.user;
        } else if (info?.wid?._serialized) {
          const serialized = info.wid._serialized;
          if (serialized.includes('@')) {
            phoneNumber = '+' + serialized.split('@')[0];
          }
        }
        
        // Try to get profile name from various sources
        if (info?.pushname) {
          profileName = info.pushname;
        } else if (info?.me?.name) {
          profileName = info.me.name;
        } else if (info?.me?.pushname) {
          profileName = info.me.pushname;
        }
        
        connectionInfo = {
          phoneNumber: phoneNumber,
          profileName: profileName,
          platform: info?.platform || 'WhatsApp Web',
          deviceType: 'Mobile Device',
          businessName: info?.business?.name || null,
          verifiedName: info?.verifiedName || null,
          status: 'Connected',
          lastSeen: new Date().toISOString()
        };
        
        console.log('📊 Connection info created:', connectionInfo);
        
      } catch (error) {
        console.log('Could not get detailed client info:', error.message);
        connectionInfo = {
          phoneNumber: 'Connected',
          profileName: 'WhatsApp User',
          platform: 'WhatsApp Web',
          deviceType: 'Mobile Device',
          businessName: null,
          verifiedName: null,
          status: 'Connected',
          lastSeen: new Date().toISOString()
        };
      }
    } else {
      console.log('📊 Client not ready or not available');
    }
    
    const response = {
      status: clientStatus,
      qrCode: qrCodeImage,
      connectionInfo: connectionInfo,
      lastSeen: lastSeen,
      serverTime: new Date().toISOString(),
      initializationInProgress: initializationInProgress
    };
    
    console.log('📊 Sending response:', response);
    
    res.json(response);
  } catch (error) {
    console.error('Error getting WhatsApp status:', error);
    res.status(500).json({ 
      status: 'error', 
      error: error.message 
    });
  }
});

// Force disconnect endpoint
app.post('/api/whatsapp/disconnect', async (req, res) => {
  try {
    console.log('🔄 Manual disconnect requested...');
    
    if (whatsappClient) {
      try {
        if (clientStatus === 'ready' || clientStatus === 'authenticated') {
          console.log('🔐 Logging out from WhatsApp...');
          await whatsappClient.logout();
        }
        
        console.log('🧹 Destroying WhatsApp client...');
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        await whatsappClient.destroy();
        whatsappClient = null;
        clientStatus = 'disconnected';
        qrCodeData = null;
        initializationInProgress = false;
        
        console.log('✅ WhatsApp client disconnected successfully');
        res.json({ 
          success: true, 
          message: 'WhatsApp client disconnected successfully' 
        });
      } catch (error) {
        console.log('⚠️ Error during manual disconnect:', error.message);
        // Force cleanup
        whatsappClient = null;
        clientStatus = 'disconnected';
        qrCodeData = null;
        initializationInProgress = false;
        
        res.json({ 
          success: true, 
          message: 'WhatsApp client force disconnected' 
        });
      }
    } else {
      res.json({ 
        success: true, 
        message: 'WhatsApp client was not connected' 
      });
    }
  } catch (error) {
    console.error('Error during manual disconnect:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Reconnect endpoint
app.post('/api/whatsapp/reconnect', async (req, res) => {
  try {
    console.log('🔄 Manual reconnect requested...');
    
    // Disconnect first if connected
    if (whatsappClient) {
      try {
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        await whatsappClient.destroy();
      } catch (error) {
        console.log('⚠️ Error destroying existing client:', error.message);
      }
      whatsappClient = null;
      clientStatus = 'disconnected';
      qrCodeData = null;
      initializationInProgress = false;
    }
    
    // Wait a moment before reconnecting
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Initialize new client
    initializeWhatsAppClient();
    
    res.json({ 
      success: true, 
      message: 'WhatsApp client reconnection started' 
    });
  } catch (error) {
    console.error('Error during manual reconnect:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Send message endpoint
app.post('/api/send-message', async (req, res) => {
  try {
    const { phone, message, attachments = [], urls = [] } = req.body;
    
    if (!whatsappClient) {
      return res.status(503).json({ 
        success: false, 
        error: 'WhatsApp client not initialized' 
      });
    }

    if (clientStatus !== 'ready') {
      return res.status(503).json({ 
        success: false, 
        error: 'WhatsApp client not ready' 
      });
    }

    console.log('📤 Sending message to:', phone);
    console.log('📎 Attachments:', attachments.length);
    console.log('🔗 URLs:', urls.length);

    // Format phone number
    const formattedPhone = phone.replace(/[^\d]/g, '') + '@c.us';
    console.log('📱 Original phone:', phone);
    console.log('📱 Formatted phone:', formattedPhone);
    
    // Validate phone number format
    if (!formattedPhone.match(/^\d+@c\.us$/)) {
      console.error('❌ Invalid phone number format:', formattedPhone);
      return res.status(400).json({ 
        success: false, 
        error: 'Invalid phone number format' 
      });
    }
    
    let finalMessage = message;
    
    // Add URLs to message if provided
    if (urls && urls.length > 0) {
      finalMessage += '\n\n';
      urls.forEach(url => {
        // Handle both string URLs and URL objects
        if (typeof url === 'string') {
          finalMessage += `🔗 ${url}\n`;
        } else if (url.url) {
          finalMessage += `🔗 ${url.title || url.url}: ${url.url}\n`;
        }
      });
    }

    // Send the text message first (if there's text content)
    if (finalMessage && finalMessage.trim()) {
      await whatsappClient.sendMessage(formattedPhone, finalMessage);
      console.log('✅ Text message sent successfully');
    }

    // Send attachments if provided
    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        try {
          console.log('📎 Processing attachment:', attachment.name, 'Type:', attachment.type);
          
          let media = null;
          let mediaType = null;
          
          // Determine WhatsApp media type based on file type
          const fileType = attachment.type.toLowerCase();
          const fileName = attachment.name.toLowerCase();
          
          if (fileType.startsWith('image/') || fileName.match(/\\.(jpg|jpeg|png|gif|webp|bmp|svg)$/)) {
            mediaType = 'image';
          } else if (fileType.startsWith('video/') || fileName.match(/\\.(mp4|avi|mov|wmv|flv|webm|mkv|3gp)$/)) {
            mediaType = 'video';
          } else if (fileType.startsWith('audio/') || fileName.match(/\\.(mp3|wav|ogg|m4a|aac|flac|wma)$/)) {
            mediaType = 'audio';
          } else if (fileType.includes('pdf') || fileName.match(/\\.(pdf)$/)) {
            mediaType = 'document';
          } else if (fileType.includes('document') || fileName.match(/\\.(doc|docx|xls|xlsx|ppt|pptx|txt|rtf|odt|ods|odp)$/)) {
            mediaType = 'document';
          } else if (fileName.match(/\\.(zip|rar|7z|tar|gz|bz2)$/)) {
            mediaType = 'document';
          } else {
            mediaType = 'document'; // Default to document for unknown types
          }
          
          console.log('📋 Detected media type:', mediaType);
          
          // Handle different URL types
          if (attachment.url && attachment.url.startsWith('blob:')) {
            console.log('⚠️ Blob URL detected, cannot send directly. Need server file URL.');
            continue;
          } else if (attachment.url && attachment.url.startsWith('http')) {
            // HTTP URL - try to download and send
            console.log('🌐 Processing HTTP URL attachment');
            try {
              media = await MessageMedia.fromUrl(attachment.url);
            } catch (urlError) {
              console.log('❌ Failed to download from URL, trying alternative method');
              // Try to fetch the file manually
              const response = await fetch(attachment.url);
              const arrayBuffer = await response.arrayBuffer();
              const buffer = Buffer.from(arrayBuffer);
              const base64Data = buffer.toString('base64');
              media = new MessageMedia(attachment.type, base64Data, attachment.name);
            }
          } else if (attachment.url && attachment.url.startsWith('data:')) {
            // Data URL - extract and send
            console.log('📄 Processing data URL attachment');
            console.log('📋 Data URL type:', attachment.type);
            console.log('📋 Data URL length:', attachment.url.length);
            
            try {
              const base64Data = attachment.url.split(',')[1];
              console.log('📋 Base64 data length:', base64Data.length);
              media = new MessageMedia(attachment.type, base64Data, attachment.name);
              console.log('✅ MessageMedia created successfully for data URL');
            } catch (dataUrlError) {
              console.error('❌ Error processing data URL:', dataUrlError.message);
              continue;
            }
          } else if (attachment.url && attachment.url.startsWith('/uploads/')) {
            // Server file - read from disk
            console.log('💾 Processing server file attachment');
            const filePath = path.join(__dirname, attachment.url);
            if (fs.existsSync(filePath)) {
              const fileData = fs.readFileSync(filePath);
              const base64Data = fileData.toString('base64');
              media = new MessageMedia(attachment.type, base64Data, attachment.name);
            } else {
              console.log('❌ Server file not found:', filePath);
              continue;
            }
          } else {
            // Try to find the file in uploaded files by name
            console.log('🔍 Searching for file by name in uploaded files');
            const serverFile = uploadedFiles.find(f => f.originalName === attachment.name);
            if (serverFile && fs.existsSync(serverFile.filePath)) {
              const fileData = fs.readFileSync(serverFile.filePath);
              const base64Data = fileData.toString('base64');
              media = new MessageMedia(attachment.type, base64Data, attachment.name);
            } else {
              console.log('❌ Could not find or process attachment:', attachment.name);
              continue;
            }
          }
          
          if (media) {
            // Send with appropriate caption following WhatsApp default format
            if (mediaType === 'image') {
              // Images: No caption (like WhatsApp default)
              await whatsappClient.sendMessage(formattedPhone, media);
            } else if (mediaType === 'video') {
              // Videos: No filename, just send (WhatsApp shows duration automatically)
              await whatsappClient.sendMessage(formattedPhone, media);
            } else if (mediaType === 'document') {
              // Documents: Show filename
              const caption = attachment.name || 'Document';
              await whatsappClient.sendMessage(formattedPhone, media, {
                caption: caption
              });
            } else {
              // Audio and other files: Send without caption
              await whatsappClient.sendMessage(formattedPhone, media);
            }
            
            console.log('✅ Attachment sent successfully:', attachment.name, 'Type:', mediaType);
            
            // Add small delay between attachments to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        } catch (attachmentError) {
          console.error('❌ Failed to send attachment:', attachment.name, attachmentError.message);
          // Continue with other attachments instead of failing completely
        }
      }
    }

    res.json({ 
      success: true, 
      message: 'Message sent successfully',
      attachmentsSent: attachments.length,
      urlsIncluded: urls.length
    });
  } catch (error) {
    console.error('❌ Error sending message:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Send bulk messages endpoint
app.post('/api/send-bulk', async (req, res) => {
  try {
    const { contacts, message, attachments = [], urls = [] } = req.body;
    
    if (!whatsappClient) {
      return res.status(503).json({ 
        success: false, 
        error: 'WhatsApp client not initialized' 
      });
    }

    if (clientStatus !== 'ready') {
      return res.status(503).json({ 
        success: false, 
        error: 'WhatsApp client not ready' 
      });
    }

    console.log('📤 Sending bulk messages to', contacts.length, 'contacts');
    console.log('📎 Attachments:', attachments.length);
    console.log('🔗 URLs:', urls.length);

    const results = [];
    let finalMessage = message;
    
    // Add URLs to message if provided
    if (urls && urls.length > 0) {
      finalMessage += '\n\n';
      urls.forEach(url => {
        // Handle both string URLs and URL objects
        if (typeof url === 'string') {
          finalMessage += `🔗 ${url}\n`;
        } else if (url.url) {
          finalMessage += `🔗 ${url.title || url.url}: ${url.url}\n`;
        }
      });
    }

    for (const contact of contacts) {
      try {
        const formattedPhone = contact.phone.replace(/[^\d]/g, '') + '@c.us';
        console.log('📱 Bulk send - Original phone:', contact.phone);
        console.log('📱 Bulk send - Formatted phone:', formattedPhone);
        
        // Validate phone number format
        if (!formattedPhone.match(/^\d+@c\.us$/)) {
          console.error('❌ Invalid phone number format in bulk send:', formattedPhone);
          results.push({ phone: contact.phone, success: false, error: 'Invalid phone number format' });
          continue;
        }
        
        // Send text message (if there's text content)
        if (finalMessage && finalMessage.trim()) {
          await whatsappClient.sendMessage(formattedPhone, finalMessage);
        }
        
        // Send attachments if provided
        if (attachments && attachments.length > 0) {
          for (const attachment of attachments) {
            try {
              console.log('📎 Processing attachment for bulk send:', attachment.name, 'Type:', attachment.type);
              
              let media = null;
              let mediaType = null;
              
              // Determine WhatsApp media type based on file type
              const fileType = attachment.type.toLowerCase();
              const fileName = attachment.name.toLowerCase();
              
              if (fileType.startsWith('image/') || fileName.match(/\\.(jpg|jpeg|png|gif|webp|bmp|svg)$/)) {
                mediaType = 'image';
              } else if (fileType.startsWith('video/') || fileName.match(/\\.(mp4|avi|mov|wmv|flv|webm|mkv|3gp)$/)) {
                mediaType = 'video';
              } else if (fileType.startsWith('audio/') || fileName.match(/\\.(mp3|wav|ogg|m4a|aac|flac|wma)$/)) {
                mediaType = 'audio';
              } else if (fileType.includes('pdf') || fileName.match(/\\.(pdf)$/)) {
                mediaType = 'document';
              } else if (fileType.includes('document') || fileName.match(/\\.(doc|docx|xls|xlsx|ppt|pptx|txt|rtf|odt|ods|odp)$/)) {
                mediaType = 'document';
              } else if (fileName.match(/\\.(zip|rar|7z|tar|gz|bz2)$/)) {
                mediaType = 'document';
              } else {
                mediaType = 'document'; // Default to document for unknown types
              }
              
              // Handle different URL types
              if (attachment.url && attachment.url.startsWith('blob:')) {
                console.log('⚠️ Blob URL detected, skipping for bulk send');
                continue;
              } else if (attachment.url && attachment.url.startsWith('http')) {
                // HTTP URL - try to download and send
                try {
                  media = await MessageMedia.fromUrl(attachment.url);
                } catch (urlError) {
                  console.log('❌ Failed to download from URL, trying alternative method');
                  // Try to fetch the file manually
                  const response = await fetch(attachment.url);
                  const arrayBuffer = await response.arrayBuffer();
                  const buffer = Buffer.from(arrayBuffer);
                  const base64Data = buffer.toString('base64');
                  media = new MessageMedia(attachment.type, base64Data, attachment.name);
                }
              } else if (attachment.url && attachment.url.startsWith('data:')) {
                // Data URL - extract and send
                const base64Data = attachment.url.split(',')[1];
                media = new MessageMedia(attachment.type, base64Data, attachment.name);
              } else if (attachment.url && attachment.url.startsWith('/uploads/')) {
                // Server file - read from disk
                const filePath = path.join(__dirname, attachment.url);
                if (fs.existsSync(filePath)) {
                  const fileData = fs.readFileSync(filePath);
                  const base64Data = fileData.toString('base64');
                  media = new MessageMedia(attachment.type, base64Data, attachment.name);
                } else {
                  console.log('❌ Server file not found:', filePath);
                  continue;
                }
              } else {
                // Try to find the file in uploaded files by name
                const serverFile = uploadedFiles.find(f => f.originalName === attachment.name);
                if (serverFile && fs.existsSync(serverFile.filePath)) {
                  const fileData = fs.readFileSync(serverFile.filePath);
                  const base64Data = fileData.toString('base64');
                  media = new MessageMedia(attachment.type, base64Data, attachment.name);
                } else {
                  console.log('❌ Could not find or process attachment:', attachment.name);
                  continue;
                }
              }
              
              if (media) {
                // Send with appropriate caption following WhatsApp default format
                if (mediaType === 'image') {
                  // Images: No caption (like WhatsApp default)
                  await whatsappClient.sendMessage(formattedPhone, media);
                } else if (mediaType === 'video') {
                  // Videos: No filename, just send (WhatsApp shows duration automatically)
                  await whatsappClient.sendMessage(formattedPhone, media);
                } else if (mediaType === 'document') {
                  // Documents: Show filename
                  const caption = attachment.name || 'Document';
                  await whatsappClient.sendMessage(formattedPhone, media, {
                    caption: caption
                  });
                } else {
                  // Audio and other files: Send without caption
                  await whatsappClient.sendMessage(formattedPhone, media);
                }
                
                console.log('✅ Attachment sent to', contact.phone, ':', attachment.name, 'Type:', mediaType);
                
                // Add small delay between attachments to avoid rate limiting
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            } catch (attachmentError) {
              console.error('❌ Failed to send attachment to', contact.phone, attachmentError);
              // Continue with other attachments instead of failing completely
            }
          }
        }
        
        results.push({ phone: contact.phone, success: true });
        console.log('✅ Message sent to:', contact.phone);
        
        // Add delay between contacts to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        console.error('❌ Failed to send message to', contact.phone, error);
        results.push({ phone: contact.phone, success: false, error: error.message });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    res.json({ 
      success: true, 
      message: `Bulk send completed: ${successful} successful, ${failed} failed`,
      results,
      totalSent: successful,
      totalFailed: failed,
      attachmentsSent: attachments.length,
      urlsIncluded: urls.length
    });
  } catch (error) {
    console.error('❌ Error sending bulk messages:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get supported file formats endpoint
app.get('/api/supported-formats', (req, res) => {
  try {
    const supportedFormats = {
      images: {
        description: 'Image files (photos, graphics)',
        extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'],
        mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml'],
        maxSize: '16MB'
      },
      videos: {
        description: 'Video files',
        extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.3gp'],
        mimeTypes: ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/x-matroska', 'video/3gpp'],
        maxSize: '16MB'
      },
      audio: {
        description: 'Audio files',
        extensions: ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac', '.wma'],
        mimeTypes: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4', 'audio/aac', 'audio/flac', 'audio/x-ms-wma'],
        maxSize: '16MB'
      },
      documents: {
        description: 'Document files (PDFs, Office documents, text files)',
        extensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf', '.odt', '.ods', '.odp'],
        mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'text/plain', 'application/rtf', 'application/vnd.oasis.opendocument.text', 'application/vnd.oasis.opendocument.spreadsheet', 'application/vnd.oasis.opendocument.presentation'],
        maxSize: '100MB'
      },
      archives: {
        description: 'Archive/compressed files',
        extensions: ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'],
        mimeTypes: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed', 'application/x-tar', 'application/gzip', 'application/x-bzip2'],
        maxSize: '100MB'
      }
    };
    
    res.json({
      success: true,
      formats: supportedFormats,
      note: 'All files are automatically categorized and sent with appropriate WhatsApp media types'
    });
  } catch (error) {
    console.error('❌ Error getting supported formats:', error);
    res.status(500).json({ error: 'Failed to get supported formats' });
  }
});

// Enhanced file upload endpoint with format validation
app.post('/api/upload-file', async (req, res) => {
  try {
    const { name, type, size, data, folder } = req.body;
    
    if (!name || !type || !data) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields: name, type, data' 
      });
    }

    // Validate file size (100MB limit for documents, 16MB for media)
    const maxSize = type.startsWith('image/') || type.startsWith('video/') || type.startsWith('audio/') 
      ? 16 * 1024 * 1024  // 16MB for media
      : 100 * 1024 * 1024; // 100MB for documents
    
    if (size > maxSize) {
      return res.status(400).json({ 
        success: false, 
        error: `File too large. Maximum size is ${maxSize / (1024 * 1024)}MB for this file type` 
      });
    }

    // Determine WhatsApp media type
    const fileType = type.toLowerCase();
    const fileName = name.toLowerCase();
    let mediaType = 'document'; // default
    
    if (fileType.startsWith('image/') || fileName.match(/\\.(jpg|jpeg|png|gif|webp|bmp|svg)$/)) {
      mediaType = 'image';
    } else if (fileType.startsWith('video/') || fileName.match(/\\.(mp4|avi|mov|wmv|flv|webm|mkv|3gp)$/)) {
      mediaType = 'video';
    } else if (fileType.startsWith('audio/') || fileName.match(/\\.(mp3|wav|ogg|m4a|aac|flac|wma)$/)) {
      mediaType = 'audio';
    }

    // Create folder structure
    const folderPath = path.join(uploadsDir, folder || 'Other');
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = path.extname(name);
    const baseName = path.basename(name, fileExtension);
    const uniqueName = `${baseName}_${timestamp}${fileExtension}`;
    const filePath = path.join(folderPath, uniqueName);

    // Write file to disk
    const buffer = Buffer.from(data, 'base64');
    fs.writeFileSync(filePath, buffer);

    // Store file info with WhatsApp media type
    const fileInfo = {
      id: timestamp.toString(),
      name: name,
      originalName: name,
      uniqueName: uniqueName,
      type: type,
      size: size,
      folder: folder || 'Other',
      url: `/uploads/${folder || 'Other'}/${uniqueName}`,
      uploadDate: new Date().toISOString(),
      filePath: filePath,
      whatsappMediaType: mediaType
    };

    uploadedFiles.push(fileInfo);

    // Save file metadata to MySQL database via API
    try {
      const dbResponse = await fetch('https://saamrajyam.com/api/files.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: uniqueName,
          original_name: name,
          file_path: `/uploads/${folder || 'Other'}/${uniqueName}`,
          file_size: size,
          mime_type: type
        })
      });

      if (dbResponse.ok) {
        const dbResult = await dbResponse.json();
        console.log('✅ File metadata saved to database:', dbResult.file?.id);
        fileInfo.database_id = dbResult.file?.id;
      } else {
        console.warn('⚠️ Failed to save file metadata to database');
      }
    } catch (dbError) {
      console.error('❌ Database save error:', dbError);
    }

    console.log('✅ File uploaded to server:', name, 'WhatsApp Type:', mediaType);
    res.json({
      success: true,
      id: fileInfo.id,
      database_id: fileInfo.database_id,
      url: fileInfo.url,
      whatsappMediaType: mediaType,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('❌ Error uploading file:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to upload file' 
    });
  }
});

// Get uploaded files endpoint
app.get('/api/uploaded-files', (req, res) => {
  try {
    const files = uploadedFiles.map(file => ({
      id: file.id,
      name: file.originalName,
      type: file.type,
      size: file.size,
      url: `http://localhost:${PORT}${file.url}`,
      folder: file.folder,
      uploadDate: file.uploadDate
    }));
    
    res.json(files);
  } catch (error) {
    console.error('❌ Error getting uploaded files:', error);
    res.status(500).json({ error: 'Failed to get uploaded files' });
  }
});

// Delete file endpoint
app.delete('/api/delete-file/:id', (req, res) => {
  try {
    const fileId = req.params.id;
    const fileIndex = uploadedFiles.findIndex(f => f.id === fileId);
    
    if (fileIndex === -1) {
      return res.status(404).json({ 
        success: false, 
        error: 'File not found' 
      });
    }

    const file = uploadedFiles[fileIndex];
    
    // Delete file from disk
    if (fs.existsSync(file.filePath)) {
      fs.unlinkSync(file.filePath);
    }

    // Remove from database if it has a database_id
    if (file.database_id) {
      try {
        const dbResponse = await fetch(`https://saamrajyam.com/api/files.php?id=${file.database_id}`, {
          method: 'DELETE'
        });

        if (dbResponse.ok) {
          console.log('✅ File removed from database:', file.database_id);
        } else {
          console.warn('⚠️ Failed to remove file from database');
        }
      } catch (dbError) {
        console.error('❌ Database delete error:', dbError);
      }
    }

    // Remove from memory
    uploadedFiles.splice(fileIndex, 1);

    console.log('✅ File deleted:', file.originalName);
    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    console.error('❌ Error deleting file:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to delete file' 
    });
  }
});

// Clear all files endpoint
app.delete('/api/clear-files', (req, res) => {
  try {
    // Delete all files from disk
    uploadedFiles.forEach(file => {
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
      }
    });

    // Clear memory
    uploadedFiles = [];

    console.log('✅ All files cleared');
    res.json({ 
      success: true, 
      message: 'All files cleared successfully' 
    });
  } catch (error) {
    console.error('❌ Error clearing files:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to clear files' 
    });
  }
});

// Contact management endpoints
app.get('/api/contacts', (req, res) => {
  try {
    res.json(contacts);
  } catch (error) {
    console.error('❌ Error getting contacts:', error);
    res.status(500).json({ error: 'Failed to get contacts' });
  }
});

app.post('/api/contacts', (req, res) => {
  try {
    const contact = req.body;
    contact.id = Date.now().toString();
    contact.createdAt = new Date().toISOString();
    contact.updatedAt = new Date().toISOString();
    
    contacts.push(contact);
    saveDataToFile(contacts, 'contacts.json');
    
    console.log('✅ Contact added:', contact.name);
    res.json({ success: true, contact });
  } catch (error) {
    console.error('❌ Error adding contact:', error);
    res.status(500).json({ error: 'Failed to add contact' });
  }
});

app.put('/api/contacts/:id', (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    updates.updatedAt = new Date().toISOString();
    
    const index = contacts.findIndex(c => c.id === id);
    if (index === -1) {
      return res.status(404).json({ error: 'Contact not found' });
    }
    
    contacts[index] = { ...contacts[index], ...updates };
    saveDataToFile(contacts, 'contacts.json');
    
    console.log('✅ Contact updated:', contacts[index].name);
    res.json({ success: true, contact: contacts[index] });
  } catch (error) {
    console.error('❌ Error updating contact:', error);
    res.status(500).json({ error: 'Failed to update contact' });
  }
});

app.delete('/api/contacts/:id', (req, res) => {
  try {
    const { id } = req.params;
    const index = contacts.findIndex(c => c.id === id);
    
    if (index === -1) {
      return res.status(404).json({ error: 'Contact not found' });
    }
    
    const deletedContact = contacts.splice(index, 1)[0];
    saveDataToFile(contacts, 'contacts.json');
    
    console.log('✅ Contact deleted:', deletedContact.name);
    res.json({ success: true, message: 'Contact deleted successfully' });
  } catch (error) {
    console.error('❌ Error deleting contact:', error);
    res.status(500).json({ error: 'Failed to delete contact' });
  }
});

// Categories management
app.get('/api/categories', (req, res) => {
  try {
    res.json(categories);
  } catch (error) {
    console.error('❌ Error getting categories:', error);
    res.status(500).json({ error: 'Failed to get categories' });
  }
});

app.post('/api/categories', (req, res) => {
  try {
    const category = req.body;
    category.id = Date.now().toString();
    
    categories.push(category);
    saveDataToFile(categories, 'categories.json');
    
    console.log('✅ Category added:', category.name);
    res.json({ success: true, category });
  } catch (error) {
    console.error('❌ Error adding category:', error);
    res.status(500).json({ error: 'Failed to add category' });
  }
});

// Settings management
app.get('/api/settings', (req, res) => {
  try {
    res.json(settings);
  } catch (error) {
    console.error('❌ Error getting settings:', error);
    res.status(500).json({ error: 'Failed to get settings' });
  }
});

app.post('/api/settings', (req, res) => {
  try {
    const newSettings = req.body;
    settings = { ...settings, ...newSettings };
    saveDataToFile(settings, 'settings.json');
    
    console.log('✅ Settings updated');
    res.json({ success: true, settings });
  } catch (error) {
    console.error('❌ Error updating settings:', error);
    res.status(500).json({ error: 'Failed to update settings' });
  }
});

// Data export/import
app.get('/api/export-data', (req, res) => {
  try {
    const exportData = {
      contacts,
      categories,
      settings,
      uploadedFiles: uploadedFiles.map(f => ({
        id: f.id,
        name: f.originalName,
        type: f.type,
        size: f.size,
        folder: f.folder,
        uploadDate: f.uploadDate
      }))
    };
    
    res.json({ success: true, data: exportData });
  } catch (error) {
    console.error('❌ Error exporting data:', error);
    res.status(500).json({ error: 'Failed to export data' });
  }
});

app.post('/api/import-data', (req, res) => {
  try {
    const { contacts: importedContacts, categories: importedCategories, settings: importedSettings } = req.body;
    
    if (importedContacts) {
      contacts = importedContacts;
      saveDataToFile(contacts, 'contacts.json');
    }
    
    if (importedCategories) {
      categories = importedCategories;
      saveDataToFile(categories, 'categories.json');
    }
    
    if (importedSettings) {
      settings = importedSettings;
      saveDataToFile(settings, 'settings.json');
    }
    
    console.log('✅ Data imported successfully');
    res.json({ success: true, message: 'Data imported successfully' });
  } catch (error) {
    console.error('❌ Error importing data:', error);
    res.status(500).json({ error: 'Failed to import data' });
  }
});

// Manual reset endpoint for debugging
app.post('/api/whatsapp/reset', async (req, res) => {
  try {
    console.log('🔄 Manual reset requested...');
    
    // Reset all flags
    initializationInProgress = false;
    clientStatus = 'disconnected';
    qrCodeData = null;
    
    // Destroy existing client
    if (whatsappClient) {
      try {
        await whatsappClient.destroy();
      } catch (error) {
        console.log('⚠️ Error destroying client during reset:', error.message);
      }
      whatsappClient = null;
    }
    
    // Kill Chrome processes
    try {
      const { exec } = require('child_process');
      exec('pkill -f "Google Chrome"', (error) => {
        if (error) {
          console.log('⚠️ No Chrome processes to kill or error:', error.message);
        } else {
          console.log('✅ Chrome processes terminated during reset');
        }
      });
    } catch (chromeError) {
      console.log('⚠️ Error killing Chrome processes during reset:', chromeError.message);
    }
    
    console.log('✅ Manual reset completed');
    res.json({ 
      success: true, 
      message: 'WhatsApp client reset completed' 
    });
  } catch (error) {
    console.error('Error during manual reset:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Get network interfaces to show all possible URLs
const os = require('os');

function getNetworkIPs() {
  const interfaces = os.networkInterfaces();
  const ips = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Skip internal and non-IPv4 addresses
      if (interface.family === 'IPv4' && !interface.internal) {
        ips.push(interface.address);
      }
    }
  }
  
  return ips;
}

// Start server
app.listen(PORT, () => {
  const networkIPs = getNetworkIPs();
  
  console.log('\n' + '='.repeat(70));
  console.log('🚀 WHATSAPP BACKEND SERVER STARTED SUCCESSFULLY!');
  console.log('='.repeat(70));
  console.log(`📱 Service: WhatsApp Connection Only`);
  console.log(`🌐 CORS: Enabled for all origins`);
  console.log(`⏰ Started: ${new Date().toLocaleString()}`);
  
  console.log('\n📋 FRONTEND CONFIGURATION:');
  console.log('Copy one of these URLs to your frontend "Backend Setup":');
  console.log('-'.repeat(50));
  
  // Show localhost option
  console.log(`🏠 Local (same computer):    http://localhost:${PORT}`);
  
  // Show network IP options
  if (networkIPs.length > 0) {
    console.log(`🌐 Network: http://${networkIPs[0]}:${PORT}`);
    for (let i = 1; i < networkIPs.length; i++) {
      console.log(`                             http://${networkIPs[i]}:${PORT}`);
    }
  }
  
  console.log('-'.repeat(50));
  console.log('💡 USAGE INSTRUCTIONS:');
  console.log('1. Copy one of the URLs above');
  console.log('2. Open your Contact Sphere app');
  console.log('3. Click "Backend Setup" button');
  console.log('4. Paste the URL and click "Save & Test"');
  console.log('5. You should see "✅ Connected" status');
  
  console.log('\n🔧 API ENDPOINTS AVAILABLE:');
  console.log(`• Health Check: http://localhost:${PORT}/api/health`);
  console.log(`• WhatsApp Status: http://localhost:${PORT}/api/status`);
  console.log(`• Send Message: http://localhost:${PORT}/api/send-message`);
  console.log(`• Bulk Messages: http://localhost:${PORT}/api/send-bulk`);
  
  console.log('\n' + '='.repeat(70));
  console.log('✅ Server ready! Waiting for WhatsApp connection...');
  console.log('='.repeat(70) + '\n');
  // DO NOT initialize WhatsApp client automatically
  // initializeWhatsAppClient();
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT - Starting graceful shutdown...');
  await gracefulShutdown();
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM - Starting graceful shutdown...');
  await gracefulShutdown();
});

process.on('SIGQUIT', async () => {
  console.log('\n🛑 Received SIGQUIT - Starting graceful shutdown...');
  await gracefulShutdown();
});

// Handle uncaught exceptions
process.on('uncaughtException', async (error) => {
  console.error('\n❌ Uncaught Exception:', error);
  await gracefulShutdown();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  console.error('\n❌ Unhandled Rejection at:', promise, 'reason:', reason);
  await gracefulShutdown();
});

async function gracefulShutdown() {
  console.log('🔄 Starting graceful shutdown process...');
  try {
    // 1. Stop accepting new connections
    console.log('📡 Stopping server from accepting new connections...');
    // 2. Disconnect WhatsApp client properly
    if (whatsappClient) {
      console.log('📱 Disconnecting WhatsApp client...');
      try {
        if (clientStatus === 'ready' || clientStatus === 'authenticated') {
          console.log('🔐 Logging out from WhatsApp...');
          await whatsappClient.logout();
        }
        console.log('🧹 Destroying WhatsApp client...');
        // Clean up keep-alive interval
        if (whatsappClient.keepAliveInterval) {
          clearInterval(whatsappClient.keepAliveInterval);
          console.log('🔒 Keep-alive interval cleared');
        }
        await whatsappClient.destroy();
        whatsappClient = null;
        clientStatus = 'disconnected';
        console.log('✅ WhatsApp client disconnected successfully');
      } catch (whatsappError) {
        console.log('⚠️ Error during WhatsApp disconnection:', whatsappError.message);
        whatsappClient = null;
        clientStatus = 'disconnected';
      }
    }
    // 3. Save any pending data
    console.log('💾 Saving pending data...');
    try {
      saveDataToFile(contacts, 'contacts.json');
      saveDataToFile(categories, 'categories.json');
      saveDataToFile(settings, 'settings.json');
      console.log('✅ Data saved successfully');
    } catch (saveError) {
      console.log('⚠️ Error saving data:', saveError.message);
    }
    console.log('✅ Graceful shutdown completed');
    console.log('👋 WhatsApp backend server stopped');
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
  } finally {
    process.exit(0);
  }
} 