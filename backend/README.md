# 📱 WhatsApp Connection Service

## 🎯 **Purpose**
This backend provides **ONLY** WhatsApp connection functionality. 
- ❌ No database storage
- ❌ No file uploads  
- ❌ No contact management
- ✅ WhatsApp messaging only

## 🚀 **Quick Start**

### **Install Dependencies**
```bash
npm install
```

### **Start Service**
```bash
npm start
```

### **Development Mode**
```bash
npm run dev
```

## 🔧 **Configuration**

Copy `env.example` to `.env` and configure:
```bash
cp env.example .env
```

Required settings:
- `PORT=3001`
- `FRONTEND_URL=https://yourdomain.com`

## 📡 **API Endpoints**

### **Health Check**
```
GET /api/health
```

### **WhatsApp Status**
```
GET /api/whatsapp/status
```

### **Initialize WhatsApp**
```
POST /api/whatsapp/initialize
```

### **Send Message**
```
POST /api/whatsapp/send-message
Body: { phone, text, urls }
```

### **Send Bulk Messages**
```
POST /api/whatsapp/send-bulk-messages
Body: { contacts, text, urls }
```

## 💾 **Data Storage**

**This backend stores NO data:**
- Contacts: Stored in browser (frontend)
- Media files: Stored in browser (frontend)  
- Database: Browser-based SQLite (frontend)
- WhatsApp session: Local folder only

## 🌐 **Architecture**

```
Frontend (Browser) ←→ Backend (WhatsApp) ←→ WhatsApp Web
     ↓
Browser SQLite Database
(All contacts & media)
```

## 📁 **Files**

- `server-minimal.js` - Main server (WhatsApp only)
- `package.json` - Dependencies
- `env.example` - Configuration template
- `whatsapp-session/` - WhatsApp login data (auto-created)

## ⚡ **Features**

- ✅ WhatsApp QR code generation
- ✅ Message sending (text + URLs)
- ✅ Bulk message sending
- ✅ Session persistence
- ✅ CORS support for frontend
- ✅ Minimal resource usage

## 🔒 **Security**

- Only WhatsApp session data stored locally
- No sensitive user data on backend
- CORS protection
- No database vulnerabilities

## 🎯 **Perfect For**

- Shared hosting deployment
- Minimal server requirements
- Privacy-focused applications
- Browser-based data storage 