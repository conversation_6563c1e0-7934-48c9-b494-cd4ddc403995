#!/bin/bash

echo "🔧 WhatsApp Backend Fix Script"
echo "=============================="

# Kill existing processes
echo "🧹 Stopping existing processes..."
pkill -f "node server-minimal.js" 2>/dev/null || echo "No existing processes found"

# Clean session data
echo "🗑️ Clearing WhatsApp session data..."
rm -rf whatsapp-session 2>/dev/null || echo "No session data to clear"

# Wait for cleanup
sleep 3

# Check Chrome installation
echo "🔍 Checking Chrome installation..."
if [ -f "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" ]; then
    echo "✅ Google Chrome found"
elif [ -f "/Applications/Chromium.app/Contents/MacOS/Chromium" ]; then
    echo "✅ Chromium found"
else
    echo "⚠️ Chrome not found in standard locations"
    echo "💡 Please install Google Chrome from: https://www.google.com/chrome/"
fi

# Start server
echo "🚀 Starting WhatsApp backend..."
node server-minimal.js
