const express = require('express');
const cors = require('cors');
const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 3001;

// CORS configuration
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// WhatsApp client variables
let whatsappClient = null;
let qrCodeData = null;
let clientStatus = 'disconnected';

// Simple Chrome configuration
const puppeteerConfig = {
  headless: true,
  args: [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--disable-gpu',
    '--no-first-run',
    '--disable-extensions',
    '--disable-default-apps',
    '--disable-sync',
    '--disable-translate',
    '--hide-scrollbars',
    '--mute-audio',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors',
    '--disable-web-security',
    '--user-data-dir=./chrome-simple'
  ],
  timeout: 0,
  protocolTimeout: 0,
  ignoreDefaultArgs: ['--disable-extensions'],
  handleSIGINT: false,
  handleSIGTERM: false,
  handleSIGHUP: false
};

// Initialize WhatsApp client
function initializeWhatsAppClient() {
  try {
    console.log('🔄 Initializing WhatsApp client...');
    
    // Clean up existing client
    if (whatsappClient) {
      try {
        if (whatsappClient.pupBrowser) {
          whatsappClient.destroy();
        }
      } catch (error) {
        console.log('🧹 Cleaned up previous client');
      }
      whatsappClient = null;
    }

    whatsappClient = new Client({
      authStrategy: new LocalAuth({
        dataPath: './whatsapp-simple'
      }),
      puppeteer: puppeteerConfig
    });

    whatsappClient.on('qr', (qr) => {
      console.log('📱 QR Code received!');
      qrCodeData = qr;
      clientStatus = 'qr';
    });

    whatsappClient.on('ready', () => {
      console.log('✅ WhatsApp client is ready!');
      clientStatus = 'ready';
      qrCodeData = null;
    });

    whatsappClient.on('authenticated', () => {
      console.log('🔐 WhatsApp authenticated');
      clientStatus = 'authenticated';
    });

    whatsappClient.on('disconnected', (reason) => {
      console.log('❌ WhatsApp disconnected:', reason);
      clientStatus = 'disconnected';
      qrCodeData = null;
    });

    whatsappClient.on('auth_failure', (msg) => {
      console.error('❌ Authentication failed:', msg);
      clientStatus = 'auth_failure';
    });

    whatsappClient.on('error', (error) => {
      console.error('❌ WhatsApp error:', error.message);
      clientStatus = 'error';
    });

    whatsappClient.initialize().then(() => {
      console.log('✅ WhatsApp initialization started');
    }).catch((error) => {
      console.error('❌ Failed to initialize:', error.message);
      clientStatus = 'error';
    });

  } catch (error) {
    console.error('❌ Error in initializeWhatsAppClient:', error.message);
    clientStatus = 'error';
  }
}

// API Routes

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'WhatsApp Simple',
    timestamp: new Date().toISOString() 
  });
});

// Status endpoint
app.get('/api/status', async (req, res) => {
  try {
    let qrCodeImage = null;
    
    if (qrCodeData) {
      qrCodeImage = await qrcode.toDataURL(qrCodeData);
    }
    
    res.json({
      connected: clientStatus === 'ready',
      status: clientStatus,
      qrCode: qrCodeImage
    });
  } catch (error) {
    console.error('Error getting status:', error);
    res.status(500).json({ 
      connected: false, 
      error: 'Failed to get status' 
    });
  }
});

// Send message endpoint
app.post('/api/send-message', async (req, res) => {
  try {
    const { phone, message, attachments = [] } = req.body;
    
    if (!whatsappClient || clientStatus !== 'ready') {
      return res.status(503).json({ 
        success: false, 
        error: 'WhatsApp client not ready' 
      });
    }

    const formattedPhone = phone.replace(/[^\\d]/g, '') + '@c.us';
    
    // Send text message
    if (message && message.trim()) {
      await whatsappClient.sendMessage(formattedPhone, message);
    }

    // Send attachments
    for (const attachment of attachments) {
      try {
        let media = null;
        
        if (attachment.url && attachment.url.startsWith('http')) {
          media = await MessageMedia.fromUrl(attachment.url);
        } else if (attachment.url && attachment.url.startsWith('data:')) {
          const base64Data = attachment.url.split(',')[1];
          media = new MessageMedia(attachment.type, base64Data, attachment.name);
        }
        
        if (media) {
          await whatsappClient.sendMessage(formattedPhone, media, {
            caption: attachment.name || 'Attachment'
          });
        }
      } catch (attachmentError) {
        console.error('Failed to send attachment:', attachmentError);
      }
    }

    res.json({ 
      success: true, 
      message: 'Message sent successfully'
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Get supported formats
app.get('/api/supported-formats', (req, res) => {
  res.json({
    success: true,
    formats: {
      images: { extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'], maxSize: '16MB' },
      videos: { extensions: ['.mp4', '.avi', '.mov', '.webm'], maxSize: '16MB' },
      audio: { extensions: ['.mp3', '.wav', '.ogg', '.m4a'], maxSize: '16MB' },
      documents: { extensions: ['.pdf', '.doc', '.docx', '.txt'], maxSize: '100MB' }
    }
  });
});

// Start server
app.listen(PORT, () => {
  console.log('======================================================================');
  console.log('🚀 WHATSAPP SIMPLE BACKEND STARTED!');
  console.log('======================================================================');
  console.log(`📱 Service: WhatsApp Simple`);
  console.log(`🌐 Port: ${PORT}`);
  console.log(`⏰ Started: ${new Date().toLocaleString()}`);
  console.log('');
  console.log('📋 FRONTEND CONFIGURATION:');
  console.log('Copy this URL to your frontend "Backend Setup":');
  console.log('--------------------------------------------------');
  console.log(`🏠 Local: http://localhost:${PORT}`);
  console.log('--------------------------------------------------');
  console.log('');
  console.log('🔧 API ENDPOINTS:');
  console.log(`• Health: http://localhost:${PORT}/api/health`);
  console.log(`• Status: http://localhost:${PORT}/api/status`);
  console.log(`• Send: http://localhost:${PORT}/api/send-message`);
  console.log('======================================================================');
  
  // Initialize WhatsApp client
  initializeWhatsAppClient();
}); 