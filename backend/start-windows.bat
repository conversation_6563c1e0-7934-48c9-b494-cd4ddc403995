@echo off
echo ========================================
echo  Contact Sphere Organizer - Backend
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

:: Check if npm packages are installed
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes on first run...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
    echo.
    echo Dependencies installed successfully!
    echo.
)

:: Get local IP address for display
echo Your backend will be available at:
echo - Local: http://localhost:3001
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        echo - Network: http://%%b:3001
    )
)
echo.
echo Share the Network URL with other users to connect
echo.

:: Start the server
echo Starting backend server...
echo Press Ctrl+C to stop the server
echo.
npm start

pause 