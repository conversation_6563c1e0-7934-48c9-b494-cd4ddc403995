{"name": "whatsapp-backend", "version": "1.0.0", "description": "WhatsApp Connection Service Only", "main": "server-minimal.js", "scripts": {"start": "node server-minimal.js", "dev": "nodemon server-minimal.js"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.7", "qrcode": "^1.5.3", "whatsapp-web.js": "^1.21.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": ["whatsapp", "messaging", "api"], "author": "", "license": "MIT"}