#!/bin/bash

echo "🚀 WhatsApp Backend Startup with Chrome Fix"
echo "==========================================="

# Check if we're in the backend directory
if [ ! -f "server-minimal.js" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    echo "💡 Usage: cd backend && ./start-whatsapp-fixed.sh"
    exit 1
fi

# Run the Chrome singleton fix first
echo "🔧 Running Chrome singleton lock fix..."
./fix-chrome-singleton.sh

echo ""
echo "🔄 Starting WhatsApp backend server..."
echo "💡 The server will now start with improved Chrome stability"
echo ""

# Start the server
node server-minimal.js 