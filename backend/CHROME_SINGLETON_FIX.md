# Chrome Singleton Lock Fix

## Problem Description

The error you're experiencing is a common issue with <PERSON><PERSON><PERSON><PERSON> on macOS:

```
Failed to create /Users/<USER>/chrome-user-data/SingletonLock: File exists (17)
Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.
```

This happens when <PERSON><PERSON> tries to create a singleton lock file but it already exists, preventing multiple browser instances from running simultaneously.

## Root Cause

1. **Chrome Process Not Properly Terminated**: Previous Chrome instances may not have been fully closed
2. **Singleton Lock Files**: Chrome creates lock files to prevent multiple instances, but these can become stale
3. **User Data Directory Conflicts**: The `chrome-user-data` directory may have conflicting lock files
4. **System Chrome Interference**: Your system Chrome browser may be interfering with Puppeteer

## Solutions

### Quick Fix (Recommended)

Run the automated fix script:

```bash
cd backend
./fix-chrome-singleton.sh
```

This script will:
- Kill all Chrome processes
- Remove singleton lock files
- Clean up session data
- Verify Chrome installation

### Manual Fix

If the automated script doesn't work, try these steps manually:

1. **Kill Chrome Processes**:
   ```bash
   pkill -f "Google Chrome"
   pkill -f "Chromium"
   pkill -f "chrome-mac"
   ```

2. **Remove Lock Files**:
   ```bash
   find backend/chrome-user-data -name "SingletonLock" -delete
   find backend/chrome-user-data -name "SingletonSocket*" -delete
   ```

3. **Clean Session Data**:
   ```bash
   rm -rf backend/whatsapp-session
   rm -rf backend/chrome-user-data
   ```

4. **Restart the Server**:
   ```bash
   cd backend
   node server-minimal.js
   ```

### Advanced Fix

If the issue persists:

1. **Check System Chrome**:
   - Close all Chrome browser windows
   - Check Activity Monitor for any remaining Chrome processes
   - Force quit if necessary

2. **Clear System Chrome Data** (Use with caution):
   ```bash
   rm -rf ~/Library/Application\ Support/Google/Chrome/SingletonLock
   rm -rf ~/Library/Application\ Support/Google/Chrome/SingletonSocket*
   ```

3. **Update Chrome**:
   - Make sure you have the latest version of Google Chrome
   - Download from: https://www.google.com/chrome/

4. **Restart Your Computer**:
   - Sometimes a full system restart is needed to clear all processes

## Prevention

To prevent this issue in the future:

1. **Always use the fixed startup script**:
   ```bash
   cd backend
   ./start-whatsapp-fixed.sh
   ```

2. **Don't run multiple instances** of the WhatsApp backend simultaneously

3. **Properly close the server** using Ctrl+C instead of force-killing

4. **Keep Chrome updated** to the latest version

## Troubleshooting

### Still Getting the Error?

1. **Check Chrome Installation**:
   ```bash
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version
   ```

2. **Verify Node.js and Dependencies**:
   ```bash
   node --version
   npm list whatsapp-web.js
   ```

3. **Check System Resources**:
   ```bash
   df -h
   top -l 1 | grep "PhysMem"
   ```

4. **Try Different Chrome Path**:
   - The script automatically detects Chrome paths
   - If you have Chrome installed in a custom location, update the paths in `server-minimal.js`

### Alternative Solutions

1. **Use Chromium instead of Chrome**:
   - Install Chromium: `brew install chromium`
   - The script will automatically detect and use Chromium

2. **Use a Different User Data Directory**:
   - Modify the `--user-data-dir` argument in `server-minimal.js`
   - Use a unique directory for each instance

3. **Run in Non-Headless Mode** (for debugging):
   - Change `headless: true` to `headless: false` in `server-minimal.js`
   - This will show the Chrome window and help with debugging

## Support

If you continue to experience issues:

1. Check the console output for specific error messages
2. Try running the fix script multiple times
3. Restart your computer
4. Update both Chrome and Node.js to the latest versions
5. Check if any antivirus software is blocking Chrome

## Files Modified

- `fix-chrome-singleton.sh` - Automated fix script
- `start-whatsapp-fixed.sh` - Startup script with automatic fix
- `server-minimal.js` - Enhanced Puppeteer configuration
- `CHROME_SINGLETON_FIX.md` - This documentation 