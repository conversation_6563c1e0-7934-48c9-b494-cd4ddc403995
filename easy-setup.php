<?php
/**
 * 🚀 SUPER EASY DATABASE SETUP - ONE CLICK SOLUTION
 * This script will automatically set up your entire database
 * Just upload this file and run it in your browser!
 */

// Enable error reporting for setup
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html>
<head>
    <title>🚀 Contact Sphere Organizer - Easy Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .button:hover { background: #005a87; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>";

echo "<h1>🚀 Contact Sphere Organizer - Super Easy Setup</h1>";
echo "<p><strong>This script will automatically set up your entire database!</strong></p>";

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handleSetup();
} else {
    showForm();
}

function showForm() {
    echo "<div class='info'>
        <h2>📋 What This Will Do:</h2>
        <ul>
            <li>✅ Create all database tables</li>
            <li>✅ Insert default data</li>
            <li>✅ Create upload folders</li>
            <li>✅ Generate config file</li>
        </ul>
    </div>";

    echo "<div class='step'>
        <h2>🔧 Enter Your Database Details:</h2>
        <form method='POST'>
            <table>
                <tr>
                    <td><strong>Database Host:</strong></td>
                    <td><input type='text' name='db_host' value='localhost' style='width: 200px; padding: 5px;' required></td>
                    <td><small>Usually 'localhost' for shared hosting</small></td>
                </tr>
                <tr>
                    <td><strong>Database Name:</strong></td>
                    <td><input type='text' name='db_name' placeholder='contact_sphere_organizer' style='width: 200px; padding: 5px;' required></td>
                    <td><small>Your database name from cPanel</small></td>
                </tr>
                <tr>
                    <td><strong>Database Username:</strong></td>
                    <td><input type='text' name='db_user' placeholder='your_username' style='width: 200px; padding: 5px;' required></td>
                    <td><small>Your database username</small></td>
                </tr>
                <tr>
                    <td><strong>Database Password:</strong></td>
                    <td><input type='password' name='db_pass' placeholder='your_password' style='width: 200px; padding: 5px;' required></td>
                    <td><small>Your database password</small></td>
                </tr>
            </table>
            
            <br>
            <button type='submit' class='button'>🚀 Start Setup</button>
        </form>
    </div>";

    echo "<div class='info'>
        <h2>📖 How to Get Database Details:</h2>
        <ol>
            <li><strong>Login to cPanel</strong> (your hosting provider's control panel)</li>
            <li><strong>Find \"MySQL Databases\"</strong> or \"Databases\" section</li>
            <li><strong>Create a new database</strong> (name it 'contact_sphere_organizer')</li>
            <li><strong>Create a database user</strong> with a strong password</li>
            <li><strong>Add the user to the database</strong> with all privileges</li>
            <li><strong>Copy the details</strong> to the form above</li>
        </ol>
    </div>";

    echo "<div class='info'>
        <h2>⚠️ Important Notes:</h2>
        <ul>
            <li>Make sure you have MySQL enabled on your hosting</li>
            <li>Your hosting must support PHP and MySQL</li>
            <li>Keep your database credentials secure</li>
            <li>Delete this file after setup is complete</li>
        </ul>
    </div>";
}

function handleSetup() {
    $host = $_POST['db_host'] ?? '';
    $name = $_POST['db_name'] ?? '';
    $user = $_POST['db_user'] ?? '';
    $pass = $_POST['db_pass'] ?? '';

    if (empty($host) || empty($name) || empty($user) || empty($pass)) {
        echo "<div class='error'>❌ Please fill all fields!</div>";
        showForm();
        return;
    }

    echo "<h2>🚀 Starting Setup...</h2>";

    // Test connection
    echo "<div class='step'>
        <h3>Step 1: Testing Connection...</h3>";
    try {
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<div class='success'>✅ Connection successful!</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Connection failed: " . $e->getMessage() . "</div>";
        showForm();
        return;
    }
    echo "</div>";

    // Create database
    /*
    echo "<div class='step'>
        <h3>Step 2: Creating Database...</h3>";
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<div class='success'>✅ Database '$name' created!</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to create database: " . $e->getMessage() . "</div>";
        showForm();
        return;
    }
    echo "</div>";
    */

    // Connect to database
    echo "<div class='step'>
        <h3>Step 3: Creating Tables...</h3>";
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$name;charset=utf8mb4", $user, $pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create tables
        $tables = [
            "CREATE TABLE IF NOT EXISTS contacts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                state VARCHAR(50),
                country VARCHAR(50) DEFAULT 'India',
                company VARCHAR(100),
                notes TEXT,
                is_favorite BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            "CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                color VARCHAR(7) DEFAULT '#3B82F6',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            "CREATE TABLE IF NOT EXISTS files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",
            "CREATE TABLE IF NOT EXISTS whatsapp_messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                contact_id INT NOT NULL,
                message_text TEXT NOT NULL,
                status ENUM('sent', 'delivered', 'read', 'failed') DEFAULT 'pending',
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )"
        ];
        
        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }
        
        // Insert default data
        $pdo->exec("INSERT IGNORE INTO categories (name, color) VALUES 
            ('Hot Lead', '#EF4444'),
            ('Warm Lead', '#F59E0B'),
            ('Client', '#10B981'),
            ('Vendor', '#8B5CF6')");
            
        echo "<div class='success'>✅ Tables created successfully!</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Failed to create tables: " . $e->getMessage() . "</div>";
        showForm();
        return;
    }
    echo "</div>";

    // Create config file
    echo "<div class='step'>
        <h3>Step 4: Creating Config File...</h3>";
    $config = "<?php
define('DB_HOST', '$host');
define('DB_NAME', '$name');
define('DB_USER', '$user');
define('DB_PASS', '$pass');
define('DB_CHARSET', 'utf8mb4');
define('UPLOAD_DIR', '../uploads/');
define('MAX_FILE_SIZE', 16 * 1024 * 1024);

class Database {
    private static \$instance = null;
    private \$connection;
    
    private function __construct() {
        \$dsn = \"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=\" . DB_CHARSET;
        \$this->connection = new PDO(\$dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
    }
    
    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new self();
        }
        return self::\$instance;
    }
    
    public function getConnection() { return \$this->connection; }
    
    public function query(\$sql, \$params = []) {
        \$stmt = \$this->connection->prepare(\$sql);
        \$stmt->execute(\$params);
        return \$stmt;
    }
    
    public function fetchAll(\$sql, \$params = []) {
        return \$this->query(\$sql, \$params)->fetchAll();
    }
    
    public function fetchOne(\$sql, \$params = []) {
        return \$this->query(\$sql, \$params)->fetch();
    }
    
    public function insert(\$table, \$data) {
        \$columns = implode(', ', array_keys(\$data));
        \$placeholders = ':' . implode(', :', array_keys(\$data));
        \$sql = \"INSERT INTO \$table (\$columns) VALUES (\$placeholders)\";
        \$this->query(\$sql, \$data);
        return \$this->connection->lastInsertId();
    }
}

function sanitizeInput(\$data) {
    return htmlspecialchars(trim(\$data), ENT_QUOTES, 'UTF-8');
}

date_default_timezone_set('Asia/Kolkata');
?>";

    if (file_put_contents('database/config.php', $config)) {
        echo "<div class='success'>✅ Config file created!</div>";
    } else {
        echo "<div class='error'>❌ Failed to create config file</div>";
    }
    echo "</div>";

    // Create upload directories
    echo "<div class='step'>
        <h3>Step 5: Creating Upload Directories...</h3>";
    $dirs = ['uploads', 'uploads/images', 'uploads/videos', 'uploads/documents'];
    $created = 0;
    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) $created++;
        } else {
            $created++;
        }
    }
    echo "<div class='success'>✅ Created $created upload directories!</div>";
    echo "</div>";

    // Success
    echo "<div class='success' style='text-align:center;font-size:18px;padding:20px;'>";
    echo "<h2>🎉 SETUP COMPLETE!</h2>";
    echo "<p>Your database is ready to use!</p>";
    echo "</div>";

    echo "<div class='info'><h3>📋 Next Steps:</h3>";
    echo "<ol><li>Upload your React frontend</li>";
    echo "<li>Update API endpoints in your frontend</li>";
    echo "<li>Start your local backend for WhatsApp</li>";
    echo "<li>Delete this setup file for security</li></ol></div>";

    echo "<div class='step'><h3>📊 Summary:</h3>";
    echo "<table border='1' style='width:100%;border-collapse:collapse;'>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    echo "<tr><td>Database Host</td><td>$host</td></tr>";
    echo "<tr><td>Database Name</td><td>$name</td></tr>";
    echo "<tr><td>Database User</td><td>$user</td></tr>";
    echo "<tr><td>Upload Directories</td><td>$created</td></tr>";
    echo "</table></div>";
}

echo "</body></html>";
?> 