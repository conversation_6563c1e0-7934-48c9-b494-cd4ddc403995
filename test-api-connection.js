// Test API Connection to MySQL Database
// This script tests the connection to the hosted MySQL API

const API_BASE = 'https://webwaa.io/contact-sphere-organizer';

async function testAPIConnection() {
  console.log('🔍 Testing API connection to:', API_BASE);
  
  try {
    // Test 1: Get contacts (should work)
    console.log('\n📊 Test 1: GET /api/contacts');
    const getResponse = await fetch(`${API_BASE}/api/contacts?limit=5`);
    console.log('Status:', getResponse.status, getResponse.statusText);
    
    if (getResponse.ok) {
      const getData = await getResponse.json();
      console.log('✅ GET Success:', getData);
    } else {
      const errorText = await getResponse.text();
      console.log('❌ GET Error:', errorText);
    }
    
    // Test 2: Add a test contact
    console.log('\n📝 Test 2: POST /api/contacts (Add Contact)');
    const testContact = {
      name: 'Test Contact API',
      phone: '9999999999',
      email: '<EMAIL>',
      address: 'Test Address',
      city: 'Test City',
      state: 'Test State',
      country: 'India',
      notes: 'Test contact for API validation',
      build_type: 'Residential',
      project_status: 'Prospect',
      is_favorite: false,
      is_pinned: false,
      is_shortlisted: false,
      has_active_reminder: false,
      categories: ['Test Category']
    };
    
    const postResponse = await fetch(`${API_BASE}/api/contacts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testContact)
    });
    
    console.log('Status:', postResponse.status, postResponse.statusText);
    
    if (postResponse.ok) {
      const postData = await postResponse.json();
      console.log('✅ POST Success:', postData);
    } else {
      const errorText = await postResponse.text();
      console.log('❌ POST Error:', errorText);
      console.log('❌ POST Headers:', Object.fromEntries(postResponse.headers.entries()));
    }
    
  } catch (error) {
    console.error('❌ Network Error:', error.message);
  }
}

// Test the connection
testAPIConnection();
