<?php
header('Content-Type: text/plain');
header('Access-Control-Allow-Origin: *');

require_once 'database/config.php';

try {
    echo "Testing database connection...\n";
    $db = Database::getInstance();
    echo "✅ Database connected successfully\n";
    
    // Check if files table exists
    echo "\nChecking if files table exists...\n";
    $tableCheck = $db->fetchOne("SHOW TABLES LIKE 'files'");
    if ($tableCheck) {
        echo "✅ Files table exists\n";
        
        // Get table structure
        echo "\nFiles table structure:\n";
        $structure = $db->fetchAll("DESCRIBE files");
        foreach ($structure as $column) {
            echo "- {$column['Field']} ({$column['Type']})\n";
        }
        
        // Count files
        echo "\nCounting files in database...\n";
        $count = $db->fetchOne("SELECT COUNT(*) as count FROM files");
        echo "Total files: " . $count['count'] . "\n";
        
        // Get sample files
        if ($count['count'] > 0) {
            echo "\nSample files:\n";
            $files = $db->fetchAll("SELECT * FROM files LIMIT 5");
            foreach ($files as $file) {
                echo "- ID: {$file['id']}, Name: {$file['original_name']}, Type: {$file['file_type']}\n";
            }
        }
        
    } else {
        echo "❌ Files table does not exist\n";
        
        // Show all tables
        echo "\nAvailable tables:\n";
        $tables = $db->fetchAll("SHOW TABLES");
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            echo "- $tableName\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
