<?php
/**
 * Categories API - Handle category CRUD operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database/config.php';

try {
    $db = Database::getInstance();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetCategories($db);
            break;
        case 'POST':
            handleCreateCategory($db);
            break;
        case 'PUT':
            handleUpdateCategory($db);
            break;
        case 'DELETE':
            handleDeleteCategory($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetCategories($db) {
    $sql = "
        SELECT 
            c.*,
            COUNT(cc.contact_id) as contact_count
        FROM categories c
        LEFT JOIN contact_categories cc ON c.id = cc.category_id
        GROUP BY c.id
        ORDER BY c.name ASC
    ";
    
    $categories = $db->fetchAll($sql);
    
    echo json_encode([
        'success' => true,
        'data' => $categories
    ]);
}

function handleCreateCategory($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['name'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Category name is required']);
        return;
    }
    
    $name = sanitizeInput($input['name']);
    $color = isset($input['color']) ? sanitizeInput($input['color']) : '#3B82F6';
    $description = isset($input['description']) ? sanitizeInput($input['description']) : '';
    
    // Check if category already exists
    $existing = $db->fetchOne("SELECT id FROM categories WHERE name = :name", ['name' => $name]);
    if ($existing) {
        http_response_code(400);
        echo json_encode(['error' => 'Category already exists']);
        return;
    }
    
    $categoryId = $db->insert('categories', [
        'name' => $name,
        'color' => $color,
        'description' => $description
    ]);
    
    logActivity('create', 'category', $categoryId, "Created category: $name");
    
    echo json_encode([
        'success' => true,
        'message' => 'Category created successfully',
        'data' => ['id' => $categoryId]
    ]);
}

function handleUpdateCategory($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Category ID is required']);
        return;
    }
    
    $categoryId = (int)$input['id'];
    
    // Check if category exists
    $existing = $db->fetchOne("SELECT name FROM categories WHERE id = :id", ['id' => $categoryId]);
    if (!$existing) {
        http_response_code(404);
        echo json_encode(['error' => 'Category not found']);
        return;
    }
    
    $updateData = [];
    
    if (isset($input['name'])) {
        $updateData['name'] = sanitizeInput($input['name']);
    }
    if (isset($input['color'])) {
        $updateData['color'] = sanitizeInput($input['color']);
    }
    if (isset($input['description'])) {
        $updateData['description'] = sanitizeInput($input['description']);
    }
    
    if (!empty($updateData)) {
        $db->update('categories', $updateData, 'id = :id', ['id' => $categoryId]);
    }
    
    logActivity('update', 'category', $categoryId, "Updated category: {$existing['name']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Category updated successfully'
    ]);
}

function handleDeleteCategory($db) {
    $categoryId = isset($_GET['id']) ? (int)$_GET['id'] : null;
    
    if (!$categoryId) {
        http_response_code(400);
        echo json_encode(['error' => 'Category ID is required']);
        return;
    }
    
    // Check if category exists
    $existing = $db->fetchOne("SELECT name FROM categories WHERE id = :id", ['id' => $categoryId]);
    if (!$existing) {
        http_response_code(404);
        echo json_encode(['error' => 'Category not found']);
        return;
    }
    
    // Check if category is in use
    $inUse = $db->fetchOne("SELECT COUNT(*) as count FROM contact_categories WHERE category_id = :id", ['id' => $categoryId]);
    if ($inUse['count'] > 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Cannot delete category that has contacts assigned']);
        return;
    }
    
    $db->delete('categories', 'id = :id', ['id' => $categoryId]);
    
    logActivity('delete', 'category', $categoryId, "Deleted category: {$existing['name']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Category deleted successfully'
    ]);
}
?> 