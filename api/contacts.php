<?php
/**
 * Contacts API - Handle contact CRUD operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database/config.php';

try {
    $db = Database::getInstance();

    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetContacts($db);
            break;
        case 'POST':
            handleCreateContact($db);
            break;
        case 'PUT':
            handleUpdateContact($db);
            break;
        case 'DELETE':
            handleDeleteContact($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
}

function handleGetContacts($db) {
    try {
        // Check if requesting deleted contacts
        $includeDeleted = isset($_GET['include_deleted']) && $_GET['include_deleted'] === 'true';

        $sql = "
            SELECT
                c.*,
                GROUP_CONCAT(DISTINCT cat.name) as categories,
                COUNT(DISTINCT cf.file_id) as file_count,
                COUNT(DISTINCT r.id) as reminder_count,
                COUNT(DISTINCT wm.id) as message_count
            FROM contacts c
            LEFT JOIN contact_categories cc ON c.id = cc.contact_id
            LEFT JOIN categories cat ON cc.category_id = cat.id
            LEFT JOIN contact_files cf ON c.id = cf.contact_id
            LEFT JOIN reminders r ON c.id = r.contact_id AND r.is_completed = FALSE
            LEFT JOIN whatsapp_messages wm ON c.id = wm.contact_id
            GROUP BY c.id
            ORDER BY c.created_at DESC
        ";

        $contacts = $db->fetchAll($sql);

        // Format the response
        $formattedContacts = array_map(function($contact) {
            return [
                'id' => (string)$contact['id'],
                'name' => $contact['name'],
                'phone' => $contact['phone'],
                'email' => $contact['email'] ?? '',
                'address' => $contact['address'] ?? '',
                'city' => $contact['city'] ?? '',
                'state' => $contact['state'] ?? '',
                'country' => $contact['country'] ?? 'India',
                'postal_code' => $contact['postal_code'] ?? '',
                'company' => $contact['company'] ?? '',
                'job_title' => $contact['job_title'] ?? '',
                'website' => $contact['website'] ?? '',
                'notes' => $contact['notes'] ?? '',
                'build_type' => $contact['build_type'] ?? 'Residential',
                'budget_range' => $contact['budget_range'] ?? '',
                'project_status' => $contact['project_status'] ?? 'Prospect',
                'source' => $contact['source'] ?? '',
                'is_favorite' => (bool)$contact['is_favorite'],
                'is_pinned' => (bool)$contact['is_pinned'],
                'is_shortlisted' => (bool)$contact['is_shortlisted'],
                'has_active_reminder' => (bool)$contact['has_active_reminder'],
                'date' => $contact['date'],
                'created_at' => $contact['created_at'],
                'updated_at' => $contact['updated_at'],
                'categories' => $contact['categories'] ? explode(',', $contact['categories']) : [],
                'file_count' => (int)$contact['file_count'],
                'reminder_count' => (int)$contact['reminder_count'],
                'message_count' => (int)$contact['message_count']
            ];
        }, $contacts);

        echo json_encode([
            'success' => true,
            'contacts' => $formattedContacts,
            'total' => count($formattedContacts)
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch contacts: ' . $e->getMessage()]);
    }
}

function handleCreateContact($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input || !isset($input['name']) || !isset($input['phone'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Name and phone are required']);
            return;
        }

        // Prepare contact data
        $contactData = [
            'name' => $input['name'],
            'phone' => $input['phone'],
            'email' => $input['email'] ?? null,
            'address' => $input['address'] ?? null,
            'city' => $input['city'] ?? null,
            'state' => $input['state'] ?? null,
            'country' => $input['country'] ?? 'India',
            'postal_code' => $input['postal_code'] ?? null,
            'company' => $input['company'] ?? null,
            'job_title' => $input['job_title'] ?? null,
            'website' => $input['website'] ?? null,
            'notes' => $input['notes'] ?? null,
            'build_type' => $input['build_type'] ?? 'Residential',
            'budget_range' => $input['budget_range'] ?? null,
            'project_status' => $input['project_status'] ?? 'Prospect',
            'source' => $input['source'] ?? null,
            'is_favorite' => isset($input['is_favorite']) ? (bool)$input['is_favorite'] : false,
            'is_pinned' => isset($input['is_pinned']) ? (bool)$input['is_pinned'] : false,
            'is_shortlisted' => isset($input['is_shortlisted']) ? (bool)$input['is_shortlisted'] : false,
            'date' => $input['date'] ?? date('Y-m-d')
        ];

        $contactId = $db->insert('contacts', $contactData);

        // Handle categories if provided
        if (isset($input['categories']) && is_array($input['categories'])) {
            foreach ($input['categories'] as $categoryName) {
                // Find or create category
                $category = $db->fetchOne("SELECT id FROM categories WHERE name = ?", [$categoryName]);
                if (!$category) {
                    $categoryId = $db->insert('categories', ['name' => $categoryName]);
                } else {
                    $categoryId = $category['id'];
                }

                // Link contact to category
                $db->insert('contact_categories', [
                    'contact_id' => $contactId,
                    'category_id' => $categoryId
                ]);
            }
        }

        echo json_encode([
            'success' => true,
            'contact_id' => $contactId,
            'message' => 'Contact created successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create contact: ' . $e->getMessage()]);
    }
}

function handleUpdateContact($db) {
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $contactId = trim($pathInfo, '/');

        if (!$contactId) {
            http_response_code(400);
            echo json_encode(['error' => 'Contact ID is required']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            http_response_code(400);
            echo json_encode(['error' => 'Invalid input data']);
            return;
        }

        // Build update data
        $updateData = [];
        $allowedFields = [
            'name', 'phone', 'email', 'address', 'city', 'state', 'country',
            'postal_code', 'company', 'job_title', 'website', 'notes',
            'build_type', 'budget_range', 'project_status', 'source',
            'is_favorite', 'is_pinned', 'is_shortlisted', 'date'
        ];

        foreach ($allowedFields as $field) {
            if (isset($input[$field])) {
                $updateData[$field] = $input[$field];
            }
        }

        if (empty($updateData)) {
            http_response_code(400);
            echo json_encode(['error' => 'No valid fields to update']);
            return;
        }

        // Update contact
        $sql = "UPDATE contacts SET " . implode(' = ?, ', array_keys($updateData)) . " = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $params = array_values($updateData);
        $params[] = $contactId;

        $db->query($sql, $params);

        echo json_encode([
            'success' => true,
            'message' => 'Contact updated successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update contact: ' . $e->getMessage()]);
    }
}

function handleDeleteContact($db) {
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $contactId = trim($pathInfo, '/');

        if (!$contactId) {
            http_response_code(400);
            echo json_encode(['error' => 'Contact ID is required']);
            return;
        }

        // Delete contact (cascade will handle related records)
        $db->query("DELETE FROM contacts WHERE id = ?", [$contactId]);

        echo json_encode([
            'success' => true,
            'message' => 'Contact deleted successfully'
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete contact: ' . $e->getMessage()]);
    }
}
?>