<?php
/**
 * Contacts API - Handle contact CRUD operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database/config.php';

try {
    $db = Database::getInstance();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetContacts($db);
            break;
        case 'POST':
            handleCreateContact($db);
            break;
        case 'PUT':
            handleUpdateContact($db);
            break;
        case 'DELETE':
            handleDeleteContact($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGetContacts($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
    $category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';
    $state = isset($_GET['state']) ? sanitizeInput($_GET['state']) : '';
    $city = isset($_GET['city']) ? sanitizeInput($_GET['city']) : '';
    $filter = isset($_GET['filter']) ? sanitizeInput($_GET['filter']) : '';
    
    $offset = ($page - 1) * $limit;
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    if ($search) {
        $whereConditions[] = "(c.name LIKE :search OR c.phone LIKE :search OR c.email LIKE :search)";
        $params['search'] = "%$search%";
    }
    
    if ($category) {
        $whereConditions[] = "cc.category_id = :category";
        $params['category'] = $category;
    }
    
    if ($state) {
        $whereConditions[] = "c.state = :state";
        $params['state'] = $state;
    }
    
    if ($city) {
        $whereConditions[] = "c.city = :city";
        $params['city'] = $city;
    }
    
    // Handle filters
    switch ($filter) {
        case 'favorites':
            $whereConditions[] = "c.is_favorite = 1";
            break;
        case 'pinned':
            $whereConditions[] = "c.is_pinned = 1";
            break;
        case 'shortlisted':
            $whereConditions[] = "c.is_shortlisted = 1";
            break;
        case 'reminders':
            $whereConditions[] = "c.has_active_reminder = 1";
            break;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get contacts with categories
    $sql = "
        SELECT 
            c.*,
            GROUP_CONCAT(cat.name) as categories,
            GROUP_CONCAT(cat.color) as category_colors,
            COUNT(DISTINCT m.id) as message_count,
            COUNT(DISTINCT r.id) as reminder_count,
            COUNT(DISTINCT f.id) as file_count
        FROM contacts c
        LEFT JOIN contact_categories cc ON c.id = cc.contact_id
        LEFT JOIN categories cat ON cc.category_id = cat.id
        LEFT JOIN whatsapp_messages m ON c.id = m.contact_id
        LEFT JOIN reminders r ON c.id = r.contact_id
        LEFT JOIN contact_files cf ON c.id = cf.contact_id
        LEFT JOIN files f ON cf.file_id = f.id
        $whereClause
        GROUP BY c.id
        ORDER BY c.created_at DESC
        LIMIT :limit OFFSET :offset
    ";
    
    $params['limit'] = $limit;
    $params['offset'] = $offset;
    
    $contacts = $db->fetchAll($sql, $params);
    
    // Get total count
    $countSql = "
        SELECT COUNT(DISTINCT c.id) as total
        FROM contacts c
        LEFT JOIN contact_categories cc ON c.id = cc.contact_id
        $whereClause
    ";
    
    $totalResult = $db->fetchOne($countSql, array_slice($params, 0, -2));
    $total = $totalResult['total'];
    
    // Process categories
    foreach ($contacts as &$contact) {
        if ($contact['categories']) {
            $categoryNames = explode(',', $contact['categories']);
            $categoryColors = explode(',', $contact['category_colors']);
            $contact['category_list'] = array_map(function($name, $color) {
                return ['name' => $name, 'color' => $color];
            }, $categoryNames, $categoryColors);
        } else {
            $contact['category_list'] = [];
        }
        unset($contact['categories'], $contact['category_colors']);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $contacts,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleCreateContact($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON data']);
        return;
    }
    
    // Validate required fields
    if (empty($input['name']) || empty($input['phone'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Name and phone are required']);
        return;
    }
    
    // Validate phone number
    if (!validatePhone($input['phone'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid phone number']);
        return;
    }
    
    // Check if phone already exists
    $existing = $db->fetchOne("SELECT id FROM contacts WHERE phone = :phone", ['phone' => $input['phone']]);
    if ($existing) {
        http_response_code(400);
        echo json_encode(['error' => 'Phone number already exists']);
        return;
    }
    
    // Prepare contact data
    $contactData = [
        'name' => sanitizeInput($input['name']),
        'phone' => sanitizeInput($input['phone']),
        'email' => isset($input['email']) ? sanitizeInput($input['email']) : null,
        'address' => isset($input['address']) ? sanitizeInput($input['address']) : null,
        'city' => isset($input['city']) ? sanitizeInput($input['city']) : null,
        'state' => isset($input['state']) ? sanitizeInput($input['state']) : null,
        'country' => isset($input['country']) ? sanitizeInput($input['country']) : 'India',
        'postal_code' => isset($input['postal_code']) ? sanitizeInput($input['postal_code']) : null,
        'company' => isset($input['company']) ? sanitizeInput($input['company']) : null,
        'job_title' => isset($input['job_title']) ? sanitizeInput($input['job_title']) : null,
        'website' => isset($input['website']) ? sanitizeInput($input['website']) : null,
        'notes' => isset($input['notes']) ? sanitizeInput($input['notes']) : null,
        'build_type' => isset($input['build_type']) ? sanitizeInput($input['build_type']) : 'Residential',
        'budget_range' => isset($input['budget_range']) ? sanitizeInput($input['budget_range']) : null,
        'project_status' => isset($input['project_status']) ? sanitizeInput($input['project_status']) : 'Prospect',
        'source' => isset($input['source']) ? sanitizeInput($input['source']) : null,
        'is_favorite' => isset($input['is_favorite']) ? (bool)$input['is_favorite'] : false,
        'is_pinned' => isset($input['is_pinned']) ? (bool)$input['is_pinned'] : false,
        'is_shortlisted' => isset($input['is_shortlisted']) ? (bool)$input['is_shortlisted'] : false,
        'has_active_reminder' => isset($input['has_active_reminder']) ? (bool)$input['has_active_reminder'] : false,
        'date' => isset($input['date']) ? $input['date'] : null
    ];
    
    try {
        // Log the data being inserted for debugging
        error_log("Inserting contact data: " . json_encode($contactData));

        // Insert contact
        $contactId = $db->insert('contacts', $contactData);

        if (!$contactId) {
            throw new Exception("Failed to insert contact - no ID returned");
        }

        // Handle categories
        if (isset($input['categories']) && is_array($input['categories'])) {
            foreach ($input['categories'] as $categoryName) {
                // Get or create category
                $category = $db->fetchOne("SELECT id FROM categories WHERE name = :name", ['name' => $categoryName]);
                if (!$category) {
                    $categoryId = $db->insert('categories', [
                        'name' => $categoryName,
                        'color' => '#3B82F6'
                    ]);
                } else {
                    $categoryId = $category['id'];
                }

                // Link contact to category
                $db->insert('contact_categories', [
                    'contact_id' => $contactId,
                    'category_id' => $categoryId
                ]);
            }
        }

        // Log activity
        logActivity('create', 'contact', $contactId, "Created contact: {$contactData['name']}");

        echo json_encode([
            'success' => true,
            'message' => 'Contact created successfully',
            'data' => ['id' => $contactId]
        ]);

    } catch (Exception $e) {
        error_log("Error creating contact: " . $e->getMessage());
        error_log("Contact data: " . json_encode($contactData));
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to create contact: ' . $e->getMessage()
        ]);
    }
}

function handleUpdateContact($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Contact ID is required']);
        return;
    }
    
    $contactId = (int)$input['id'];
    
    // Check if contact exists
    $existing = $db->fetchOne("SELECT id FROM contacts WHERE id = :id", ['id' => $contactId]);
    if (!$existing) {
        http_response_code(404);
        echo json_encode(['error' => 'Contact not found']);
        return;
    }
    
    // Prepare update data
    $updateData = [];
    $allowedFields = ['name', 'email', 'address', 'city', 'state', 'country', 'postal_code', 
                     'company', 'job_title', 'website', 'notes', 'build_type', 'budget_range', 
                     'project_status', 'source', 'is_favorite', 'is_pinned', 'is_shortlisted', 
                     'has_active_reminder', 'date'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateData[$field] = sanitizeInput($input[$field]);
        }
    }
    
    if (!empty($updateData)) {
        $db->update('contacts', $updateData, 'id = :id', ['id' => $contactId]);
    }
    
    // Handle categories update
    if (isset($input['categories']) && is_array($input['categories'])) {
        // Remove existing categories
        $db->delete('contact_categories', 'contact_id = :contact_id', ['contact_id' => $contactId]);
        
        // Add new categories
        foreach ($input['categories'] as $categoryName) {
            $category = $db->fetchOne("SELECT id FROM categories WHERE name = :name", ['name' => $categoryName]);
            if (!$category) {
                $categoryId = $db->insert('categories', [
                    'name' => $categoryName,
                    'color' => '#3B82F6'
                ]);
            } else {
                $categoryId = $category['id'];
            }
            
            $db->insert('contact_categories', [
                'contact_id' => $contactId,
                'category_id' => $categoryId
            ]);
        }
    }
    
    // Log activity
    logActivity('update', 'contact', $contactId, "Updated contact");
    
    echo json_encode([
        'success' => true,
        'message' => 'Contact updated successfully'
    ]);
}

function handleDeleteContact($db) {
    $contactId = isset($_GET['id']) ? (int)$_GET['id'] : null;
    
    if (!$contactId) {
        http_response_code(400);
        echo json_encode(['error' => 'Contact ID is required']);
        return;
    }
    
    // Check if contact exists
    $existing = $db->fetchOne("SELECT name FROM contacts WHERE id = :id", ['id' => $contactId]);
    if (!$existing) {
        http_response_code(404);
        echo json_encode(['error' => 'Contact not found']);
        return;
    }
    
    // Delete contact (cascade will handle related records)
    $db->delete('contacts', 'id = :id', ['id' => $contactId]);
    
    // Log activity
    logActivity('delete', 'contact', $contactId, "Deleted contact: {$existing['name']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Contact deleted successfully'
    ]);
}
?> 