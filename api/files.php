<?php
/**
 * Files API - Handle file upload, management, and database operations
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database/config.php';

try {
    $db = Database::getInstance();
    
    // Ensure required tables exist
    createTablesIfNotExist($db);
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetFiles($db);
            break;
        case 'POST':
            handleUploadFile($db);
            break;
        case 'DELETE':
            handleDeleteFile($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
}

function createTablesIfNotExist($db) {
    try {
        // Create files table if it doesn't exist
        $db->query("
            CREATE TABLE IF NOT EXISTS files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                file_type ENUM('image', 'video', 'document', 'audio', 'other') NOT NULL,
                folder VARCHAR(100) DEFAULT 'Default',
                uploaded_by INT DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_file_type (file_type),
                INDEX idx_folder (folder)
            )
        ");
        
        // Create contact_files table if it doesn't exist
        $db->query("
            CREATE TABLE IF NOT EXISTS contact_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                contact_id INT NOT NULL,
                file_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_contact_file (contact_id, file_id)
            )
        ");
        
    } catch (Exception $e) {
        error_log("Error creating tables: " . $e->getMessage());
    }
}

function handleGetFiles($db) {
    try {
        // First, check if files table exists
        $tableExists = $db->fetchOne("SHOW TABLES LIKE 'files'");
        if (!$tableExists) {
            // Table doesn't exist, return empty array
            echo json_encode([
                'success' => true,
                'files' => [],
                'message' => 'Files table created, no files yet'
            ]);
            return;
        }

        $folder = $_GET['folder'] ?? null;

        if ($folder) {
            // Get files from specific folder
            $sql = "SELECT * FROM files WHERE folder = ? ORDER BY created_at DESC";
            $files = $db->fetchAll($sql, [$folder]);
        } else {
            // Get all files grouped by folder
            $sql = "SELECT * FROM files ORDER BY folder, created_at DESC";
            $files = $db->fetchAll($sql);
        }
        
        // Format files for frontend
        $formattedFiles = array_map(function($file) {
            return [
                'id' => (string)$file['id'],
                'name' => $file['original_name'],
                'filename' => $file['filename'],
                'path' => $file['file_path'],
                'size' => (int)$file['file_size'],
                'type' => $file['file_type'],
                'mimeType' => $file['mime_type'],
                'folder' => $file['folder'] ?? 'Default',
                'uploadedAt' => $file['created_at'],
                'url' => '../' . $file['file_path'] // Relative URL for frontend
            ];
        }, $files);
        
        if ($folder) {
            echo json_encode([
                'success' => true,
                'files' => $formattedFiles,
                'folder' => $folder
            ]);
        } else {
            // Group files by folder
            $groupedFiles = [];
            foreach ($formattedFiles as $file) {
                $folderName = $file['folder'];
                if (!isset($groupedFiles[$folderName])) {
                    $groupedFiles[$folderName] = [];
                }
                $groupedFiles[$folderName][] = $file;
            }
            
            echo json_encode([
                'success' => true,
                'files' => $groupedFiles
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to fetch files: ' . $e->getMessage()]);
    }
}

function handleUploadFile($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input || !isset($input['filename']) || !isset($input['data'])) {
            http_response_code(400);
            echo json_encode(['error' => 'Filename and data are required']);
            return;
        }
        
        $originalName = $input['filename'];
        $base64Data = $input['data'];
        $folder = $input['folder'] ?? 'Default';
        $contactId = $input['contactId'] ?? null;
        
        // Remove data URL prefix if present
        if (strpos($base64Data, 'data:') === 0) {
            $base64Data = substr($base64Data, strpos($base64Data, ',') + 1);
        }
        
        // Decode base64 data
        $fileData = base64_decode($base64Data);
        if ($fileData === false) {
            throw new Exception('Invalid base64 data');
        }
        
        // Generate unique filename
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $uniqueFilename = uniqid() . '_' . time() . '.' . $extension;
        
        // Determine file type and MIME type
        $mimeType = $input['mimeType'] ?? 'application/octet-stream';
        $fileType = determineFileType($mimeType, $extension);
        
        // Create upload directory structure
        $uploadDir = '../uploads/' . $folder . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $filePath = $uploadDir . $uniqueFilename;
        $relativePath = 'uploads/' . $folder . '/' . $uniqueFilename;
        
        // Save file to disk
        if (file_put_contents($filePath, $fileData) === false) {
            throw new Exception('Failed to save file to disk');
        }
        
        // Save file metadata to database
        $fileData = [
            'filename' => $uniqueFilename,
            'original_name' => $originalName,
            'file_path' => $relativePath,
            'file_size' => strlen($fileData),
            'mime_type' => $mimeType,
            'file_type' => $fileType,
            'folder' => $folder
        ];
        
        $fileId = $db->insert('files', $fileData);
        
        // Link to contact if provided
        if ($contactId) {
            try {
                $db->insert('contact_files', [
                    'contact_id' => $contactId,
                    'file_id' => $fileId
                ]);
            } catch (Exception $e) {
                // Ignore duplicate key errors
                if (strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'file' => [
                'id' => (string)$fileId,
                'name' => $originalName,
                'filename' => $uniqueFilename,
                'path' => $relativePath,
                'size' => strlen($fileData),
                'type' => $fileType,
                'mimeType' => $mimeType,
                'folder' => $folder,
                'url' => '../' . $relativePath
            ],
            'message' => 'File uploaded successfully'
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to upload file: ' . $e->getMessage()]);
    }
}

function handleDeleteFile($db) {
    try {
        $pathInfo = $_SERVER['PATH_INFO'] ?? '';
        $fileId = trim($pathInfo, '/');
        
        if (!$fileId) {
            http_response_code(400);
            echo json_encode(['error' => 'File ID is required']);
            return;
        }
        
        // Get file info before deletion
        $file = $db->fetchOne("SELECT * FROM files WHERE id = ?", [$fileId]);
        
        if (!$file) {
            http_response_code(404);
            echo json_encode(['error' => 'File not found']);
            return;
        }
        
        // Delete file from disk
        $filePath = '../' . $file['file_path'];
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        
        // Delete from database (cascade will handle contact_files)
        $db->query("DELETE FROM files WHERE id = ?", [$fileId]);
        
        echo json_encode([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to delete file: ' . $e->getMessage()]);
    }
}

function determineFileType($mimeType, $extension) {
    $extension = strtolower($extension);
    
    if (strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
        return 'image';
    } elseif (strpos($mimeType, 'video/') === 0 || in_array($extension, ['mp4', 'avi', 'mov', 'mkv', 'webm'])) {
        return 'video';
    } elseif (strpos($mimeType, 'audio/') === 0 || in_array($extension, ['mp3', 'wav', 'ogg', 'm4a'])) {
        return 'audio';
    } elseif (in_array($extension, ['pdf', 'doc', 'docx', 'txt', 'rtf'])) {
        return 'document';
    } else {
        return 'other';
    }
}
?>
