<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../database/config.php';

// Get database instance
try {
    $db = Database::getInstance();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Helper function to log activities
function logActivity($action, $entity_type, $entity_id, $description) {
    global $db;
    try {
        $db->execute(
            "INSERT INTO activity_log (action, entity_type, entity_id, description, created_at) VALUES (?, ?, ?, ?, NOW())",
            [$action, $entity_type, $entity_id, $description]
        );
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Helper function to determine file type
function getFileType($mimeType) {
    if (strpos($mimeType, 'image/') === 0) return 'image';
    if (strpos($mimeType, 'video/') === 0) return 'video';
    if (strpos($mimeType, 'audio/') === 0) return 'audio';
    if (in_array($mimeType, ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'])) return 'document';
    return 'other';
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    // Get files with optional filtering
    $folderId = $_GET['folder_id'] ?? null;
    $contactId = $_GET['contact_id'] ?? null;
    $fileType = $_GET['file_type'] ?? null;
    $limit = (int)($_GET['limit'] ?? 50);
    $offset = (int)($_GET['offset'] ?? 0);
    
    try {
        $whereConditions = [];
        $params = [];
        
        if ($folderId) {
            $whereConditions[] = "f.folder_id = ?";
            $params[] = $folderId;
        }
        
        if ($contactId) {
            $whereConditions[] = "EXISTS (SELECT 1 FROM contact_files cf WHERE cf.file_id = f.id AND cf.contact_id = ?)";
            $params[] = $contactId;
        }
        
        if ($fileType) {
            $whereConditions[] = "f.file_type = ?";
            $params[] = $fileType;
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        $query = "
            SELECT 
                f.*,
                COUNT(cf.contact_id) as contact_count,
                COUNT(ma.message_id) as message_count
            FROM files f
            LEFT JOIN contact_files cf ON f.id = cf.file_id
            LEFT JOIN message_attachments ma ON f.id = ma.file_id
            $whereClause
            GROUP BY f.id
            ORDER BY f.created_at DESC
            LIMIT ? OFFSET ?
        ";
        
        $params[] = $limit;
        $params[] = $offset;
        
        $files = $db->fetchAll($query, $params);
        
        // Get total count
        $countQuery = "SELECT COUNT(DISTINCT f.id) as total FROM files f " . 
                     (!empty($whereConditions) ? 'WHERE ' . implode(' AND ', array_slice($whereConditions, 0, count($whereConditions) - 2)) : '');
        $countParams = array_slice($params, 0, count($params) - 2);
        $totalResult = $db->fetchOne($countQuery, $countParams);
        $total = $totalResult['total'] ?? 0;
        
        echo json_encode([
            'success' => true,
            'files' => $files,
            'pagination' => [
                'total' => (int)$total,
                'limit' => $limit,
                'offset' => $offset,
                'has_more' => ($offset + $limit) < $total
            ]
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }

} elseif ($method === 'POST') {
    // Save file metadata to database
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON input']);
        exit;
    }
    
    $filename = $input['filename'] ?? '';
    $originalName = $input['original_name'] ?? '';
    $filePath = $input['file_path'] ?? '';
    $fileSize = $input['file_size'] ?? 0;
    $mimeType = $input['mime_type'] ?? '';
    $contactId = $input['contact_id'] ?? null;
    
    if (!$filename || !$originalName || !$filePath || !$mimeType) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields: filename, original_name, file_path, mime_type']);
        exit;
    }
    
    try {
        $fileType = getFileType($mimeType);
        
        // Insert file record
        $fileId = $db->execute(
            "INSERT INTO files (filename, original_name, file_path, file_size, mime_type, file_type, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, NOW())",
            [$filename, $originalName, $filePath, $fileSize, $mimeType, $fileType]
        );
        
        // Link to contact if provided
        if ($contactId) {
            $db->execute(
                "INSERT INTO contact_files (contact_id, file_id, created_at) VALUES (?, ?, NOW())",
                [$contactId, $fileId]
            );
        }
        
        // Get the created file
        $file = $db->fetchOne("SELECT * FROM files WHERE id = ?", [$fileId]);
        
        // Log activity
        logActivity('create', 'file', $fileId, "Uploaded file: {$originalName}");
        
        echo json_encode([
            'success' => true,
            'file' => $file,
            'message' => 'File saved to database successfully'
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }

} elseif ($method === 'DELETE') {
    // Delete file
    $fileId = $_GET['id'] ?? null;
    
    if (!$fileId) {
        http_response_code(400);
        echo json_encode(['error' => 'File ID is required']);
        exit;
    }
    
    try {
        // Get file info before deletion
        $file = $db->fetchOne("SELECT * FROM files WHERE id = ?", [$fileId]);
        
        if (!$file) {
            http_response_code(404);
            echo json_encode(['error' => 'File not found']);
            exit;
        }
        
        // Delete file record (cascading will handle related records)
        $db->execute("DELETE FROM files WHERE id = ?", [$fileId]);
        
        // Log activity
        logActivity('delete', 'file', $fileId, "Deleted file: {$file['original_name']}");
        
        echo json_encode([
            'success' => true,
            'message' => 'File deleted from database successfully',
            'file_path' => $file['file_path'] // Return path so backend can delete physical file
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }

} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
}
?>
