
import React, { useState } from 'react';
import { X, Calendar, MapPin, Tag } from 'lucide-react';
import { Contact } from '../types/Contact';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';

interface FilterOptions {
  states: string[];
  cities: string[];
  categories: string[];
  buildTypes: string[];
  showFavoriteOnly: boolean;
  showPinnedOnly: boolean;
  showShortlistedOnly: boolean;
  dateRange?: { start: string; end: string };
}

interface FilterPanelProps {
  contacts: Contact[];
  onClose: () => void;
  onApplyFilters: (filters: FilterOptions) => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  contacts,
  onClose,
  onApplyFilters
}) => {
  const [filters, setFilters] = useState<FilterOptions>({
    states: [],
    cities: [],
    categories: [],
    buildTypes: [],
    showFavoriteOnly: false,
    showPinnedOnly: false,
    showShortlistedOnly: false
  });

  // Get unique values for filters
  const uniqueStates = Array.from(new Set(contacts.map(c => c.state))).sort();
  const uniqueCities = Array.from(new Set(contacts.map(c => c.city))).sort();
  const uniqueCategories = Array.from(new Set(contacts.flatMap(c => c.categories))).sort();
  const uniqueBuildTypes = Array.from(new Set(contacts.map(c => c.buildType))).sort();

  const handleStateChange = (state: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      states: checked
        ? [...prev.states, state]
        : prev.states.filter(s => s !== state)
    }));
  };

  const handleCityChange = (city: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      cities: checked
        ? [...prev.cities, city]
        : prev.cities.filter(c => c !== city)
    }));
  };

  const handleCategoryChange = (category: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      categories: checked
        ? [...prev.categories, category]
        : prev.categories.filter(c => c !== category)
    }));
  };

  const handleBuildTypeChange = (buildType: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      buildTypes: checked
        ? [...prev.buildTypes, buildType]
        : prev.buildTypes.filter(b => b !== buildType)
    }));
  };

  const clearAllFilters = () => {
    setFilters({
      states: [],
      cities: [],
      categories: [],
      buildTypes: [],
      showFavoriteOnly: false,
      showPinnedOnly: false,
      showShortlistedOnly: false
    });
  };

  const applyFilters = () => {
    onApplyFilters(filters);
  };

  return (
    <Card className="mb-8 animate-fade-in">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Advanced Filters</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Status Filters */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Status
            </h4>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.showFavoriteOnly}
                  onCheckedChange={(checked) =>
                    setFilters(prev => ({ ...prev, showFavoriteOnly: !!checked }))
                  }
                />
                <span className="text-sm">Favorites only</span>
              </label>
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.showPinnedOnly}
                  onCheckedChange={(checked) =>
                    setFilters(prev => ({ ...prev, showPinnedOnly: !!checked }))
                  }
                />
                <span className="text-sm">Pinned only</span>
              </label>
              <label className="flex items-center space-x-2">
                <Checkbox
                  checked={filters.showShortlistedOnly}
                  onCheckedChange={(checked) =>
                    setFilters(prev => ({ ...prev, showShortlistedOnly: !!checked }))
                  }
                />
                <span className="text-sm">Shortlisted only</span>
              </label>
            </div>
          </div>

          {/* Location Filters */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Location
            </h4>
            <div className="space-y-3">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">States</h5>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {uniqueStates.map(state => (
                    <label key={state} className="flex items-center space-x-2">
                      <Checkbox
                        checked={filters.states.includes(state)}
                        onCheckedChange={(checked) => handleStateChange(state, !!checked)}
                      />
                      <span className="text-sm">{state}</span>
                    </label>
                  ))}
                </div>
              </div>
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Cities</h5>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {uniqueCities.map(city => (
                    <label key={city} className="flex items-center space-x-2">
                      <Checkbox
                        checked={filters.cities.includes(city)}
                        onCheckedChange={(checked) => handleCityChange(city, !!checked)}
                      />
                      <span className="text-sm">{city}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Category Filters */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Categories
            </h4>
            <div className="max-h-48 overflow-y-auto space-y-1">
              {uniqueCategories.map(category => (
                <label key={category} className="flex items-center space-x-2">
                  <Checkbox
                    checked={filters.categories.includes(category)}
                    onCheckedChange={(checked) => handleCategoryChange(category, !!checked)}
                  />
                  <span className="text-sm">{category}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Build Type Filters */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Build Types
            </h4>
            <div className="max-h-48 overflow-y-auto space-y-1">
              {uniqueBuildTypes.map(buildType => (
                <label key={buildType} className="flex items-center space-x-2">
                  <Checkbox
                    checked={filters.buildTypes.includes(buildType)}
                    onCheckedChange={(checked) => handleBuildTypeChange(buildType, !!checked)}
                  />
                  <span className="text-sm">{buildType}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Date Range Filter */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Date Range
          </h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">From</label>
              <Input
                type="date"
                value={filters.dateRange?.start || ''}
                onChange={(e) =>
                  setFilters(prev => ({
                    ...prev,
                    dateRange: { ...prev.dateRange, start: e.target.value, end: prev.dateRange?.end || '' }
                  }))
                }
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
              <Input
                type="date"
                value={filters.dateRange?.end || ''}
                onChange={(e) =>
                  setFilters(prev => ({
                    ...prev,
                    dateRange: { start: prev.dateRange?.start || '', end: e.target.value }
                  }))
                }
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-6 pt-6 border-t border-gray-200">
          <Button variant="outline" onClick={clearAllFilters}>
            Clear All
          </Button>
          <Button onClick={applyFilters} className="bg-blue-600 hover:bg-blue-700">
            Apply Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
