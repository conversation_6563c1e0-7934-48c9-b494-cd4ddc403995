import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Contact } from '../types/Contact';
import { Star, Pin, CheckCircle, MessageCircle, Paperclip, Phone } from 'lucide-react';
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';
import { RequirementsHoverPopup } from './RequirementsHoverPopup';

interface KanbanBoardProps {
  contacts: Contact[];
  onContactClick: (contact: Contact) => void;
  onUpdateContact: (contact: Contact) => void;
  isCompactView?: boolean;
  onAddToWhatsAppSender?: (contact: Contact) => void;
  onOpenWhatsAppSenderWithContact?: (contact: Contact) => void;
  contactMessageCounts?: Map<string, number>;
}

const stages = [
  { id: 'lead', title: 'Lead', color: 'bg-blue-100' },
  { id: 'contacted', title: 'Contacted', color: 'bg-yellow-100' },
  { id: 'profile-sent', title: 'Profile Sent', color: 'bg-purple-100' },
  { id: 'followup', title: 'Followup', color: 'bg-orange-100' },
  { id: 'meeting-done', title: 'Meeting Done', color: 'bg-indigo-100' },
  { id: 'client-interested', title: 'Client Interested', color: 'bg-green-100' },
  { id: 'advance-received', title: 'Advance Received', color: 'bg-emerald-100' }
];

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
  contacts,
  onContactClick,
  onUpdateContact,
  isCompactView = false,
  onAddToWhatsAppSender,
  onOpenWhatsAppSenderWithContact,
  contactMessageCounts
}) => {
  const handleContactNameClick = (e: React.MouseEvent, contact: Contact) => {
    // Check if the click target is a button or inside a button
    const target = e.target as HTMLElement;
    const isButton = target.closest('button') || target.tagName === 'BUTTON';
    
    console.log('KanbanBoard - Contact name clicked:', {
      target: target,
      targetTagName: target.tagName,
      targetClassName: target.className,
      isButton: isButton,
      closestButton: target.closest('button')
    });
    
    if (!isButton) {
      console.log('KanbanBoard - Opening contact details for:', contact.name);
      onContactClick(contact);
    } else {
      console.log('KanbanBoard - Button clicked, not opening contact details');
    }
  };

  const getContactsByStage = (stage: string) => {
    return contacts.filter(contact => (contact.kanbanStage || 'lead') === stage);
  };

  const handleDragStart = (e: React.DragEvent, contact: Contact) => {
    e.dataTransfer.setData('text/plain', contact.id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, newStage: string) => {
    e.preventDefault();
    const contactId = e.dataTransfer.getData('text/plain');
    const contact = contacts.find(c => c.id === contactId);
    if (contact) {
      const updatedContact = { ...contact, kanbanStage: newStage as Contact['kanbanStage'] };
      onUpdateContact(updatedContact);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-yellow-100 text-yellow-800',
    ];
    const index = category.length % colors.length;
    return colors[index];
  };

  const toggleFavorite = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 KanbanBoard toggleFavorite START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 KanbanBoard toggleFavorite - About to update contact');
    onUpdateContact({ ...contact, isFavorite: !contact.isFavorite });
    console.log('🔴 KanbanBoard toggleFavorite END');
  };

  const togglePin = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 KanbanBoard togglePin START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 KanbanBoard togglePin - About to update contact');
    onUpdateContact({ ...contact, isPinned: !contact.isPinned });
    console.log('🔴 KanbanBoard togglePin END');
  };

  const toggleShortlist = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 KanbanBoard toggleShortlist START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 KanbanBoard toggleShortlist - About to update contact');
    onUpdateContact({ ...contact, isShortlisted: !contact.isShortlisted });
    console.log('🔴 KanbanBoard toggleShortlist END');
  };

  const openWhatsApp = (phone: string, e: React.MouseEvent, contact?: Contact) => {
    e.preventDefault();
    e.stopPropagation();
    if (onOpenWhatsAppSenderWithContact && contact) {
      onOpenWhatsAppSenderWithContact(contact);
    } else if (onAddToWhatsAppSender && contact) {
      onAddToWhatsAppSender(contact);
    } else {
      // Fallback to opening WhatsApp directly
      const cleanPhone = phone.replace(/[^\d]/g, '');
      window.open(`https://wa.me/${cleanPhone}`, '_blank');
    }
  };

  return (
    <TooltipProvider>
      <div className="flex gap-2 h-full overflow-x-auto">
        {stages.map((stage) => (
          <div
            key={stage.id}
            className={`flex-shrink-0 ${isCompactView ? 'w-44' : 'w-72'}`}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, stage.id)}
          >
            <Card className={`h-full ${stage.color}`}>
              <CardHeader className="pb-2">
                <CardTitle className={`${isCompactView ? 'text-xs' : 'text-sm'} font-semibold flex items-center justify-between`}>
                  <span className="truncate">{stage.title}</span>
                  <Badge variant="secondary" className="text-xs">
                    {getContactsByStage(stage.id).length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className={`space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto ${isCompactView ? 'p-2' : ''}`}>
                {getContactsByStage(stage.id).map((contact) => (
                  <TooltipProvider key={contact.id}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Card
                          className={`hover:shadow-md transition-all duration-200 ${
                            contact.hasActiveReminder ? 'animate-pulse border-orange-400 bg-orange-50' : ''
                          } ${isCompactView ? 'p-2' : ''}`}
                          draggable
                          onDragStart={(e) => handleDragStart(e, contact)}
                        >
                          <CardContent className={isCompactView ? 'p-2' : 'p-3'}>
                            {isCompactView ? (
                              <div>
                                {/* Contact Info - Separate clickable area */}
                                <div 
                                  className="flex items-center justify-between mb-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded p-1 transition-colors"
                                  onClick={(e) => {
                                    console.log('🟢 KanbanBoard contact info area clicked - Contact:', contact.name);
                                    console.log('🟢 Event target:', e.target);
                                    console.log('🟢 Event currentTarget:', e.currentTarget);
                                    onContactClick(contact);
                                  }}
                                >
                                  <RequirementsHoverPopup contact={contact}>
                                    <h4 className="font-semibold text-xs truncate flex-1 mr-1 cursor-help">
                                      {contact.name}
                                      {contactMessageCounts && contactMessageCounts.get(contact.phone) && (
                                        <span className="ml-1 inline-block bg-green-100 text-green-700 text-xs px-1 rounded">
                                          {contactMessageCounts.get(contact.phone)}
                                        </span>
                                      )}
                                    </h4>
                                  </RequirementsHoverPopup>
                                </div>
                                
                                {/* Action Buttons - Completely separate area */}
                                <div className="flex items-center justify-between">
                                  <div className="text-xs text-gray-600 truncate flex-1">
                                    {contact.phone}
                                  </div>
                                  <div className="flex gap-1">
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Star 
                                          className={`w-2 h-2 cursor-pointer transition-all duration-200 ${
                                            contact.isFavorite 
                                              ? 'text-yellow-500 fill-current hover:text-yellow-600' 
                                              : 'text-gray-400 hover:text-yellow-500'
                                          }`}
                                          onClick={(e) => toggleFavorite(e, contact)}
                                        />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{contact.isFavorite ? 'Remove from favorites' : 'Add to favorites'}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Pin 
                                          className={`w-2 h-2 cursor-pointer transition-all duration-200 ${
                                            contact.isPinned 
                                              ? 'text-blue-500 fill-current hover:text-blue-600' 
                                              : 'text-gray-400 hover:text-blue-500'
                                          }`}
                                          onClick={(e) => togglePin(e, contact)}
                                        />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{contact.isPinned ? 'Unpin contact' : 'Pin contact'}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <CheckCircle 
                                          className={`w-2 h-2 cursor-pointer transition-all duration-200 ${
                                            contact.isShortlisted 
                                              ? 'text-green-500 fill-current hover:text-green-600' 
                                              : 'text-gray-400 hover:text-green-500'
                                          }`}
                                          onClick={(e) => toggleShortlist(e, contact)}
                                        />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{contact.isShortlisted ? 'Remove from shortlist' : 'Add to shortlist'}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <MessageCircle
                                        className="w-3 h-3 text-green-600 cursor-pointer hover:text-green-700"
                                        onClick={(e) => openWhatsApp(contact.phone, e)}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Send WhatsApp message</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </div>
                              </div>
                            ) : (
                              <>
                                {/* Contact Info - Separate clickable area */}
                                <div 
                                  className="flex items-start justify-between mb-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded p-2 transition-colors"
                                  onClick={(e) => {
                                    console.log('🟢 KanbanBoard contact info area clicked (detailed) - Contact:', contact.name);
                                    console.log('🟢 Event target:', e.target);
                                    console.log('🟢 Event currentTarget:', e.currentTarget);
                                    onContactClick(contact);
                                  }}
                                >
                                  <RequirementsHoverPopup contact={contact}>
                                    <h4 className="font-semibold text-sm truncate flex-1 mr-2 cursor-help">
                                      {contact.name}
                                      {contactMessageCounts && contactMessageCounts.get(contact.phone) && (
                                        <span className="ml-1 inline-block bg-green-100 text-green-700 text-xs px-1 rounded">
                                          {contactMessageCounts.get(contact.phone)}
                                        </span>
                                      )}
                                    </h4>
                                  </RequirementsHoverPopup>
                                </div>
                                
                                {/* Action Buttons - Completely separate area */}
                                <div className="flex items-center justify-center gap-1 mb-2">
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Star 
                                        className={`w-3 h-3 cursor-pointer transition-all duration-200 ${
                                          contact.isFavorite 
                                            ? 'text-yellow-500 fill-current hover:text-yellow-600' 
                                            : 'text-gray-400 hover:text-yellow-500'
                                        }`}
                                        onClick={(e) => toggleFavorite(e, contact)}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{contact.isFavorite ? 'Remove from favorites' : 'Add to favorites'}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <Pin 
                                        className={`w-3 h-3 cursor-pointer transition-all duration-200 ${
                                          contact.isPinned 
                                            ? 'text-blue-500 fill-current hover:text-blue-600' 
                                            : 'text-gray-400 hover:text-blue-500'
                                        }`}
                                        onClick={(e) => togglePin(e, contact)}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{contact.isPinned ? 'Unpin contact' : 'Pin contact'}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                  <Tooltip>
                                    <TooltipTrigger>
                                      <CheckCircle 
                                        className={`w-3 h-3 cursor-pointer transition-all duration-200 ${
                                          contact.isShortlisted 
                                            ? 'text-green-500 fill-current hover:text-green-600' 
                                            : 'text-gray-400 hover:text-green-500'
                                        }`}
                                        onClick={(e) => toggleShortlist(e, contact)}
                                      />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{contact.isShortlisted ? 'Remove from shortlist' : 'Add to shortlist'}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                  {contact.attachments.length > 0 && (
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <Paperclip className="w-3 h-3 text-gray-400" />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>Has attachments</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </div>
                                
                                <div className="text-xs text-gray-600 mb-2">
                                  <div className="flex items-center gap-1">
                                    <Phone className="w-3 h-3" />
                                    <span>{contact.phone}</span>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <MessageCircle
                                          className="w-3 h-3 text-green-600 cursor-pointer hover:text-green-700 ml-1"
                                          onClick={(e) => openWhatsApp(contact.phone, e, contact)}
                                        />
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>{onOpenWhatsAppSenderWithContact ? 'Send WhatsApp message to this contact' : onAddToWhatsAppSender ? 'Add to WhatsApp sender' : 'Send WhatsApp message'}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </div>
                                  <div>{contact.city}, {contact.state}</div>
                                  {contact.buildType && (
                                    <div className="font-medium text-purple-600">{contact.buildType}</div>
                                  )}
                                </div>

                                <p className="text-xs text-gray-700 mb-2 line-clamp-2 break-words">
                                  {contact.requirements}
                                </p>

                                <div className="flex flex-wrap gap-1 mb-2">
                                  {contact.categories.slice(0, 2).map((category) => (
                                    <Badge key={category} className={`text-xs px-1 py-0 ${getCategoryColor(category)}`}>
                                      {category}
                                    </Badge>
                                  ))}
                                  {contact.categories.length > 2 && (
                                    <Badge className="text-xs px-1 py-0 bg-gray-100 text-gray-600">
                                      +{contact.categories.length - 2}
                                    </Badge>
                                  )}
                                </div>

                                <div className="text-xs text-gray-500">
                                  {new Date(contact.date).toLocaleDateString()}
                                </div>
                              </>
                            )}
                          </CardContent>
                        </Card>
                      </TooltipTrigger>
                      {contact.hasActiveReminder && contact.reminders && contact.reminders.length > 0 && (
                        <TooltipContent>
                          <div className="max-w-xs">
                            <p className="font-semibold text-orange-600 mb-1">Active Reminders:</p>
                            {contact.reminders.map((reminder, index) => (
                              <div key={reminder.id} className="mb-1 last:mb-0">
                                <p className="font-medium">{reminder.title}</p>
                                <p className="text-xs text-gray-600">{reminder.date} at {reminder.time}</p>
                                {reminder.notes && <p className="text-xs italic">{reminder.notes}</p>}
                              </div>
                            ))}
                          </div>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                ))}
              </CardContent>
            </Card>
          </div>
        ))}
      </div>
    </TooltipProvider>
  );
};
