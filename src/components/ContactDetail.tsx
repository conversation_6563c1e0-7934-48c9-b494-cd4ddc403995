import React, { useState, useRef, useEffect } from 'react';
import { X, Upload, FileText, Image, Volume2, Download, Edit, Save, Bell, BellOff, Plus, MessageCircle } from 'lucide-react';
import { Contact, FileAttachment, Reminder } from '../types/Contact';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { getAllStates, getCitiesByState } from '../data/indianStatesAndCities';

interface ContactDetailProps {
  contact: Contact;
  onClose: () => void;
  onUpdate: (contact: Contact) => void;
  onDelete: (id: string) => void;
  onAddToWhatsAppSender?: (contact: Contact) => void;
  onOpenWhatsAppSenderWithContact?: (contact: Contact) => void;
}

export const ContactDetail: React.FC<ContactDetailProps> = ({
  contact,
  onClose,
  onUpdate,
  onDelete,
  onAddToWhatsAppSender,
  onOpenWhatsAppSenderWithContact
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContact, setEditedContact] = useState(contact);
  const [newNote, setNewNote] = useState('');
  const [showReminderForm, setShowReminderForm] = useState(false);
  const [newReminder, setNewReminder] = useState({
    title: '',
    date: '',
    time: '',
    notes: ''
  });
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [newCategory, setNewCategory] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get all Indian states
  const allStates = getAllStates();

  // Update cities when state changes
  useEffect(() => {
    if (editedContact.state) {
      const cities = getCitiesByState(editedContact.state);
      setAvailableCities(cities);
      
      // Clear city if it's not in the new state's cities
      if (editedContact.city && !cities.includes(editedContact.city)) {
        setEditedContact(prev => ({ ...prev, city: '' }));
      }
    } else {
      setAvailableCities([]);
      setEditedContact(prev => ({ ...prev, city: '' }));
    }
  }, [editedContact.state]);

  const handleSaveAndClose = () => {
    if (isEditing) {
      onUpdate(editedContact);
    }
    onClose();
  };

  const handleSave = () => {
    onUpdate(editedContact);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedContact(contact);
    setIsEditing(false);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newAttachments: FileAttachment[] = Array.from(files).map(file => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(file),
        uploadDate: new Date().toISOString()
      }));

      const updatedContact = {
        ...editedContact,
        attachments: [...editedContact.attachments, ...newAttachments]
      };
      
      setEditedContact(updatedContact);
      onUpdate(updatedContact);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    const updatedContact = {
      ...editedContact,
      attachments: editedContact.attachments.filter(att => att.id !== attachmentId)
    };
    setEditedContact(updatedContact);
    onUpdate(updatedContact);
  };

  const updateNotes = () => {
    if (newNote.trim()) {
      const updatedNotes = editedContact.notes 
        ? `${editedContact.notes}\n\n[${new Date().toLocaleString()}] ${newNote}`
        : `[${new Date().toLocaleString()}] ${newNote}`;
      
      const updatedContact = { ...editedContact, notes: updatedNotes };
      setEditedContact(updatedContact);
      onUpdate(updatedContact);
      setNewNote('');
    }
  };

  const addReminder = () => {
    if (newReminder.title && newReminder.date && newReminder.time) {
      const reminder: Reminder = {
        id: Date.now().toString(),
        ...newReminder
      };
      
      const updatedContact = {
        ...editedContact,
        reminders: [...(editedContact.reminders || []), reminder],
        hasActiveReminder: true
      };
      
      setEditedContact(updatedContact);
      onUpdate(updatedContact);
      setNewReminder({ title: '', date: '', time: '', notes: '' });
      setShowReminderForm(false);
    }
  };

  const removeReminder = (reminderId: string) => {
    const updatedReminders = editedContact.reminders?.filter(r => r.id !== reminderId) || [];
    const updatedContact = {
      ...editedContact,
      reminders: updatedReminders,
      hasActiveReminder: updatedReminders.length > 0
    };
    
    setEditedContact(updatedContact);
    onUpdate(updatedContact);
  };

  const stopAllReminders = () => {
    const updatedContact = {
      ...editedContact,
      hasActiveReminder: false
    };
    
    setEditedContact(updatedContact);
    onUpdate(updatedContact);
  };

  const updateKanbanStage = (stage: Contact['kanbanStage']) => {
    const updatedContact = { ...editedContact, kanbanStage: stage };
    setEditedContact(updatedContact);
    onUpdate(updatedContact);
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return Image;
    if (type.startsWith('audio/')) return Volume2;
    return FileText;
  };

  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-yellow-100 text-yellow-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-red-100 text-red-800',
      'bg-teal-100 text-teal-800',
    ];
    const index = category.length % colors.length;
    return colors[index];
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Contact Details</span>
            <div className="flex gap-2">
              {contact.hasActiveReminder && (
                <Button onClick={stopAllReminders} size="sm" variant="outline">
                  <BellOff className="w-4 h-4 mr-2" />
                  Stop Reminders
                </Button>
              )}
              {!isEditing ? (
                <Button onClick={() => setIsEditing(true)} size="sm">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button onClick={handleSave} size="sm">
                    <Save className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                  <Button onClick={handleCancel} variant="outline" size="sm">
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  {isEditing ? (
                    <Input
                      value={editedContact.name}
                      onChange={(e) => setEditedContact(prev => ({ ...prev, name: e.target.value }))}
                    />
                  ) : (
                    <p className="text-gray-900 font-medium">{contact.name}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  {isEditing ? (
                    <Input
                      value={editedContact.phone}
                      onChange={(e) => setEditedContact(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  ) : (
                    <p className="text-gray-900">{contact.phone}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                  {isEditing ? (
                    <Select
                      value={editedContact.city}
                      onValueChange={(value) => setEditedContact(prev => ({ ...prev, city: value }))}
                      disabled={!editedContact.state}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={editedContact.state ? "Select city" : "Select state first"} />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] overflow-y-auto">
                        {availableCities.map((city) => (
                          <SelectItem key={city} value={city}>
                            {city}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-gray-900">{contact.city}</p>
                  )}
                  {isEditing && editedContact.state && availableCities.length === 0 && (
                    <p className="text-gray-500 text-xs mt-1">No cities available for selected state</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">State</label>
                  {isEditing ? (
                    <Select
                      value={editedContact.state}
                      onValueChange={(value) => setEditedContact(prev => ({ ...prev, state: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[200px] overflow-y-auto">
                        {allStates.map((state) => (
                          <SelectItem key={state} value={state}>
                            {state}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-gray-900">{contact.state}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Build Type</label>
                  {isEditing ? (
                    <Select
                      value={editedContact.buildType || 'Residential'}
                      onValueChange={(value) => setEditedContact(prev => ({ ...prev, buildType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Residential">Residential</SelectItem>
                        <SelectItem value="Commercial">Commercial</SelectItem>
                        <SelectItem value="Healthcare">Healthcare</SelectItem>
                        <SelectItem value="Institutional / Educational">Institutional / Educational</SelectItem>
                        <SelectItem value="Religious / Spiritual">Religious / Spiritual</SelectItem>
                        <SelectItem value="Government / Civic">Government / Civic</SelectItem>
                        <SelectItem value="Industrial">Industrial</SelectItem>
                        <SelectItem value="Recreational / Cultural / Sports">Recreational / Cultural / Sports</SelectItem>
                        <SelectItem value="Mixed-Use">Mixed-Use</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-gray-900">{contact.buildType || 'Not specified'}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                  {isEditing ? (
                    <Input
                      type="date"
                      value={editedContact.date}
                      onChange={(e) => setEditedContact(prev => ({ ...prev, date: e.target.value }))}
                    />
                  ) : (
                    <p className="text-gray-900">{new Date(contact.date).toLocaleDateString()}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Project Stage</label>
                  <Select
                    value={editedContact.kanbanStage || 'lead'}
                    onValueChange={updateKanbanStage}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="lead">Lead</SelectItem>
                      <SelectItem value="contacted">Contacted</SelectItem>
                      <SelectItem value="quoted">Quoted</SelectItem>
                      <SelectItem value="negotiating">Negotiating</SelectItem>
                      <SelectItem value="won">Won</SelectItem>
                      <SelectItem value="lost">Lost</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                  {isEditing ? (
                    <Textarea
                      value={editedContact.requirements}
                      onChange={(e) => setEditedContact(prev => ({ ...prev, requirements: e.target.value }))}
                      rows={3}
                      className="resize-none"
                    />
                  ) : (
                    <p className="text-gray-900 whitespace-pre-wrap break-words">{contact.requirements}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Reminders */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  Reminders
                </span>
                <Button onClick={() => setShowReminderForm(true)} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Reminder
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {showReminderForm && (
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <Input
                      placeholder="Reminder title"
                      value={newReminder.title}
                      onChange={(e) => setNewReminder(prev => ({ ...prev, title: e.target.value }))}
                    />
                    <div className="flex gap-2">
                      <Input
                        type="date"
                        value={newReminder.date}
                        onChange={(e) => setNewReminder(prev => ({ ...prev, date: e.target.value }))}
                      />
                      <Input
                        type="time"
                        value={newReminder.time}
                        onChange={(e) => setNewReminder(prev => ({ ...prev, time: e.target.value }))}
                      />
                    </div>
                  </div>
                  <Textarea
                    placeholder="Additional notes"
                    value={newReminder.notes}
                    onChange={(e) => setNewReminder(prev => ({ ...prev, notes: e.target.value }))}
                    rows={2}
                    className="mb-4"
                  />
                  <div className="flex gap-2">
                    <Button onClick={addReminder} size="sm">
                      Add Reminder
                    </Button>
                    <Button onClick={() => setShowReminderForm(false)} variant="outline" size="sm">
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {editedContact.reminders && editedContact.reminders.length > 0 ? (
                <div className="space-y-3">
                  {editedContact.reminders.map((reminder) => (
                    <div key={reminder.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <h4 className="font-medium">{reminder.title}</h4>
                        <p className="text-sm text-gray-600">
                          {new Date(reminder.date).toLocaleDateString()} at {reminder.time}
                        </p>
                        {reminder.notes && (
                          <p className="text-sm text-gray-500 mt-1">{reminder.notes}</p>
                        )}
                      </div>
                      <Button
                        onClick={() => removeReminder(reminder.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No reminders set</p>
              )}
            </CardContent>
          </Card>

          {/* Categories */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Categories</h3>
              <div className="flex flex-wrap gap-2">
                {editedContact.categories.map((category) => (
                  <Badge key={category} className={getCategoryColor(category)}>
                    {category}
                    {isEditing && (
                      <button
                        onClick={() => setEditedContact(prev => ({
                          ...prev,
                          categories: prev.categories.filter(c => c !== category)
                        }))}
                        className="ml-2 hover:text-red-600"
                      >
                        ×
                      </button>
                    )}
                  </Badge>
                ))}
                {isEditing && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newCategory = prompt('Enter new category:');
                      if (newCategory && !editedContact.categories.includes(newCategory)) {
                        setEditedContact(prev => ({
                          ...prev,
                          categories: [...prev.categories, newCategory]
                        }));
                      }
                    }}
                  >
                    + Add Category
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Attachments */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Attachments</h3>
                <div className="flex gap-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.mp3,.wav,.mp4"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  <Button 
                    onClick={() => fileInputRef.current?.click()}
                    size="sm"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Media
                  </Button>
                </div>
              </div>

              {editedContact.attachments.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {editedContact.attachments.map((attachment) => {
                    const FileIcon = getFileIcon(attachment.type);
                    return (
                      <div key={attachment.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-2">
                          <FileIcon className="w-8 h-8 text-blue-600" />
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeAttachment(attachment.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        <p className="font-medium text-sm mb-1 truncate">{attachment.name}</p>
                        <p className="text-xs text-gray-500 mb-2">
                          {new Date(attachment.uploadDate).toLocaleDateString()}
                        </p>
                        {attachment.type.startsWith('image/') && (
                          <img
                            src={attachment.url}
                            alt={attachment.name}
                            className="w-full h-24 object-cover rounded mt-2"
                          />
                        )}
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="w-full mt-2"
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = attachment.url;
                            link.download = attachment.name;
                            link.click();
                          }}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No attachments uploaded</p>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Notes</h3>
              
              {/* Add new note */}
              <div className="mb-4">
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Add a new note..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={2}
                    className="flex-1"
                  />
                  <Button onClick={updateNotes} disabled={!newNote.trim()}>
                    Add Note
                  </Button>
                </div>
              </div>

              {/* Existing notes */}
              {editedContact.notes ? (
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 break-words">
                    {editedContact.notes}
                  </pre>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No notes added yet</p>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            {onOpenWhatsAppSenderWithContact && (
              <Button
                onClick={() => onOpenWhatsAppSenderWithContact(contact)}
                className="bg-green-600 hover:bg-green-700"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Send WhatsApp Message
              </Button>
            )}
            {onAddToWhatsAppSender && !onOpenWhatsAppSenderWithContact && (
              <Button
                onClick={() => onAddToWhatsAppSender(contact)}
                className="bg-green-600 hover:bg-green-700"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Add to WhatsApp Sender
              </Button>
            )}
            <Button variant="outline" onClick={handleSaveAndClose}>
              Save and Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
