import React, { useState } from 'react';
import { Download, FileText, Star, Pin, Check } from 'lucide-react';
import { Contact } from '../types/Contact';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import * as XLSX from 'xlsx';

interface ExportModalProps {
  contacts: Contact[];
  allContacts: Contact[];
  onClose: () => void;
}

export const ExportModal: React.FC<ExportModalProps> = ({
  contacts,
  allContacts,
  onClose
}) => {
  const [exportType, setExportType] = useState<'filtered' | 'all' | 'favorites' | 'pinned' | 'shortlisted'>('filtered');
  const [format, setFormat] = useState<'csv' | 'excel'>('csv');

  const getContactsToExport = (): Contact[] => {
    switch (exportType) {
      case 'all':
        return allContacts;
      case 'favorites':
        return allContacts.filter(c => c.isFavorite);
      case 'pinned':
        return allContacts.filter(c => c.isPinned);
      case 'shortlisted':
        return allContacts.filter(c => c.isShortlisted);
      default:
        return contacts;
    }
  };

  const exportToCSV = (contactsToExport: Contact[]) => {
    const headers = ['Name', 'Phone', 'Requirements', 'Categories', 'State', 'City', 'Date', 'BuildType'];
    const csvContent = [
      headers.join(','),
      ...contactsToExport.map(contact => [
        `"${contact.name}"`,
        `"${contact.phone}"`,
        `"${contact.requirements}"`,
        `"${contact.categories.join(',')}"`,
        `"${contact.state}"`,
        `"${contact.city}"`,
        `"${contact.date}"`,
        `"${contact.buildType}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `contacts_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportToExcel = (contactsToExport: Contact[]) => {
    const workbook = XLSX.utils.book_new();
    
    // Prepare data for export
    const headers = ['Name', 'Phone', 'Requirements', 'Categories', 'State', 'City', 'Date', 'BuildType'];
    const data = [
      headers,
      ...contactsToExport.map(contact => [
        contact.name,
        contact.phone,
        contact.requirements,
        contact.categories.join(', '),
        contact.state,
        contact.city,
        contact.date,
        contact.buildType
      ])
    ];
    
    const worksheet = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts');
    
    // Save the file
    XLSX.writeFile(workbook, `contacts_export_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  const handleExport = () => {
    const contactsToExport = getContactsToExport();
    
    if (contactsToExport.length === 0) {
      alert('No contacts to export');
      return;
    }

    if (format === 'csv') {
      exportToCSV(contactsToExport);
    } else {
      exportToExcel(contactsToExport);
    }

    onClose();
  };

  const contactsToExport = getContactsToExport();

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Export Contacts</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Type Selection */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">What to export</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="radio"
                  value="filtered"
                  checked={exportType === 'filtered'}
                  onChange={(e) => setExportType(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Current filtered list ({contacts.length} contacts)
                </span>
              </label>
              
              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="radio"
                  value="all"
                  checked={exportType === 'all'}
                  onChange={(e) => setExportType(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  All contacts ({allContacts.length} contacts)
                </span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="radio"
                  value="favorites"
                  checked={exportType === 'favorites'}
                  onChange={(e) => setExportType(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  Favorites only ({allContacts.filter(c => c.isFavorite).length} contacts)
                </span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50">
                <input
                  type="radio"
                  value="pinned"
                  checked={exportType === 'pinned'}
                  onChange={(e) => setExportType(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="flex items-center gap-2">
                  <Pin className="w-4 h-4 text-blue-500" />
                  Pinned only ({allContacts.filter(c => c.isPinned).length} contacts)
                </span>
              </label>

              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50 md:col-span-2">
                <input
                  type="radio"
                  value="shortlisted"
                  checked={exportType === 'shortlisted'}
                  onChange={(e) => setExportType(e.target.value as any)}
                  className="text-blue-600"
                />
                <span className="flex items-center gap-2">
                  <Check className="w-4 h-4 text-green-500" />
                  Shortlisted only ({allContacts.filter(c => c.isShortlisted).length} contacts)
                </span>
              </label>
            </div>
          </div>

          {/* Format Selection */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Export format</h3>
            <div className="flex gap-4">
              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50 flex-1">
                <input
                  type="radio"
                  value="csv"
                  checked={format === 'csv'}
                  onChange={(e) => setFormat(e.target.value as any)}
                  className="text-blue-600"
                />
                <span>CSV (.csv)</span>
              </label>
              
              <label className="flex items-center space-x-3 cursor-pointer p-3 border rounded-lg hover:bg-gray-50 flex-1">
                <input
                  type="radio"
                  value="excel"
                  checked={format === 'excel'}
                  onChange={(e) => setFormat(e.target.value as any)}
                  className="text-blue-600"
                />
                <span>Excel (.xlsx)</span>
              </label>
            </div>
          </div>

          {/* Export Preview */}
          <Card>
            <CardContent className="p-6">
              <h4 className="font-medium text-gray-900 mb-3">Export Preview</h4>
              <p className="text-sm text-gray-600 mb-3">
                {contactsToExport.length} contacts will be exported with the following fields:
              </p>
              <div className="text-sm text-gray-700">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  <span className="flex items-center gap-1">• Name</span>
                  <span className="flex items-center gap-1">• Phone</span>
                  <span className="flex items-center gap-1">• Requirements</span>
                  <span className="flex items-center gap-1">• Categories</span>
                  <span className="flex items-center gap-1">• State</span>
                  <span className="flex items-center gap-1">• City</span>
                  <span className="flex items-center gap-1">• Date</span>
                  <span className="flex items-center gap-1">• Build Type</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4 border-t">
            <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={contactsToExport.length === 0}
              className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
            >
              <Download className="w-4 h-4 mr-2" />
              Export {contactsToExport.length} Contacts
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
