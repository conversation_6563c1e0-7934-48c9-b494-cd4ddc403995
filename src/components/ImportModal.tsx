import React, { useState, useRef } from 'react';
import { Upload, Download, FileText, X, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { Contact } from '../types/Contact';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import * as XLSX from 'xlsx';
import { parseDate } from '../utils/dateUtils';

interface ImportModalProps {
  onClose: () => void;
  onImport: (contacts: Contact[]) => void;
}

export const ImportModal: React.FC<ImportModalProps> = ({ onClose, onImport }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [importedContacts, setImportedContacts] = useState<Contact[]>([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [showInstructions, setShowInstructions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const downloadTemplate = (format: 'csv' | 'excel') => {
    const headers = ['Name', 'Phone', 'Requirements', 'Categories', 'State', 'City', 'Date', 'BuildType'];
    const sampleData = [
      ['John Doe', '+**********', 'Web development services', 'Web Development,E-commerce', 'California', 'San Francisco', '2024-01-15', 'Residential'],
      ['Jane Smith', '+**********', 'Mobile app development', 'Mobile Development,iOS', 'New York', 'New York City', '15/01/2024', 'Commercial'],
      ['Mike Johnson', '+**********', 'Digital marketing consultation', 'Marketing,SEO', 'Texas', 'Austin', '01-15-2024', 'Healthcare']
    ];

    if (format === 'csv') {
      const csvContent = [
        headers.join(','),
        ...sampleData.map(row => row.map(cell => `"${cell}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'contact_import_template.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else {
      // Create Excel file
      const workbook = XLSX.utils.book_new();
      
      // Add notes sheet
      const notesData = [
        ['IMPORTANT NOTES:'],
        [''],
        ['1. Name and Phone Number are MANDATORY fields'],
        ['2. Phone number must include country code (e.g., +1 for US, +91 for India)'],
        ['3. Country code is required for WhatsApp integration to work properly'],
        ['4. Categories should be comma-separated (e.g., "Web Development,E-commerce")'],
        ['5. Build Type options:'],
        ['   • Residential'],
        ['   • Commercial'],
        ['   • Healthcare'],
        ['   • Institutional / Educational'],
        ['   • Religious / Spiritual'],
        ['   • Government / Civic'],
        ['   • Industrial'],
        ['   • Recreational / Cultural / Sports'],
        ['   • Mixed-Use'],
        ['6. Date format: Supports multiple formats:'],
        ['   • YYYY-MM-DD (e.g., 2024-01-15)'],
        ['   • DD/MM/YYYY (e.g., 15/01/2024)'],
        ['   • MM/DD/YYYY (e.g., 01/15/2024)'],
        ['   • DD-MM-YYYY (e.g., 15-01-2024)'],
        ['   • MM-DD-YYYY (e.g., 01-15-2024)'],
        ['   • YYYY/MM/DD (e.g., 2024/01/15)'],
        ['   • DD.MM.YYYY (e.g., 15.01.2024)'],
        ['   • MM.DD.YYYY (e.g., 01.15.2024)'],
        ['   • Excel date numbers are also supported'],
        [''],
        ['SAMPLE DATA:']
      ];
      
      const notesSheet = XLSX.utils.aoa_to_sheet(notesData);
      XLSX.utils.book_append_sheet(workbook, notesSheet, 'Notes');
      
      // Add data sheet
      const dataSheet = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
      XLSX.utils.book_append_sheet(workbook, dataSheet, 'Contacts');
      
      // Save the file
      XLSX.writeFile(workbook, 'contact_import_template.xlsx');
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    const validTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    if (!validTypes.includes(file.type) && !file.name.endsWith('.csv') && !file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setErrorMessage('Please select a valid CSV or Excel file');
      setImportStatus('error');
      return;
    }

    setSelectedFile(file);
    setImportStatus('idle');
    setErrorMessage('');
  };

  const parseCSV = (text: string): Contact[] => {
    const lines = text.split('\n').filter(line => line.trim());
    if (lines.length < 2) {
      throw new Error('File must contain at least a header row and one data row');
    }

    // Improved CSV parsing that handles quoted fields
    const parseCSVLine = (line: string): string[] => {
      const result = [];
      let current = '';
      let inQuotes = false;
      
      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
          if (inQuotes && line[i + 1] === '"') {
            // Escaped quote
            current += '"';
            i++;
          } else {
            // Toggle quote state
            inQuotes = !inQuotes;
          }
        } else if (char === ',' && !inQuotes) {
          // End of field
          result.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      // Add the last field
      result.push(current.trim());
      return result;
    };

    const headers = parseCSVLine(lines[0]).map(h => h.replace(/"/g, '').trim());
    const expectedHeaders = ['Name', 'Phone', 'Requirements', 'Categories', 'State', 'City', 'Date', 'BuildType'];
    
    const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
    if (missingHeaders.length > 0) {
      throw new Error(`Missing required columns: ${missingHeaders.join(', ')}`);
    }

    const contacts: Contact[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]);
      if (values.length < headers.length) continue;

      const categoriesValue = values[headers.indexOf('Categories')] || '';
      const buildTypeValue = values[headers.indexOf('BuildType')] || '';
      const stateValue = values[headers.indexOf('State')] || '';
      const cityValue = values[headers.indexOf('City')] || '';

      const contact: Contact = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: (values[headers.indexOf('Name')] || '').trim(),
        phone: (values[headers.indexOf('Phone')] || '').trim(),
        requirements: (values[headers.indexOf('Requirements')] || '').trim(),
        categories: categoriesValue ? categoriesValue.split(',').map(c => c.trim()).filter(c => c) : ['General'],
        state: stateValue.trim() || 'Unknown',
        city: cityValue.trim() || 'Unknown',
        date: parseDate(values[headers.indexOf('Date')] || ''),
        isFavorite: false,
        isPinned: false,
        isShortlisted: false,
        notes: '',
        attachments: [],
        buildType: buildTypeValue.trim() || 'Residential',
        kanbanStage: 'lead',
        reminders: [],
        hasActiveReminder: false
      };

      if (contact.name && contact.phone) {
        contacts.push(contact);
      }
    }

    return contacts;
  };

  const parseExcel = async (file: File): Promise<Contact[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Try to find the Contacts sheet, fallback to first sheet
          let sheetName = 'Contacts';
          if (!workbook.SheetNames.includes('Contacts')) {
            sheetName = workbook.SheetNames[0];
          }
          
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          
          if (jsonData.length < 2) {
            throw new Error('File must contain at least a header row and one data row');
          }

          const headers = (jsonData[0] as string[]).map(h => h.trim());
          const expectedHeaders = ['Name', 'Phone', 'Requirements', 'Categories', 'State', 'City', 'Date', 'BuildType'];
          
          const missingHeaders = expectedHeaders.filter(h => !headers.includes(h));
          if (missingHeaders.length > 0) {
            throw new Error(`Missing required columns: ${missingHeaders.join(', ')}`);
          }

          const contacts: Contact[] = [];
          
          for (let i = 1; i < jsonData.length; i++) {
            const row = jsonData[i] as any[];
            if (!row || row.length < headers.length) continue;

            const categoriesValue = (row[headers.indexOf('Categories')] || '').toString().trim();
            const buildTypeValue = (row[headers.indexOf('BuildType')] || '').toString().trim();
            const stateValue = (row[headers.indexOf('State')] || '').toString().trim();
            const cityValue = (row[headers.indexOf('City')] || '').toString().trim();

            const contact: Contact = {
              id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
              name: (row[headers.indexOf('Name')] || '').toString().trim(),
              phone: (row[headers.indexOf('Phone')] || '').toString().trim(),
              requirements: (row[headers.indexOf('Requirements')] || '').toString().trim(),
              categories: categoriesValue ? categoriesValue.split(',').map(c => c.trim()).filter(c => c) : ['General'],
              state: stateValue || 'Unknown',
              city: cityValue || 'Unknown',
              date: parseDate((row[headers.indexOf('Date')] || '').toString()),
              isFavorite: false,
              isPinned: false,
              isShortlisted: false,
              notes: '',
              attachments: [],
              buildType: buildTypeValue || 'Residential',
              kanbanStage: 'lead',
              reminders: [],
              hasActiveReminder: false
            };

            if (contact.name && contact.phone) {
              contacts.push(contact);
            }
          }

          resolve(contacts);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsArrayBuffer(file);
    });
  };

  const processFile = async () => {
    if (!selectedFile) return;

    setImportStatus('processing');
    console.log('🔄 Processing file:', selectedFile.name, selectedFile.type);
    
    try {
      let contacts: Contact[];
      
      if (selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
        console.log('📄 Processing CSV file');
        const text = await selectedFile.text();
        console.log('📄 CSV content preview:', text.substring(0, 200) + '...');
        contacts = parseCSV(text);
      } else {
        console.log('📊 Processing Excel file');
        contacts = await parseExcel(selectedFile);
      }
      
      console.log('✅ Parsed contacts:', contacts.length, contacts);
      
      if (contacts.length === 0) {
        throw new Error('No valid contacts found in the file');
      }

      setImportedContacts(contacts);
      setImportStatus('success');
      console.log('✅ Import successful, ready to add contacts');
    } catch (error) {
      console.error('❌ Import error:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to process file');
      setImportStatus('error');
    }
  };

  const handleImport = () => {
    onImport(importedContacts);
    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Import Contacts</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Download Template</h3>
                  <p className="text-sm text-gray-600">
                    Get the template with the correct format and important notes
                  </p>
                </div>
                <div className="flex flex-wrap gap-3">
                  <Button onClick={() => downloadTemplate('csv')} variant="outline" className="flex-1 min-w-[140px]">
                    <Download className="w-4 h-4 mr-2" />
                    CSV Template
                  </Button>
                  <Button onClick={() => downloadTemplate('excel')} variant="outline" className="flex-1 min-w-[140px]">
                    <Download className="w-4 h-4 mr-2" />
                    Excel Template
                  </Button>
                </div>
                
                {/* Instructions Toggle Button */}
                <div className="border-t pt-4">
                  <Button
                    onClick={() => setShowInstructions(!showInstructions)}
                    variant="ghost"
                    className="w-full justify-between text-blue-700 hover:text-blue-800 hover:bg-blue-50"
                  >
                    <span className="font-medium">View Import Instructions</span>
                    {showInstructions ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </Button>
                </div>

                {/* Collapsible Instructions */}
                {showInstructions && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 animate-in slide-in-from-top-2 duration-200">
                    <h4 className="font-medium text-blue-900 mb-3">Important Notes:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                      <div>
                        <ul className="space-y-1">
                          <li>• <strong>Name and Phone Number are MANDATORY</strong></li>
                          <li>• Phone number must include <strong>country code</strong></li>
                          <li>• Country code required for WhatsApp integration</li>
                          <li>• Categories should be comma-separated</li>
                        </ul>
                      </div>
                      <div>
                        <div className="mb-2"><strong>Build Type options:</strong></div>
                        <div className="grid grid-cols-2 gap-1 text-xs">
                          <div>• Residential</div>
                          <div>• Commercial</div>
                          <div>• Healthcare</div>
                          <div>• Institutional</div>
                          <div>• Religious</div>
                          <div>• Government</div>
                          <div>• Industrial</div>
                          <div>• Recreational</div>
                          <div>• Mixed-Use</div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-3 pt-3 border-t border-blue-200">
                      <div className="font-medium mb-1">Supported Date formats:</div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-1 text-xs">
                        <div>• YYYY-MM-DD</div>
                        <div>• DD/MM/YYYY</div>
                        <div>• MM/DD/YYYY</div>
                        <div>• DD-MM-YYYY</div>
                        <div>• MM-DD-YYYY</div>
                        <div>• YYYY/MM/DD</div>
                        <div>• DD.MM.YYYY</div>
                        <div>• Excel dates</div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardContent className="p-6">
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  {selectedFile ? selectedFile.name : 'Drop your Excel/CSV file here'}
                </p>
                <p className="text-gray-600 mb-4">
                  Supports .csv, .xls, and .xlsx files
                </p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  variant="outline"
                  className="min-w-[140px]"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Choose File
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv,.xls,.xlsx"
                  onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                  className="hidden"
                />
              </div>
            </CardContent>
          </Card>

          {/* Status Messages */}
          {importStatus === 'error' && (
            <Alert className="border-red-200 bg-red-50">
              <X className="w-4 h-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}

          {importStatus === 'success' && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="w-4 h-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Successfully processed {importedContacts.length} contacts. Ready to import!
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row justify-between gap-4 pt-4 border-t">
            <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Cancel
            </Button>
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              {selectedFile && importStatus !== 'success' && (
                <Button
                  onClick={processFile}
                  disabled={importStatus === 'processing'}
                  className="w-full sm:w-auto"
                >
                  {importStatus === 'processing' ? 'Processing...' : 'Process File'}
                </Button>
              )}
              {importStatus === 'success' && (
                <Button 
                  onClick={handleImport} 
                  className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
                >
                  Import {importedContacts.length} Contacts
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
