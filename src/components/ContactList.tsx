import React from 'react';
import { Star, Pin, CheckCircle, Phone, MapPin, Building, Tag, Calendar, Trash2, RotateCcw, MessageCircle } from 'lucide-react';
import { Contact } from '../types/Contact';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { RequirementsHoverPopup } from './RequirementsHoverPopup';

interface ContactListProps {
  contacts: Contact[];
  onContactClick: (contact: Contact) => void;
  onUpdateContact: (contact: Contact) => void;
  onDeleteContact: (id: string) => void;
  onRestoreContact?: (id: string) => void;
  isRecycleBin?: boolean;
  isListView?: boolean;
  isCompactView?: boolean;
  onAddToWhatsAppSender?: (contact: Contact) => void;
  onOpenWhatsAppSenderWithContact?: (contact: Contact) => void;
  contactMessageCounts?: Map<string, number>;
}

export const ContactList: React.FC<ContactListProps> = ({
  contacts,
  onContactClick,
  onUpdateContact,
  onDeleteContact,
  onRestoreContact,
  isRecycleBin = false,
  isListView = false,
  isCompactView = false,
  onAddToWhatsAppSender,
  onOpenWhatsAppSenderWithContact,
  contactMessageCounts
}) => {
  const handleContactNameClick = (e: React.MouseEvent, contact: Contact) => {
    // Check if the click target is a button or inside a button
    const target = e.target as HTMLElement;
    const isButton = target.closest('button') || target.tagName === 'BUTTON';
    
    console.log('Contact name clicked:', {
      target: target,
      targetTagName: target.tagName,
      targetClassName: target.className,
      isButton: isButton,
      closestButton: target.closest('button')
    });
    
    if (!isButton) {
      console.log('Opening contact details for:', contact.name);
      onContactClick(contact);
    } else {
      console.log('Button clicked, not opening contact details');
    }
  };

  const toggleFavorite = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 toggleFavorite START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    console.log('🔴 Event type:', e.type);
    console.log('🔴 Event bubbles:', e.bubbles);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 toggleFavorite - About to update contact');
    onUpdateContact({ ...contact, isFavorite: !contact.isFavorite });
    console.log('🔴 toggleFavorite END');
  };

  const togglePin = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 togglePin START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 togglePin - About to update contact');
    onUpdateContact({ ...contact, isPinned: !contact.isPinned });
    console.log('🔴 togglePin END');
  };

  const toggleShortlist = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 toggleShortlist START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 toggleShortlist - About to update contact');
    onUpdateContact({ ...contact, isShortlisted: !contact.isShortlisted });
    console.log('🔴 toggleShortlist END');
  };

  const handleWhatsApp = (e: React.MouseEvent, contact: Contact) => {
    console.log('🔴 handleWhatsApp START - Contact:', contact.name);
    console.log('🔴 Event target:', e.target);
    console.log('🔴 Event currentTarget:', e.currentTarget);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔴 handleWhatsApp - About to open WhatsApp');
    if (onOpenWhatsAppSenderWithContact) {
      onOpenWhatsAppSenderWithContact(contact);
    } else if (onAddToWhatsAppSender) {
      onAddToWhatsAppSender(contact);
    } else {
      // Fallback to opening WhatsApp directly
      const cleanPhone = contact.phone.replace(/[^\d]/g, '');
      window.open(`https://wa.me/${cleanPhone}`, '_blank');
    }
    console.log('🔴 handleWhatsApp END');
  };

  const handleDelete = (e: React.MouseEvent, id: string) => {
    e.preventDefault();
    e.stopPropagation();
    onDeleteContact(id);
  };

  const handleRestore = (e: React.MouseEvent, id: string) => {
    e.preventDefault();
    e.stopPropagation();
    onRestoreContact?.(id);
  };

  if (contacts.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 dark:text-gray-400">No contacts found</p>
      </div>
    );
  }

  if (isCompactView) {
    return (
      <div className="space-y-2">
        {contacts.map((contact) => (
          <TooltipProvider key={contact.id}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className={`flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow ${
                    contact.hasActiveReminder ? 'animate-pulse border-orange-400 bg-orange-50' : ''
                  }`}
                >
                  {/* Contact Info - Separate clickable area */}
                  <div 
                    className="flex items-center gap-3 min-w-0 flex-1 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded p-2 transition-colors"
                    onClick={(e) => {
                      console.log('🟢 Contact info area clicked - Contact:', contact.name);
                      console.log('🟢 Event target:', e.target);
                      console.log('🟢 Event currentTarget:', e.currentTarget);
                      console.log('🟢 Time:', new Date().toISOString());
                      onContactClick(contact);
                    }}
                    onMouseDown={(e) => {
                      console.log('🟢 Contact info area mouseDown - Contact:', contact.name);
                      console.log('🟢 Event target:', e.target);
                    }}
                  >
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                        {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </div>
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <RequirementsHoverPopup contact={contact}>
                          <h3 className="font-semibold text-gray-900 dark:text-white truncate cursor-help">
                            {contact.name}
                          </h3>
                        </RequirementsHoverPopup>
                        {contactMessageCounts && contactMessageCounts.get(contact.phone) && (
                          <Badge variant="secondary" className="text-xs px-1.5 py-0.5 bg-green-100 text-green-700">
                            {contactMessageCounts.get(contact.phone)} msg
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{contact.phone}</p>
                    </div>
                  </div>
                  
                  {/* Action Buttons - Completely separate area */}
                  <div className="flex items-center gap-1 ml-2">
                    <TooltipProvider>
                      {!isRecycleBin && (
                        <>
                          <button
                            type="button"
                            onClick={(e) => {
                              console.log('🔴 Regular button onClick START - Contact:', contact.name);
                              console.log('🔴 Event target:', e.target);
                              console.log('🔴 Event currentTarget:', e.currentTarget);
                              e.preventDefault();
                              e.stopPropagation();
                              onUpdateContact({ ...contact, isFavorite: !contact.isFavorite });
                            }}
                            className={`h-8 w-8 p-0 transition-all duration-200 rounded-md flex items-center justify-center ${
                              contact.isFavorite 
                                ? 'text-yellow-500 hover:text-yellow-600 hover:bg-yellow-50' 
                                : 'text-gray-400 hover:text-yellow-500 hover:bg-yellow-50'
                            }`}
                          >
                            <Star className={`w-4 h-4 ${contact.isFavorite ? 'fill-current' : ''}`} />
                          </button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => togglePin(e, contact)}
                            className={`h-8 w-8 p-0 transition-all duration-200 ${
                              contact.isPinned 
                                ? 'text-blue-500 hover:text-blue-600 hover:bg-blue-50' 
                                : 'text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                            }`}
                          >
                            <Pin className={`w-4 h-4 ${contact.isPinned ? 'fill-current' : ''}`} />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => toggleShortlist(e, contact)}
                            className={`h-8 w-8 p-0 transition-all duration-200 ${
                              contact.isShortlisted 
                                ? 'text-green-500 hover:text-green-600 hover:bg-green-50' 
                                : 'text-gray-400 hover:text-green-500 hover:bg-green-50'
                            }`}
                          >
                            <CheckCircle className={`w-4 h-4 ${contact.isShortlisted ? 'fill-current' : ''}`} />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleWhatsApp(e, contact)}
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                          >
                            <MessageCircle className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                      
                      {isRecycleBin ? (
                        <>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleRestore(e, contact.id)}
                                className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
                              >
                                <RotateCcw className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Restore contact</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleDelete(e, contact.id)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete permanently</p>
                            </TooltipContent>
                          </Tooltip>
                        </>
                      ) : (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleDelete(e, contact.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete contact</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </TooltipProvider>
                  </div>
                </div>
              </TooltipTrigger>
              {contact.hasActiveReminder && contact.reminders && contact.reminders.length > 0 && (
                <TooltipContent>
                  <div className="max-w-xs">
                    <p className="font-semibold text-orange-600 mb-1">Active Reminders:</p>
                    {contact.reminders.map((reminder, index) => (
                      <div key={reminder.id} className="mb-1 last:mb-0">
                        <p className="font-medium">{reminder.title}</p>
                        <p className="text-xs text-gray-600">{reminder.date} at {reminder.time}</p>
                        {reminder.notes && <p className="text-xs italic">{reminder.notes}</p>}
                      </div>
                    ))}
                  </div>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {contacts.map((contact) => (
        <TooltipProvider key={contact.id}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Card
                className={`hover:shadow-lg transition-shadow ${
                  contact.hasActiveReminder ? 'animate-pulse border-orange-400 bg-orange-50' : ''
                }`}
              >
                <CardContent className="p-4">
                  {/* Contact Info - Separate clickable area */}
                  <div 
                    className="flex items-center justify-between mb-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 rounded p-2 transition-colors"
                    onClick={(e) => {
                      console.log('🟢 Contact info area clicked (card view) - Contact:', contact.name);
                      console.log('🟢 Event target:', e.target);
                      console.log('🟢 Event currentTarget:', e.currentTarget);
                      onContactClick(contact);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                        {contact.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <RequirementsHoverPopup contact={contact}>
                            <h3 className="font-semibold text-gray-900 dark:text-white cursor-help">
                              {contact.name}
                            </h3>
                          </RequirementsHoverPopup>
                          {contactMessageCounts && contactMessageCounts.get(contact.phone) && (
                            <Badge variant="secondary" className="text-xs px-1.5 py-0.5 bg-green-100 text-green-700">
                              {contactMessageCounts.get(contact.phone)} msg
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{contact.phone}</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Buttons - Completely separate area */}
                  <div className="flex items-center justify-center gap-1 mb-3">
                    <TooltipProvider>
                      {!isRecycleBin && (
                        <>
                          <button
                            type="button"
                            onClick={(e) => {
                              console.log('🔴 Regular button onClick START - Contact:', contact.name);
                              console.log('🔴 Event target:', e.target);
                              console.log('🔴 Event currentTarget:', e.currentTarget);
                              e.preventDefault();
                              e.stopPropagation();
                              onUpdateContact({ ...contact, isFavorite: !contact.isFavorite });
                            }}
                            className={`h-8 w-8 p-0 transition-all duration-200 rounded-md flex items-center justify-center ${
                              contact.isFavorite 
                                ? 'text-yellow-500 hover:text-yellow-600 hover:bg-yellow-50' 
                                : 'text-gray-400 hover:text-yellow-500 hover:bg-yellow-50'
                            }`}
                          >
                            <Star className={`w-4 h-4 ${contact.isFavorite ? 'fill-current' : ''}`} />
                          </button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => togglePin(e, contact)}
                            className={`h-8 w-8 p-0 transition-all duration-200 ${
                              contact.isPinned 
                                ? 'text-blue-500 hover:text-blue-600 hover:bg-blue-50' 
                                : 'text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                            }`}
                          >
                            <Pin className={`w-4 h-4 ${contact.isPinned ? 'fill-current' : ''}`} />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => toggleShortlist(e, contact)}
                            className={`h-8 w-8 p-0 transition-all duration-200 ${
                              contact.isShortlisted 
                                ? 'text-green-500 hover:text-green-600 hover:bg-green-50' 
                                : 'text-gray-400 hover:text-green-500 hover:bg-green-50'
                            }`}
                          >
                            <CheckCircle className={`w-4 h-4 ${contact.isShortlisted ? 'fill-current' : ''}`} />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => handleWhatsApp(e, contact)}
                            className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                          >
                            <MessageCircle className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                      
                      {isRecycleBin ? (
                        <>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleRestore(e, contact.id)}
                                className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
                              >
                                <RotateCcw className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Restore contact</p>
                            </TooltipContent>
                          </Tooltip>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => handleDelete(e, contact.id)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Delete permanently</p>
                            </TooltipContent>
                          </Tooltip>
                        </>
                      ) : (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleDelete(e, contact.id)}
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Delete contact</p>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </TooltipProvider>
                  </div>

                  <div className="space-y-2 mb-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <MapPin className="w-4 h-4" />
                      <span className="truncate">{contact.city}, {contact.state}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Building className="w-4 h-4" />
                      <span className="truncate">{contact.buildType}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(contact.date).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="mb-3">
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                      {contact.requirements}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {contact.categories.slice(0, 3).map((category, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                    {contact.categories.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{contact.categories.length - 3}
                      </Badge>
                    )}
                  </div>

                  {!isRecycleBin && (
                    <div className="flex justify-end">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant={onOpenWhatsAppSenderWithContact ? "default" : "outline"}
                              size="sm"
                              onClick={(e) => handleWhatsApp(e, contact)}
                              className={onOpenWhatsAppSenderWithContact 
                                ? "bg-green-600 hover:bg-green-700 text-white" 
                                : "text-green-600 hover:text-green-700 hover:bg-green-50"
                              }
                            >
                              <MessageCircle className="w-4 h-4 mr-1" />
                              {onOpenWhatsAppSenderWithContact ? 'Send Message' : onAddToWhatsAppSender ? 'Add to Sender' : 'WhatsApp'}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{onOpenWhatsAppSenderWithContact ? 'Send WhatsApp message to this contact' : onAddToWhatsAppSender ? 'Add to WhatsApp sender' : 'Open WhatsApp chat'}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TooltipTrigger>
            {contact.hasActiveReminder && contact.reminders && contact.reminders.length > 0 && (
              <TooltipContent>
                <div className="max-w-xs">
                  <p className="font-semibold text-orange-600 mb-1">Active Reminders:</p>
                  {contact.reminders.map((reminder, index) => (
                    <div key={reminder.id} className="mb-1 last:mb-0">
                      <p className="font-medium">{reminder.title}</p>
                      <p className="text-xs text-gray-600">{reminder.date} at {reminder.time}</p>
                      {reminder.notes && <p className="text-xs italic">{reminder.notes}</p>}
                    </div>
                  ))}
                </div>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  );
};
