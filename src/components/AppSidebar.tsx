
import React, { useState } from 'react';
import { Search, Tag, MapPin, Building, Calendar, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
} from '@/components/ui/sidebar';

interface AppSidebarProps {
  selectedCategories: string[];
  handleCategoryClick: (category: string) => void;
  setSelectedCategories: (categories: string[]) => void;
  selectedStates: string[];
  handleStateClick: (state: string) => void;
  setSelectedStates: (states: string[]) => void;
  selectedCities: string[];
  handleCityClick: (city: string) => void;
  setSelectedCities: (cities: string[]) => void;
  selectedBuildTypes: string[];
  handleBuildTypeClick: (buildType: string) => void;
  setSelectedBuildTypes: (buildTypes: string[]) => void;
  allCategories: string[];
  allStates: string[];
  allCities: string[];
  allBuildTypes: string[];
  getCategoryColor: (category: string) => string;
  showRecycleBin: boolean;
  setShowRecycleBin: (show: boolean) => void;
  deletedContactsCount: number;
  totalContacts: number;
  favoriteContacts: number;
  shortlistedContacts: number;
  onQuickFilter: (filter: 'all' | 'favorites' | 'shortlisted') => void;
  filteredContacts: any[];
  dateRange: { start: string; end: string } | null;
  setDateRange: (range: { start: string; end: string } | null) => void;
}

export function AppSidebar({
  selectedCategories,
  handleCategoryClick,
  setSelectedCategories,
  selectedStates,
  handleStateClick,
  setSelectedStates,
  selectedCities,
  handleCityClick,
  setSelectedCities,
  selectedBuildTypes,
  handleBuildTypeClick,
  setSelectedBuildTypes,
  allCategories,
  allStates,
  allCities,
  allBuildTypes,
  getCategoryColor,
  showRecycleBin,
  dateRange,
  setDateRange
}: AppSidebarProps) {
  const [categorySearch, setCategorySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [citySearch, setCitySearch] = useState('');
  const [buildTypeSearch, setBuildTypeSearch] = useState('');

  const filteredCategories = allCategories.filter(category =>
    category.toLowerCase().includes(categorySearch.toLowerCase())
  );

  const filteredStates = allStates.filter(state =>
    state.toLowerCase().includes(stateSearch.toLowerCase())
  );

  const filteredCities = allCities.filter(city =>
    city.toLowerCase().includes(citySearch.toLowerCase())
  );

  const filteredBuildTypes = allBuildTypes.filter(buildType =>
    buildType.toLowerCase().includes(buildTypeSearch.toLowerCase())
  );

  return (
    <Sidebar className="border-r border-gray-200 dark:border-gray-700 w-64">
      <SidebarHeader className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Filters
        </h2>
      </SidebarHeader>
      
      <SidebarContent className="px-4">
        {/* Date Filter */}
        {!showRecycleBin && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Date Range
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="space-y-2">
                <Input
                  type="date"
                  placeholder="Start date"
                  value={dateRange?.start || ''}
                  onChange={(e) => setDateRange(
                    dateRange 
                      ? { ...dateRange, start: e.target.value }
                      : { start: e.target.value, end: '' }
                  )}
                  className="h-7 text-xs"
                />
                <Input
                  type="date"
                  placeholder="End date"
                  value={dateRange?.end || ''}
                  onChange={(e) => setDateRange(
                    dateRange 
                      ? { ...dateRange, end: e.target.value }
                      : { start: '', end: e.target.value }
                  )}
                  className="h-7 text-xs"
                />
                {dateRange && (dateRange.start || dateRange.end) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setDateRange(null)}
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs h-6"
                  >
                    Clear dates
                  </Button>
                )}
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* States Filter */}
        {!showRecycleBin && allStates.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <Building className="w-4 h-4" />
              States
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
                  <Input
                    placeholder="Search states..."
                    value={stateSearch}
                    onChange={(e) => setStateSearch(e.target.value)}
                    className="pl-7 h-7 text-xs"
                  />
                </div>
                <div className="max-h-32 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {filteredStates.map((state) => (
                      <Badge
                        key={state}
                        onClick={() => handleStateClick(state)}
                        className={`cursor-pointer transition-all duration-200 hover:scale-105 text-xs ${
                          selectedStates.includes(state)
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {state}
                        {selectedStates.includes(state) && (
                          <span className="ml-1">×</span>
                        )}
                      </Badge>
                    ))}
                  </div>
                  {selectedStates.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedStates([])}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs h-6 mt-1"
                    >
                      Clear states
                    </Button>
                  )}
                </div>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Cities Filter */}
        {!showRecycleBin && allCities.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Cities
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
                  <Input
                    placeholder="Search cities..."
                    value={citySearch}
                    onChange={(e) => setCitySearch(e.target.value)}
                    className="pl-7 h-7 text-xs"
                  />
                </div>
                <div className="max-h-32 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {filteredCities.map((city) => (
                      <Badge
                        key={city}
                        onClick={() => handleCityClick(city)}
                        className={`cursor-pointer transition-all duration-200 hover:scale-105 text-xs ${
                          selectedCities.includes(city)
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {city}
                        {selectedCities.includes(city) && (
                          <span className="ml-1">×</span>
                        )}
                      </Badge>
                    ))}
                  </div>
                  {selectedCities.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedCities([])}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs h-6 mt-1"
                    >
                      Clear cities
                    </Button>
                  )}
                </div>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Build Types Filter */}
        {!showRecycleBin && allBuildTypes.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              Build Types
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
                  <Input
                    placeholder="Search build types..."
                    value={buildTypeSearch}
                    onChange={(e) => setBuildTypeSearch(e.target.value)}
                    className="pl-7 h-7 text-xs"
                  />
                </div>
                <div className="max-h-32 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {filteredBuildTypes.map((buildType) => (
                      <Badge
                        key={buildType}
                        onClick={() => handleBuildTypeClick(buildType)}
                        className={`cursor-pointer transition-all duration-200 hover:scale-105 text-xs ${
                          selectedBuildTypes.includes(buildType)
                            ? 'bg-purple-600 text-white hover:bg-purple-700'
                            : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                        }`}
                      >
                        {buildType}
                        {selectedBuildTypes.includes(buildType) && (
                          <span className="ml-1">×</span>
                        )}
                      </Badge>
                    ))}
                  </div>
                  {selectedBuildTypes.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedBuildTypes([])}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs h-6 mt-1"
                    >
                      Clear build types
                    </Button>
                  )}
                </div>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Categories Filter */}
        {!showRecycleBin && allCategories.length > 0 && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <Tag className="w-4 h-4" />
              Categories
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3" />
                  <Input
                    placeholder="Search categories..."
                    value={categorySearch}
                    onChange={(e) => setCategorySearch(e.target.value)}
                    className="pl-7 h-7 text-xs"
                  />
                </div>
                <div className="max-h-32 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {filteredCategories.map((category) => (
                      <Badge
                        key={category}
                        onClick={() => handleCategoryClick(category)}
                        className={`cursor-pointer transition-all duration-200 hover:scale-105 text-xs ${
                          selectedCategories.includes(category)
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : getCategoryColor(category)
                        }`}
                      >
                        {category}
                        {selectedCategories.includes(category) && (
                          <span className="ml-1">×</span>
                        )}
                      </Badge>
                    ))}
                  </div>
                  {selectedCategories.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedCategories([])}
                      className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xs h-6 mt-1"
                    >
                      Clear categories
                    </Button>
                  )}
                </div>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}
      </SidebarContent>
    </Sidebar>
  );
}
