import React, { useState, useEffect } from 'react';
import { Calendar, Settings } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { formatDateForDisplay } from '../utils/dateUtils';

interface DateFormatSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

type DateFormat = 'local' | 'dd/mm/yyyy' | 'mm/dd/yyyy' | 'yyyy-mm-dd';

export const DateFormatSettings: React.FC<DateFormatSettingsProps> = ({ isOpen, onClose }) => {
  const [selectedFormat, setSelectedFormat] = useState<DateFormat>('local');

  // Load saved date format preference
  useEffect(() => {
    const savedFormat = localStorage.getItem('contactSphereDateFormat') as DateFormat;
    if (savedFormat) {
      setSelectedFormat(savedFormat);
    }
  }, []);

  // Save date format preference
  const saveDateFormat = (format: DateFormat) => {
    setSelectedFormat(format);
    localStorage.setItem('contactSphereDateFormat', format);
  };

  // Sample date for preview
  const sampleDate = '2024-01-15';

  const formatOptions = [
    { value: 'local', label: 'Local Format (Browser Default)', example: '1/15/2024' },
    { value: 'dd/mm/yyyy', label: 'DD/MM/YYYY (European)', example: '15/01/2024' },
    { value: 'mm/dd/yyyy', label: 'MM/DD/YYYY (US)', example: '01/15/2024' },
    { value: 'yyyy-mm-dd', label: 'YYYY-MM-DD (ISO)', example: '2024-01-15' },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Date Format Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <Label htmlFor="dateFormat" className="text-sm font-medium text-gray-700">
              Date Display Format
            </Label>
            <Select value={selectedFormat} onValueChange={(value) => saveDateFormat(value as DateFormat)}>
              <SelectTrigger className="mt-2">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {formatOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Card>
            <CardContent className="p-4">
              <h4 className="font-medium text-gray-900 mb-2">Preview</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Sample Date:</span>
                  <span className="font-mono">{sampleDate}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Displayed as:</span>
                  <span className="font-medium">
                    {formatDateForDisplay(sampleDate, selectedFormat)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="font-medium text-blue-900 mb-2">Note:</h4>
            <p className="text-sm text-blue-800">
              This setting affects how dates are displayed throughout the application. 
              Import functionality supports multiple date formats regardless of this setting.
            </p>
          </div>

          <div className="flex justify-end">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 