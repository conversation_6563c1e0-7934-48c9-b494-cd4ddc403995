import React, { useState, useMemo } from 'react';
import { 
  MessageCircle, 
  Calendar, 
  MapPin, 
  Filter, 
  SortAsc, 
  SortDesc, 
  Grid, 
  List, 
  FileText, 
  Image as ImageIcon,
  Video, 
  Link as LinkIcon,
  Download,
  Eye,
  EyeOff,
  Search,
  X
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  WhatsAppMessageHistory, 
  mockWhatsAppHistory, 
  filterWhatsAppHistory, 
  sortWhatsAppHistory 
} from '@/data/whatsappHistory';

interface WhatsAppSentHistoryProps {
  onClose: () => void;
}

export const WhatsAppSentHistory: React.FC<WhatsAppSentHistoryProps> = ({ onClose }) => {
  const [viewMode, setViewMode] = useState<'compact' | 'detailed'>('compact');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'city' | 'messages' | 'lastMessage' | 'firstMessage'>('lastMessage');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '' },
    city: '',
    state: '',
    minMessages: '',
    maxMessages: '',
    status: '' as 'active' | 'inactive' | '',
    hasAttachments: false,
    hasLinks: false
  });
  const [showFilters, setShowFilters] = useState(false);

  // Filter and sort the history
  const filteredHistory = useMemo(() => {
    let filtered = mockWhatsAppHistory;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.contactPhone.includes(searchTerm) ||
        item.contactCity.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply other filters
    const filterParams = {
      dateRange: filters.dateRange.start && filters.dateRange.end ? filters.dateRange : undefined,
      city: filters.city || undefined,
      state: filters.state || undefined,
      minMessages: filters.minMessages ? parseInt(filters.minMessages) : undefined,
      maxMessages: filters.maxMessages ? parseInt(filters.maxMessages) : undefined,
      status: filters.status || undefined,
      hasAttachments: filters.hasAttachments || undefined,
      hasLinks: filters.hasLinks || undefined
    };

    filtered = filterWhatsAppHistory(filtered, filterParams);

    // Apply sorting
    filtered = sortWhatsAppHistory(filtered, sortBy, sortOrder);

    return filtered;
  }, [searchTerm, filters, sortBy, sortOrder]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getAttachmentIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="w-4 h-4" />;
      case 'video': return <Video className="w-4 h-4" />;
      case 'document': return <FileText className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const clearFilters = () => {
    setFilters({
      dateRange: { start: '', end: '' },
      city: '',
      state: '',
      minMessages: '',
      maxMessages: '',
      status: '',
      hasAttachments: false,
      hasLinks: false
    });
    setSearchTerm('');
  };

  const hasActiveFilters = searchTerm || 
    filters.dateRange.start || 
    filters.city || 
    filters.state || 
    filters.minMessages || 
    filters.maxMessages || 
    filters.status || 
    filters.hasAttachments || 
    filters.hasLinks;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg w-full max-w-7xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <MessageCircle className="w-6 h-6 text-green-600" />
            <div>
              <h2 className="text-xl font-semibold">WhatsApp Sent History</h2>
              <p className="text-sm text-gray-600">
                {filteredHistory.length} contacts with message history
              </p>
            </div>
          </div>
          <Button variant="ghost" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Controls */}
        <div className="p-6 border-b space-y-4">
          {/* Search and View Controls */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search contacts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'compact' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('compact')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'detailed' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('detailed')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2">
                  Active
                </Badge>
              )}
            </Button>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div>
                <label className="text-sm font-medium mb-2 block">Date Range</label>
                <div className="space-y-2">
                  <Input
                    type="date"
                    value={filters.dateRange.start}
                    onChange={(e) => setFilters(prev => ({ 
                      ...prev, 
                      dateRange: { ...prev.dateRange, start: e.target.value } 
                    }))}
                    placeholder="Start date"
                  />
                  <Input
                    type="date"
                    value={filters.dateRange.end}
                    onChange={(e) => setFilters(prev => ({ 
                      ...prev, 
                      dateRange: { ...prev.dateRange, end: e.target.value } 
                    }))}
                    placeholder="End date"
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Location</label>
                <div className="space-y-2">
                  <Input
                    placeholder="City"
                    value={filters.city}
                    onChange={(e) => setFilters(prev => ({ ...prev, city: e.target.value }))}
                  />
                  <Input
                    placeholder="State"
                    value={filters.state}
                    onChange={(e) => setFilters(prev => ({ ...prev, state: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Message Count</label>
                <div className="space-y-2">
                  <Input
                    type="number"
                    placeholder="Min messages"
                    value={filters.minMessages}
                    onChange={(e) => setFilters(prev => ({ ...prev, minMessages: e.target.value }))}
                  />
                  <Input
                    type="number"
                    placeholder="Max messages"
                    value={filters.maxMessages}
                    onChange={(e) => setFilters(prev => ({ ...prev, maxMessages: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Options</label>
                <div className="space-y-2">
                  <Select value={filters.status} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="hasAttachments"
                      checked={filters.hasAttachments}
                      onChange={(e) => setFilters(prev => ({ ...prev, hasAttachments: e.target.checked }))}
                    />
                    <label htmlFor="hasAttachments" className="text-sm">Has Attachments</label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="hasLinks"
                      checked={filters.hasLinks}
                      onChange={(e) => setFilters(prev => ({ ...prev, hasLinks: e.target.checked }))}
                    />
                    <label htmlFor="hasLinks" className="text-sm">Has Links</label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sort Controls */}
          <div className="flex items-center gap-4">
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="city">City</SelectItem>
                <SelectItem value="messages">Message Count</SelectItem>
                <SelectItem value="lastMessage">Last Message</SelectItem>
                <SelectItem value="firstMessage">First Message</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
            </Button>

            {hasActiveFilters && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* Content */}
        <ScrollArea className="flex-1 p-6">
          {viewMode === 'compact' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredHistory.map((contact) => (
                <Card key={contact.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{contact.contactName}</CardTitle>
                      <Badge variant={contact.status === 'active' ? 'default' : 'secondary'}>
                        {contact.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{contact.contactPhone}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <MapPin className="w-4 h-4" />
                      {contact.contactCity}, {contact.contactState}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Messages Sent:</span>
                        <Badge variant="outline">{contact.totalMessages}</Badge>
                      </div>
                      
                      <div className="flex items-center justify-between text-sm">
                        <span>Last Message:</span>
                        <span>{new Date(contact.lastMessageDate).toLocaleDateString()}</span>
                      </div>

                      {(contact.attachments.length > 0 || contact.links.length > 0) && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          {contact.attachments.length > 0 && (
                            <div className="flex items-center gap-1">
                              <Download className="w-4 h-4" />
                              {contact.attachments.length} files
                            </div>
                          )}
                          {contact.links.length > 0 && (
                            <div className="flex items-center gap-1">
                              <LinkIcon className="w-4 h-4" />
                              {contact.links.length} links
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHistory.map((contact) => (
                <Card key={contact.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{contact.contactName}</CardTitle>
                        <p className="text-gray-600">{contact.contactPhone}</p>
                        <div className="flex items-center gap-2 text-sm text-gray-500 mt-1">
                          <MapPin className="w-4 h-4" />
                          {contact.contactCity}, {contact.contactState}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={contact.status === 'active' ? 'default' : 'secondary'}>
                          {contact.status}
                        </Badge>
                        <Badge variant="outline">
                          {contact.totalMessages} messages
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Message History */}
                      <div>
                        <h4 className="font-semibold mb-3">Message History</h4>
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {contact.messages.map((message) => (
                            <div key={message.id} className="border-l-4 border-green-500 pl-3">
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm font-medium">
                                  {new Date(message.sentAt).toLocaleString()}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {message.status}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-700 mb-2">{message.text}</p>
                              
                              {message.attachments.length > 0 && (
                                <div className="flex flex-wrap gap-2 mb-2">
                                  {message.attachments.map((att) => (
                                    <div key={att.id} className="flex items-center gap-1 text-xs text-gray-600">
                                      {getAttachmentIcon(att.type)}
                                      {att.name}
                                    </div>
                                  ))}
                                </div>
                              )}
                              
                              {message.links.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                  {message.links.map((link, index) => (
                                    <div key={index} className="flex items-center gap-1 text-xs text-blue-600">
                                      <LinkIcon className="w-3 h-3" />
                                      {link}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Attachments & Links Summary */}
                      <div>
                        <h4 className="font-semibold mb-3">Sent Data Summary</h4>
                        <div className="space-y-4">
                          {contact.attachments.length > 0 && (
                            <div>
                              <h5 className="text-sm font-medium mb-2">Attachments ({contact.attachments.length})</h5>
                              <div className="space-y-2">
                                {contact.attachments.map((att) => (
                                  <div key={att.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div className="flex items-center gap-2">
                                      {getAttachmentIcon(att.type)}
                                      <span className="text-sm">{att.name}</span>
                                    </div>
                                    <span className="text-xs text-gray-500">{formatFileSize(att.size)}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {contact.links.length > 0 && (
                            <div>
                              <h5 className="text-sm font-medium mb-2">Links ({contact.links.length})</h5>
                              <div className="space-y-2">
                                {contact.links.map((link, index) => (
                                  <div key={index} className="flex items-center gap-2 p-2 bg-blue-50 rounded">
                                    <LinkIcon className="w-4 h-4 text-blue-600" />
                                    <span className="text-sm text-blue-600 truncate">{link}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="pt-4 border-t">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-gray-600">First Message:</span>
                                <div>{new Date(contact.firstMessageDate).toLocaleDateString()}</div>
                              </div>
                              <div>
                                <span className="text-gray-600">Last Message:</span>
                                <div>{new Date(contact.lastMessageDate).toLocaleDateString()}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredHistory.length === 0 && (
            <div className="text-center py-12">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No contacts found</h3>
              <p className="text-gray-600">
                {hasActiveFilters 
                  ? "Try adjusting your filters or search terms." 
                  : "No WhatsApp message history available."
                }
              </p>
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
}; 