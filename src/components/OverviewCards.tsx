import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Pin, Star, CheckCircle, Bell, Tag, Users, MessageCircle } from 'lucide-react';
import { Contact } from '../types/Contact';

interface OverviewCardsProps {
  pinnedContacts: number;
  favoriteContacts: number;
  shortlistedContacts: number;
  reminderContacts: number;
  allContacts: number;
  filteredContacts: Contact[];
  allCategories: string[];
  currentFilter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned';
  onQuickFilter: (filter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned') => void;
  selectedCategories: string[];
  onCategoryClick: (category: string) => void;
  onWhatsAppHistoryClick?: () => void;
}

export const OverviewCards: React.FC<OverviewCardsProps> = ({
  pinnedContacts,
  favoriteContacts,
  shortlistedContacts,
  reminderContacts,
  allContacts,
  filteredContacts,
  allCategories,
  currentFilter,
  onQuickFilter,
  selectedCategories,
  onCategoryClick,
  onWhatsAppHistoryClick
}) => {
  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-blue-100 text-blue-800 hover:bg-blue-200',
      'bg-green-100 text-green-800 hover:bg-green-200',
      'bg-purple-100 text-purple-800 hover:bg-purple-200',
      'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      'bg-pink-100 text-pink-800 hover:bg-pink-200',
      'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
      'bg-red-100 text-red-800 hover:bg-red-200',
      'bg-teal-100 text-teal-800 hover:bg-teal-200',
    ];
    const index = category.length % colors.length;
    return colors[index];
  };

  const handleCardClick = (filter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned') => {
    // If the same filter is already active, clear it (go back to 'all')
    if (currentFilter === filter) {
      onQuickFilter('all');
    } else {
      onQuickFilter(filter);
    }
  };

  const getCardStyle = (filter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned') => {
    const isActive = currentFilter === filter;
    return `cursor-pointer transition-all duration-200 ${
      isActive 
        ? 'shadow-lg border-2 border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
        : 'hover:shadow-md border border-gray-200 dark:border-gray-700'
    }`;
  };

  const [showAllCategories, setShowAllCategories] = useState(false);
  const categoriesToShow = showAllCategories ? allCategories : allCategories.slice(0, 10);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 flex-shrink-0">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-4">
        <Card 
          className={getCardStyle('all')}
          onClick={() => handleCardClick('all')}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">All Contacts</p>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{allContacts}</p>
              </div>
              <Users className="w-6 h-6 text-gray-500" />
            </div>
          </CardContent>
        </Card>

        <Card 
          className={getCardStyle('pinned')}
          onClick={() => handleCardClick('pinned')}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Pinned Contacts</p>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{pinnedContacts}</p>
              </div>
              <Pin className="w-6 h-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card 
          className={getCardStyle('favorites')}
          onClick={() => handleCardClick('favorites')}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Favorites</p>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{favoriteContacts}</p>
              </div>
              <Star className="w-6 h-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card 
          className={getCardStyle('shortlisted')}
          onClick={() => handleCardClick('shortlisted')}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Shortlisted</p>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{shortlistedContacts}</p>
              </div>
              <CheckCircle className="w-6 h-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card 
          className={getCardStyle('reminders')}
          onClick={() => handleCardClick('reminders')}
        >
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-gray-600 dark:text-gray-400">Reminders</p>
                <p className="text-lg font-bold text-gray-900 dark:text-white">{reminderContacts}</p>
              </div>
              <Bell className="w-6 h-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Categories */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Tag className="w-4 h-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Categories</h3>
        </div>
        <div className="flex flex-wrap gap-1">
          {categoriesToShow.map((category) => (
            <Badge
              key={category}
              onClick={() => onCategoryClick(category)}
              className={`text-xs px-2 py-1 cursor-pointer transition-colors ${
                selectedCategories.includes(category)
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : getCategoryColor(category)
              }`}
            >
              {category}
              {selectedCategories.includes(category) && (
                <span className="ml-1">×</span>
              )}
            </Badge>
          ))}
          {allCategories.length > 10 && !showAllCategories && (
            <Badge
              className="text-xs px-2 py-1 bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200"
              onClick={() => setShowAllCategories(true)}
            >
              +{allCategories.length - 10} more
            </Badge>
          )}
          {allCategories.length > 10 && showAllCategories && (
            <Badge
              className="text-xs px-2 py-1 bg-gray-100 text-gray-600 cursor-pointer hover:bg-gray-200"
              onClick={() => setShowAllCategories(false)}
            >
              Show less
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};
