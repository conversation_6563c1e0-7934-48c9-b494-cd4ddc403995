import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, MessageCircle, CheckCircle } from 'lucide-react';
import { Contact } from '../types/Contact';
import { getContactHistoryByPhone } from '../data/whatsappHistory';

interface WhatsAppDuplicateWarningProps {
  contacts: Contact[];
  onConfirm: (applyToAll: boolean) => void;
  onCancel: () => void;
  isOpen: boolean;
}

export const WhatsAppDuplicateWarning: React.FC<WhatsAppDuplicateWarningProps> = ({
  contacts,
  onConfirm,
  onCancel,
  isOpen
}) => {
  const getContactsWithHistory = () => {
    return contacts.map(contact => {
      const history = getContactHistoryByPhone(contact.phone);
      return {
        contact,
        history,
        messageCount: history?.totalMessagesSent || 0
      };
    }).filter(item => item.messageCount > 0);
  };

  const contactsWithHistory = getContactsWithHistory();
  const totalDuplicateContacts = contactsWithHistory.length;

  if (totalDuplicateContacts === 0) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-amber-600">
            <AlertTriangle className="w-5 h-5" />
            Duplicate Message Warning
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You have previously sent messages to <strong>{totalDuplicateContacts}</strong> of the selected contacts. 
              Sending duplicate messages may be considered spam.
            </AlertDescription>
          </Alert>

          <div className="max-h-60 overflow-y-auto space-y-3">
            {contactsWithHistory.map(({ contact, history, messageCount }) => (
              <div key={contact.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <MessageCircle className="w-4 h-4 text-green-600" />
                    <span className="font-medium">{contact.name}</span>
                  </div>
                  <span className="text-sm text-gray-600">{contact.phone}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-amber-600 border-amber-300">
                    {messageCount} previous message{messageCount > 1 ? 's' : ''}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    Last: {history?.lastSentDate ? new Date(history.lastSentDate).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <CheckCircle className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              You can still proceed if you're sure the message is relevant and not spam.
            </span>
          </div>

          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onConfirm(false)}
              className="border-amber-300 text-amber-700 hover:bg-amber-50"
            >
              Send Anyway
            </Button>
            <Button 
              onClick={() => onConfirm(true)}
              className="bg-amber-600 hover:bg-amber-700"
            >
              Apply to All & Send
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 