import React, { useState, useRef } from 'react';
import { Contact } from '../types/Contact';

interface RequirementsHoverPopupProps {
  contact: Contact;
  children: React.ReactNode;
  className?: string;
}

export const RequirementsHoverPopup: React.FC<RequirementsHoverPopupProps> = ({
  contact,
  children,
  className = ''
}) => {
  const [showPopup, setShowPopup] = useState(false);
  const [popupPosition, setPopupPosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Don't show popup if there are no requirements
  if (!contact.requirements || contact.requirements.trim() === '') {
    return <div className={className}>{children}</div>;
  }

  const handleMouseEnter = (e: React.MouseEvent) => {
    setShowPopup(true);
    // Position popup at cursor location
    setPopupPosition({ x: e.clientX, y: e.clientY });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    // Update popup position as cursor moves
    setPopupPosition({ x: e.clientX, y: e.clientY });
  };

  const handleMouseLeave = () => {
    setShowPopup(false);
  };

  return (
    <div 
      ref={containerRef}
      className={`relative ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      {/* Hover Popup */}
      {showPopup && (
        <div 
          className="fixed z-50 pointer-events-none"
          style={{
            left: popupPosition.x + 10,
            top: popupPosition.y - 10,
            transform: 'translateY(-100%)'
          }}
        >
          <div className="bg-gray-900 text-white px-3 py-2 rounded-lg shadow-xl max-w-xs animate-in fade-in-0 zoom-in-95 duration-200">
            <p className="text-sm leading-relaxed whitespace-pre-wrap">
              {contact.requirements}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}; 