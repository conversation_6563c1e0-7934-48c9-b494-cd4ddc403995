import React from 'react';
import { Eye, Users, MessageCircle, ArrowLeft, Send, AlertCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { WhatsAppMessage } from '@/pages/WhatsAppSender';
import { Contact } from '@/types/Contact';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface MessageReviewProps {
  message: WhatsAppMessage;
  selectedContacts: Contact[];
  onConfirm: () => void;
  onBack: () => void;
  isConnected: boolean;
}

export const MessageReview: React.FC<MessageReviewProps> = ({
  message,
  selectedContacts,
  onConfirm,
  onBack,
  isConnected
}) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const totalAttachmentSize = message.attachments.reduce((total, att) => total + att.size, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Eye className="w-5 h-5" />
          <h2 className="text-xl font-semibold">Review & Confirm</h2>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Edit
          </Button>
          <Button 
            onClick={onConfirm} 
            className={`${isConnected ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}`}
            disabled={!isConnected}
          >
            <Send className="w-4 h-4 mr-2" />
            {isConnected ? 'Send Messages' : 'Connect WhatsApp First'}
          </Button>
        </div>
      </div>

      {/* Connection Warning */}
      {!isConnected && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>WhatsApp Not Connected:</strong> You need to connect to WhatsApp before sending messages. 
            Click "Connect WhatsApp First" to proceed to the connection step.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Message Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Message Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* WhatsApp-style Preview */}
            <div className="bg-gray-100 rounded-2xl p-4 max-w-sm mx-auto">
              <div className="bg-green-600 text-white p-3 rounded-t-lg flex items-center gap-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-sm">👤</span>
                </div>
                <div>
                  <div className="font-medium text-sm">Recipient</div>
                  <div className="text-xs opacity-80">will receive this message</div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-b-lg min-h-[200px] space-y-3">
                {/* Main Message Bubble */}
                {(message.text || message.attachments.length > 0) && (
                  <div className="bg-green-100 rounded-lg p-3 max-w-[90%] ml-auto">
                    {/* Text */}
                    {message.text && (
                      <div className="mb-2">
                        <p className="text-sm whitespace-pre-wrap">{message.text}</p>
                      </div>
                    )}

                    {/* Attachments */}
                    {message.attachments.map(attachment => (
                      <div key={attachment.id} className="mb-2">
                        {attachment.type === 'image' && (
                          <div className="relative">
                            <img
                              src={attachment.url}
                              alt={attachment.name}
                              className="max-w-full h-32 object-cover rounded"
                            />
                            <div className="absolute bottom-1 right-1 bg-black/50 text-white text-xs px-1 rounded">
                              📷 {attachment.name}
                            </div>
                          </div>
                        )}
                        {attachment.type === 'video' && (
                          <div className="bg-gray-200 p-3 rounded flex items-center gap-2">
                            <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">▶️</span>
                            </div>
                            <div className="flex-1">
                              <div className="text-xs font-medium">{attachment.name}</div>
                              <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                            </div>
                          </div>
                        )}
                        {attachment.type === 'document' && (
                          <div className="bg-blue-50 p-3 rounded flex items-center gap-2">
                            <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
                              <span className="text-white text-xs">📄</span>
                            </div>
                            <div className="flex-1">
                              <div className="text-xs font-medium">{attachment.name}</div>
                              <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Timestamp */}
                    <div className="flex items-center justify-end gap-1 mt-2">
                      <span className="text-xs text-gray-500">
                        {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      <span className="text-green-600 text-xs">✓✓</span>
                    </div>
                  </div>
                )}

                {/* Separate Link Message Bubbles */}
                {message.links.map((link, index) => (
                  <div key={index} className="bg-green-100 rounded-lg p-3 max-w-[90%] ml-auto">
                    {/* Link Preview */}
                    <div className="bg-gray-50 border rounded p-2">
                      <div className="flex items-center gap-2 mb-2">
                        <ExternalLink className="w-3 h-3 text-blue-600" />
                        <span className="text-xs text-blue-600 truncate">{link.url}</span>
                      </div>
                      {/* Link Preview Card */}
                                          <div className="bg-white rounded border p-2">
                      {/* Large thumbnail display */}
                      <div className="w-full mb-2">
                        {link.image && (
                          <img
                            src={link.image}
                            alt="Preview"
                            className="w-full h-32 object-cover rounded"
                            onLoad={() => console.log('✅ Image loaded successfully in review:', link.image)}
                            onError={(e) => {
                              console.error('❌ Image failed to load in review:', link.image);
                              const target = e.target as HTMLImageElement;
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDMwMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iNjQiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzlCOUJBMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
                              target.onerror = null; // Prevent further error handling
                            }}
                          />
                        )}
                        {!link.image && (
                          <div className="w-full h-32 bg-gray-200 rounded flex items-center justify-center">
                            <span className="text-2xl text-gray-500">📷</span>
                          </div>
                        )}
                      </div>
                      {/* Title only */}
                      <div className="w-full">
                        <h4 className="text-sm font-medium">{link.title}</h4>
                      </div>
                    </div>
                    </div>

                    {/* Timestamp */}
                    <div className="flex items-center justify-end gap-1 mt-2">
                      <span className="text-xs text-gray-500">
                        {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                      <span className="text-green-600 text-xs">✓✓</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Message Statistics */}
            <div className="mt-4 space-y-2 text-sm text-gray-600">
              <div>Character count: {message.text.length}</div>
              {message.attachments.length > 0 && (
                <div>Attachments: {message.attachments.length} ({formatFileSize(totalAttachmentSize)})</div>
              )}
              {message.links.length > 0 && (
                <div>Links: {message.links.length} (sent as separate messages)</div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recipients List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Recipients ({selectedContacts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {selectedContacts.map((contact, index) => (
                  <div key={contact.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-medium text-sm">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{contact.name}</div>
                        <div className="text-sm text-gray-600">{contact.phone}</div>
                        <div className="text-xs text-gray-500">{contact.city}, {contact.state}</div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      {contact.isFavorite && <Badge variant="secondary" className="text-xs">Favorite</Badge>}
                      {contact.isPinned && <Badge variant="secondary" className="text-xs">Pinned</Badge>}
                      {contact.isShortlisted && <Badge variant="secondary" className="text-xs">Shortlisted</Badge>}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Summary Card */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-blue-900">Ready to Send</h3>
              <p className="text-sm text-blue-700">
                Your message will be sent to {selectedContacts.length} contacts with a {message.links.length > 0 ? '5' : '3'}-second delay between each contact.
                {message.links.length > 0 && ' Links will be sent as separate messages with WhatsApp previews.'}
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-900">{selectedContacts.length}</div>
              <div className="text-sm text-blue-700">recipients</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
