import React from 'react';
import { Smartphone, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { WhatsAppMessage } from '@/pages/WhatsAppSender';

interface MessagePreviewProps {
  message: WhatsAppMessage;
}

export const MessagePreview: React.FC<MessagePreviewProps> = ({ message }) => {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Function to format WhatsApp text with bold, italic, and strikethrough
  const formatWhatsAppText = (text: string) => {
    // Replace WhatsApp formatting with HTML
    const formattedText = text
      .replace(/\*(.*?)\*/g, '<strong>$1</strong>') // Bold: *text*
      .replace(/_(.*?)_/g, '<em>$1</em>') // Italic: _text_
      .replace(/~(.*?)~/g, '<del>$1</del>'); // Strikethrough: ~text~

    // Split by line breaks and create paragraphs
    const lines = formattedText.split('\n');
    return lines.map((line, index) => (
      <div key={index} dangerouslySetInnerHTML={{ __html: line }} />
    ));
  };

  return (
    <Card className="sticky top-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="w-5 h-5" />
          WhatsApp Preview
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Phone Frame */}
        <div className="max-w-sm mx-auto bg-gray-100 rounded-3xl p-4 shadow-lg">
          {/* Phone Header */}
          <div className="bg-green-600 text-white p-3 rounded-t-lg flex items-center gap-3">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              <span className="text-sm">👤</span>
            </div>
            <div>
              <div className="font-medium text-sm">Contact Name</div>
              <div className="text-xs opacity-80">online</div>
            </div>
          </div>

          {/* Message Bubbles */}
          <div className="bg-white p-4 rounded-b-lg min-h-[200px] space-y-3">
            {/* Main Message Bubble */}
            {(message.text || message.attachments.length > 0) && (
              <div className="bg-green-100 rounded-lg p-3 max-w-[80%] ml-auto">
                {/* Text Message */}
                {message.text && (
                  <div className="mb-2">
                    <div className="text-sm whitespace-pre-wrap">
                      {formatWhatsAppText(message.text)}
                    </div>
                  </div>
                )}

                {/* Attachments */}
                {message.attachments.map(attachment => (
                  <div key={attachment.id} className="mb-2">
                    {attachment.type === 'image' && (
                      <div className="relative">
                        <img
                          src={attachment.url}
                          alt={attachment.name}
                          className="max-w-full h-32 object-cover rounded"
                        />
                        <div className="absolute bottom-1 right-1 bg-black/50 text-white text-xs px-1 rounded">
                          📷
                        </div>
                      </div>
                    )}
                    {attachment.type === 'video' && (
                      <div className="bg-gray-200 p-4 rounded flex items-center gap-2">
                        <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">▶️</span>
                        </div>
                        <div className="flex-1">
                          <div className="text-xs font-medium">{attachment.name}</div>
                          <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                        </div>
                      </div>
                    )}
                    {attachment.type === 'audio' && (
                      <div className="bg-purple-50 p-4 rounded flex items-center gap-2">
                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">🎵</span>
                        </div>
                        <div className="flex-1">
                          <div className="text-xs font-medium">{attachment.name}</div>
                          <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                        </div>
                      </div>
                    )}
                    {attachment.type === 'document' && (
                      <div className="bg-blue-50 p-3 rounded flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📄</span>
                        </div>
                        <div className="flex-1">
                          <div className="text-xs font-medium">{attachment.name}</div>
                          <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Message Time */}
                <div className="flex items-center justify-end gap-1 mt-2">
                  <span className="text-xs text-gray-500">
                    {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                  <span className="text-green-600 text-xs">✓✓</span>
                </div>
              </div>
            )}

            {/* Separate Link Message Bubbles */}
            {message.links.map((link, index) => (
              <div key={index} className="bg-green-100 rounded-lg p-3 max-w-[80%] ml-auto">
                {/* Link Preview */}
                <div className="bg-gray-50 border rounded p-2">
                  <div className="flex items-center gap-2 mb-2">
                    <ExternalLink className="w-3 h-3 text-blue-600" />
                    <span className="text-xs text-blue-600 truncate">{link.url}</span>
                  </div>
                  {/* Link Preview Card */}
                  <div className="bg-white rounded border p-2">
                    {/* Large thumbnail display */}
                    <div className="w-full mb-2">
                      {link.image && (
                        <img
                          src={link.image}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded"
                          onLoad={() => console.log('✅ Image loaded successfully:', link.image)}
                          onError={(e) => {
                            console.error('❌ Image failed to load:', link.image);
                            const target = e.target as HTMLImageElement;
                            target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDMwMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iNjQiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzlCOUJBMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
                            target.onerror = null; // Prevent further error handling
                          }}
                        />
                      )}
                      {!link.image && (
                        <div className="w-full h-32 bg-gray-200 rounded flex items-center justify-center">
                          <span className="text-2xl text-gray-500">📷</span>
                        </div>
                      )}
                    </div>
                    {/* Title only */}
                    <div className="w-full">
                      <h4 className="text-sm font-medium">{link.title}</h4>
                    </div>
                  </div>
                </div>

                {/* Message Time */}
                <div className="flex items-center justify-end gap-1 mt-2">
                  <span className="text-xs text-gray-500">
                    {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                  <span className="text-green-600 text-xs">✓✓</span>
                </div>
              </div>
            ))}

            {/* Empty State */}
            {!message.text && message.attachments.length === 0 && message.links.length === 0 && (
              <div className="text-center text-gray-400 py-8">
                <Smartphone className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Start composing your message to see the preview</p>
              </div>
            )}
          </div>
        </div>

        {/* Message Stats */}
        <div className="mt-4 text-center space-y-1">
          <div className="text-sm text-gray-600">
            Message Length: {message.text.length} characters
          </div>
          {message.attachments.length > 0 && (
            <div className="text-sm text-gray-600">
              Attachments: {message.attachments.length}
            </div>
          )}
          {message.links.length > 0 && (
            <div className="text-sm text-gray-600">
              Links: {message.links.length} (sent as separate messages)
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
