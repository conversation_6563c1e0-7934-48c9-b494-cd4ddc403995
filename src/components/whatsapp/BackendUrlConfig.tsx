import React, { useState, useEffect } from 'react';
import { Settings, Wifi, WifiOff, CheckCircle, XCircle, HelpCircle, Monitor, Network, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  getBackendUrl, 
  setBackendUrl, 
  testBackendConnection, 
  isValidUrl,
  getLocalNetworkSuggestions,
  COMMON_BACKEND_URLS 
} from '@/config/api';
import { useToast } from '@/hooks/use-toast';

interface BackendUrlConfigProps {
  onConnectionSuccess?: () => void;
}

export const BackendUrlConfig: React.FC<BackendUrlConfigProps> = ({ onConnectionSuccess }) => {
  const [currentUrl, setCurrentUrl] = useState(getBackendUrl());
  const [inputUrl, setInputUrl] = useState(currentUrl);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const { toast } = useToast();

  // Test connection on component mount
  useEffect(() => {
    testConnection(currentUrl);
  }, []);

  const testConnection = async (url: string) => {
    setIsLoading(true);
    try {
      const connected = await testBackendConnection(url);
      setIsConnected(connected);
      setLastChecked(new Date());
      
      if (connected) {
        toast({
          title: "✅ Backend Connected",
          description: `Successfully connected to ${url}`,
        });
        // Call the success callback if provided
        if (onConnectionSuccess) {
          onConnectionSuccess();
        }
      } else {
        toast({
          title: "❌ Connection Failed",
          description: `Cannot connect to ${url}. Make sure the backend is running.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      setIsConnected(false);
      toast({
        title: "❌ Connection Error",
        description: "Failed to test backend connection",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveUrl = async () => {
    if (!isValidUrl(inputUrl)) {
      toast({
        title: "❌ Invalid URL",
        description: "Please enter a valid URL (e.g., http://localhost:3001)",
        variant: "destructive",
      });
      return;
    }

    setBackendUrl(inputUrl);
    setCurrentUrl(inputUrl);
    await testConnection(inputUrl);
  };

  const handleQuickSelect = (url: string) => {
    setInputUrl(url);
  };

  const getConnectionStatus = () => {
    if (isLoading) return { icon: <Settings className="w-4 h-4 animate-spin" />, text: "Testing...", color: "bg-yellow-100 text-yellow-800" };
    if (isConnected) return { icon: <CheckCircle className="w-4 h-4" />, text: "Connected", color: "bg-green-100 text-green-800" };
    return { icon: <XCircle className="w-4 h-4" />, text: "Disconnected", color: "bg-red-100 text-red-800" };
  };

  const status = getConnectionStatus();

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2">
          {isConnected ? <Wifi className="w-4 h-4 text-green-600" /> : <WifiOff className="w-4 h-4 text-red-600" />}
          Backend Setup
          <Badge variant="outline" className={status.color}>
            {status.text}
          </Badge>
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Network className="w-5 h-5" />
            Backend Server Configuration
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                {status.icon}
                Connection Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Current Backend URL:</span>
                <code className="bg-gray-100 px-2 py-1 rounded text-sm">{currentUrl}</code>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status:</span>
                <Badge className={status.color}>
                  {status.icon}
                  {status.text}
                </Badge>
              </div>
              
              {lastChecked && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Last Checked:</span>
                  <span className="text-sm text-gray-500">{lastChecked.toLocaleTimeString()}</span>
                </div>
              )}
              
              <Button 
                onClick={() => testConnection(currentUrl)} 
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Settings className="w-4 h-4 animate-spin mr-2" /> : <Wifi className="w-4 h-4 mr-2" />}
                Test Connection
              </Button>
            </CardContent>
          </Card>

          {/* URL Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Change Backend URL</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Backend Server URL</label>
                <Input
                  value={inputUrl}
                  onChange={(e) => setInputUrl(e.target.value)}
                  placeholder="http://localhost:3001"
                  className="font-mono"
                />
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleSaveUrl} disabled={isLoading} className="flex-1">
                  Save & Test Connection
                </Button>
                <Button 
                  onClick={() => setInputUrl(currentUrl)} 
                  variant="outline"
                  disabled={isLoading}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Select Options */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Select Common URLs</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 gap-2">
                {COMMON_BACKEND_URLS.map((url) => (
                  <Button
                    key={url}
                    variant="outline"
                    onClick={() => handleQuickSelect(url)}
                    className="justify-start font-mono text-sm"
                  >
                    <Monitor className="w-4 h-4 mr-2" />
                    {url}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2 text-blue-900">
                <HelpCircle className="w-5 h-5" />
                Setup Help for Beginners
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-blue-800">
              <div className="space-y-2">
                <h4 className="font-semibold">Step 1: Start Your Backend Server</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Open terminal/command prompt on your PC</li>
                  <li>• Navigate to the backend folder</li>
                  <li>• Run: <code className="bg-blue-100 px-1 rounded">npm start</code> or <code className="bg-blue-100 px-1 rounded">node server.js</code></li>
                  <li>• You should see "Server running on port 3001"</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Step 2: Find Your Backend URL</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• <strong>Same PC:</strong> Use <code className="bg-blue-100 px-1 rounded">http://localhost:3001</code></li>
                  <li>• <strong>Different PC on same network:</strong> Use <code className="bg-blue-100 px-1 rounded">http://YOUR-PC-IP:3001</code></li>
                  <li>• To find your PC's IP: Run <code className="bg-blue-100 px-1 rounded">ipconfig</code> (Windows) or <code className="bg-blue-100 px-1 rounded">ifconfig</code> (Mac/Linux)</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-semibold">Step 3: Test Connection</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Enter your backend URL above</li>
                  <li>• Click "Save & Test Connection"</li>
                  <li>• You should see "Connected" status</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Troubleshooting */}
          {!isConnected && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Connection Failed?</strong> Check these:
                <ul className="mt-2 space-y-1 text-sm">
                  <li>• Is the backend server running?</li>
                  <li>• Is the URL correct?</li>
                  <li>• Is port 3001 accessible?</li>
                  <li>• Check firewall settings</li>
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}; 