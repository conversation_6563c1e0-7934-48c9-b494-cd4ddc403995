import React, { useState, useEffect } from 'react';
import { Send, CheckCircle, XCircle, AlertTriangle, RotateCcw, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { WhatsAppMessage, SentMessage } from '@/pages/WhatsAppSender';
import { Contact } from '@/types/Contact';
import { whatsAppService, SendMessageResult } from '@/services/whatsappService';
import { useToast } from '@/hooks/use-toast';

interface BulkSenderProps {
  message: WhatsAppMessage;
  contacts: Contact[];
  onSendComplete: (results: SentMessage[]) => void;
  onBack: () => void;
}

export const BulkSender: React.FC<BulkSenderProps> = ({
  message,
  contacts,
  onSendComplete,
  onBack
}) => {
  const [isSending, setIsSending] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [results, setResults] = useState<SentMessage[]>([]);
  const [progress, setProgress] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    checkConnection();
    
    // Set up periodic connection check every 5 seconds
    const interval = setInterval(checkConnection, 5000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  const checkConnection = async () => {
    const connected = await whatsAppService.isConnected();
    setIsConnected(connected);
  };

  const startSending = async () => {
    if (!isConnected) {
      toast({
        title: "Not Connected",
        description: "Please connect to WhatsApp first.",
        variant: "destructive",
      });
      return;
    }

    if (contacts.length === 0) {
      toast({
        title: "No Contacts",
        description: "Please select at least one contact to send messages to.",
        variant: "destructive",
      });
      return;
    }

    setIsSending(true);
    setCurrentIndex(0);
    setResults([]);
    setProgress(0);

    try {
      const sendResults: SentMessage[] = [];

      for (let i = 0; i < contacts.length; i++) {
        const contact = contacts[i];
        setCurrentIndex(i);
        setProgress((i / contacts.length) * 100);

        try {
          // Personalize message with contact name
          let personalizedMessage = message.text;
          if (contact.name) {
            personalizedMessage = message.text.replace(/\{\{name\}\}/g, contact.name);
          }

          console.log(`📤 Sending message to ${contact.name} (${contact.phone})...`);

          // Send message using WhatsApp service
          const result = await whatsAppService.sendMessage(
            contact.phone,
            personalizedMessage,
            message.attachments,
            message.links
          );

          console.log(`✅ Message sent successfully to ${contact.name}:`, result);

          const sentMessage: SentMessage = {
            id: result.messageId || `msg_${Date.now()}_${i}`,
            contactId: contact.id,
            contactName: contact.name,
            contactPhone: contact.phone,
            message: {
              ...message,
              text: personalizedMessage
            },
            status: result.success ? 'sent' : 'failed',
            sentAt: result.timestamp || new Date().toISOString(),
            retryCount: 0
          };

          sendResults.push(sentMessage);

          // Add delay between contacts to avoid rate limiting
          // Increased delay since we may be sending multiple messages per contact (main message + links)
          if (i < contacts.length - 1) {
            const delay = message.links && message.links.length > 0 ? 5000 : 3000;
            console.log(`⏳ Waiting ${delay / 1000} seconds before next contact...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }

        } catch (error) {
          console.error(`❌ Error sending message to ${contact.name}:`, error);
          
          // Determine the specific error type
          let errorStatus = 'failed';
          let errorMessage = 'Unknown error';
          
          if (error instanceof Error) {
            errorMessage = error.message;
            if (errorMessage.includes('not found') || errorMessage.includes('contact')) {
              errorStatus = 'contact_not_found';
            } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
              errorStatus = 'network_error';
            } else if (errorMessage.includes('rate limit') || errorMessage.includes('too many')) {
              errorStatus = 'rate_limited';
            }
          }
          
          const failedMessage: SentMessage = {
            id: `failed_${Date.now()}_${i}`,
            contactId: contact.id,
            contactName: contact.name,
            contactPhone: contact.phone,
            message: message,
            status: errorStatus as any,
            sentAt: new Date().toISOString(),
            retryCount: 0,
            error: errorMessage
          };

          sendResults.push(failedMessage);
        }
      }

      setProgress(100);
      setResults(sendResults);
      onSendComplete(sendResults);

      const successCount = sendResults.filter(r => r.status === 'sent').length;
      const failedCount = sendResults.filter(r => r.status !== 'sent').length;

      if (successCount > 0) {
        toast({
          title: "Sending Complete",
          description: `Successfully sent ${successCount} messages. ${failedCount} failed.`,
        });
      } else {
        toast({
          title: "Sending Failed",
          description: `All ${failedCount} messages failed to send. Check the results below.`,
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error('❌ Error in bulk sending:', error);
      toast({
        title: "Sending Failed",
        description: "An error occurred while sending messages. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSending(false);
    }
  };

  const retryMessage = async (result: SentMessage) => {
    try {
      console.log(`🔄 Retrying message to ${result.contactName}...`);
      
      // Personalize message with contact name
      let personalizedMessage = result.message.text;
      if (result.contactName) {
        personalizedMessage = result.message.text.replace(/\{\{name\}\}/g, result.contactName);
      }

      const retryResult = await whatsAppService.sendMessage(
        result.contactPhone,
        personalizedMessage,
        result.message.attachments,
        result.message.links
      );

      if (retryResult.success) {
        // Update the result in the results array
        setResults(prev => prev.map(r => 
          r.id === result.id 
            ? { ...r, status: 'sent', retryCount: r.retryCount + 1, error: undefined }
            : r
        ));
        
        toast({
          title: "Retry Successful",
          description: `Message to ${result.contactName} sent successfully.`,
        });
      } else {
        // Update the result with new error
        setResults(prev => prev.map(r => 
          r.id === result.id 
            ? { ...r, status: 'failed', retryCount: r.retryCount + 1, error: retryResult.error }
            : r
        ));
        
        toast({
          title: "Retry Failed",
          description: `Failed to send message to ${result.contactName}.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(`❌ Error retrying message to ${result.contactName}:`, error);
      
      // Update the result with new error
      setResults(prev => prev.map(r => 
        r.id === result.id 
          ? { ...r, status: 'failed', retryCount: r.retryCount + 1, error: error instanceof Error ? error.message : 'Unknown error' }
          : r
      ));
      
      toast({
        title: "Retry Failed",
        description: `Error retrying message to ${result.contactName}.`,
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'not_delivered':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
      case 'contact_not_found':
        return <XCircle className="w-4 h-4 text-orange-600" />;
      case 'network_error':
        return <AlertTriangle className="w-4 h-4 text-blue-600" />;
      case 'rate_limited':
        return <AlertTriangle className="w-4 h-4 text-purple-600" />;
      default:
        return <RotateCcw className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'not_delivered':
        return 'bg-yellow-100 text-yellow-800';
      case 'contact_not_found':
        return 'bg-orange-100 text-orange-800';
      case 'network_error':
        return 'bg-blue-100 text-blue-800';
      case 'rate_limited':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'sent':
        return 'Sent';
      case 'failed':
        return 'Failed';
      case 'not_delivered':
        return 'Not Delivered';
      case 'contact_not_found':
        return 'Contact Not Found';
      case 'network_error':
        return 'Network Error';
      case 'rate_limited':
        return 'Rate Limited';
      default:
        return 'Unknown';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            Send Messages
          </CardTitle>
          <Button variant="outline" onClick={onBack} size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Connection Status */}
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm">
            {isConnected ? 'WhatsApp Connected' : 'WhatsApp Not Connected'}
          </span>
        </div>

        {/* Progress */}
        {isSending && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Sending Progress</span>
              <span className="text-sm text-gray-600">
                {currentIndex + 1} of {contacts.length}
              </span>
            </div>
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-gray-600">
              Sending to: {contacts[currentIndex]?.name} ({contacts[currentIndex]?.phone})
            </p>
          </div>
        )}

        {/* Action Buttons */}
        {!isSending && results.length === 0 && (
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Ready to send message to {contacts.length} contact(s)
              </p>
              <Button
                onClick={startSending}
                disabled={!isConnected}
                className="bg-green-600 hover:bg-green-700"
              >
                <Send className="w-4 h-4 mr-2" />
                Start Sending
              </Button>
            </div>
          </div>
        )}

        {/* Results */}
        {results.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Sending Results</h3>
              <div className="flex gap-2">
                <Badge className={getStatusColor('sent')}>
                  {results.filter(r => r.status === 'sent').length} Sent
                </Badge>
                <Badge className={getStatusColor('failed')}>
                  {results.filter(r => r.status === 'failed').length} Failed
                </Badge>
                <Badge className={getStatusColor('contact_not_found')}>
                  {results.filter(r => r.status === 'contact_not_found').length} Not Found
                </Badge>
                <Badge className={getStatusColor('network_error')}>
                  {results.filter(r => r.status === 'network_error').length} Network
                </Badge>
              </div>
            </div>

            <ScrollArea className="h-64">
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div
                    key={result.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <p className="font-medium text-sm">{result.contactName}</p>
                        <p className="text-xs text-gray-600">{result.contactPhone}</p>
                        {result.error && (
                          <p className="text-xs text-red-600 mt-1">{result.error}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(result.status)}>
                        {getStatusText(result.status)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {new Date(result.sentAt).toLocaleTimeString()}
                      </span>
                      {result.status !== 'sent' && result.retryCount < 3 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => retryMessage(result)}
                          className="ml-2"
                        >
                          <RotateCcw className="w-3 h-3 mr-1" />
                          Retry
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Summary */}
        {results.length > 0 && !isSending && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Contacts:</span>
                <span className="ml-2 font-medium">{contacts.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Successfully Sent:</span>
                <span className="ml-2 font-medium text-green-600">
                  {results.filter(r => r.status === 'sent').length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Failed:</span>
                <span className="ml-2 font-medium text-red-600">
                  {results.filter(r => r.status === 'failed').length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Contact Not Found:</span>
                <span className="ml-2 font-medium text-orange-600">
                  {results.filter(r => r.status === 'contact_not_found').length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Network Errors:</span>
                <span className="ml-2 font-medium text-blue-600">
                  {results.filter(r => r.status === 'network_error').length}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Rate Limited:</span>
                <span className="ml-2 font-medium text-purple-600">
                  {results.filter(r => r.status === 'rate_limited').length}
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-gray-600">Success Rate:</span>
                <span className="ml-2 font-medium">
                  {Math.round((results.filter(r => r.status === 'sent').length / results.length) * 100)}%
                </span>
              </div>
            </div>
            
            {/* Action buttons after completion */}
            <div className="flex gap-3 mt-4 pt-4 border-t">
              <Button
                onClick={() => {
                  setResults([]);
                  setProgress(0);
                  setCurrentIndex(0);
                }}
                variant="outline"
                className="flex-1"
              >
                <Send className="w-4 h-4 mr-2" />
                Send New Message
              </Button>
              <Button
                onClick={onBack}
                variant="outline"
                className="flex-1"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Composer
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
