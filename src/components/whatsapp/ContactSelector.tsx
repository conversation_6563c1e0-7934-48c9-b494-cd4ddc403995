import React, { useState, useMemo } from 'react';
import { Users, Filter, Search, AlertTriangle, Grid, List, ArrowLeft, ChevronLeft, ChevronRight, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FilterPanel } from '@/components/FilterPanel';
import { Contact } from '@/types/Contact';
import { SentMessage } from '@/pages/WhatsAppSender';
// import { whatsappMessageService } from '@/services/whatsappMessageService'; // Service doesn't exist
import { RequirementsHoverPopup } from '../RequirementsHoverPopup';
import { ContactHistoryNotification } from './ContactHistoryNotification';

interface FilterOptions {
  states: string[];
  cities: string[];
  categories: string[];
  buildTypes: string[];
  showFavoriteOnly: boolean;
  showPinnedOnly: boolean;
  showShortlistedOnly: boolean;
  dateRange?: { start: string; end: string };
}

interface ContactSelectorProps {
  contacts: Contact[];
  selectedContacts: Contact[];
  onContactsSelected: (contacts: Contact[]) => void;
  onContinueToComposer: () => void;
  sentMessages: SentMessage[];
  onBack?: () => void;
  isLoading?: boolean;
}

export const ContactSelector: React.FC<ContactSelectorProps> = ({
  contacts,
  selectedContacts,
  onContactsSelected,
  onContinueToComposer,
  sentMessages,
  onBack,
  isLoading = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    states: [],
    cities: [],
    categories: [],
    buildTypes: [],
    showFavoriteOnly: false,
    showPinnedOnly: false,
    showShortlistedOnly: false
  });
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'compact' | 'detailed'>('compact');
  const [currentPage, setCurrentPage] = useState(1);
  const [showHistoryNotification, setShowHistoryNotification] = useState(false);
  const contactsPerPage = 25;

  // Filter contacts based on criteria
  const filteredContacts = useMemo(() => {
    let filtered = contacts;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.phone.includes(searchTerm) ||
        contact.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.state.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply preset filters
    switch (selectedFilter) {
      case 'favorites':
        filtered = filtered.filter(c => c.isFavorite);
        break;
      case 'pinned':
        filtered = filtered.filter(c => c.isPinned);
        break;
      case 'shortlisted':
        filtered = filtered.filter(c => c.isShortlisted);
        break;
      case 'reminders':
        filtered = filtered.filter(c => c.hasActiveReminder);
        break;
    }

    // Apply advanced filters
    if (filters.states.length > 0) {
      filtered = filtered.filter(c => filters.states.includes(c.state));
    }
    if (filters.cities.length > 0) {
      filtered = filtered.filter(c => filters.cities.includes(c.city));
    }
    if (filters.buildTypes.length > 0) {
      filtered = filtered.filter(c => filters.buildTypes.includes(c.buildType));
    }
    if (filters.showFavoriteOnly) {
      filtered = filtered.filter(c => c.isFavorite);
    }
    if (filters.showPinnedOnly) {
      filtered = filtered.filter(c => c.isPinned);
    }
    if (filters.showShortlistedOnly) {
      filtered = filtered.filter(c => c.isShortlisted);
    }

    // Apply date range filter
    if (filters.dateRange?.start && filters.dateRange?.end) {
      filtered = filtered.filter(c => {
        const contactDate = new Date(c.date);
        const startDate = new Date(filters.dateRange!.start);
        const endDate = new Date(filters.dateRange!.end);
        return contactDate >= startDate && contactDate <= endDate;
      });
    }

    return filtered;
  }, [contacts, searchTerm, selectedFilter, filters]);

  // Pagination logic
  const totalPages = Math.ceil(filteredContacts.length / contactsPerPage);
  const startIndex = (currentPage - 1) * contactsPerPage;
  const endIndex = startIndex + contactsPerPage;
  const currentContacts = filteredContacts.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filteredContacts.length]);

  // Check if contact already received message today
  const getContactWarning = (contact: Contact) => {
    const today = new Date().toDateString();
    const alreadySent = sentMessages.find(msg => 
      msg.contactPhone === contact.phone && 
      new Date(msg.sentAt).toDateString() === today
    );
    return alreadySent;
  };

  const handleContactToggle = async (contact: Contact) => {
    const isSelected = selectedContacts.find(c => c.id === contact.id);
    if (isSelected) {
      onContactsSelected(selectedContacts.filter(c => c.id !== contact.id));
    } else {
      const newSelectedContacts = [...selectedContacts, contact];
      onContactsSelected(newSelectedContacts);
      
      // Check if any of the selected contacts have message history
      const contactsWithHistory = newSelectedContacts.filter(c => 
        sentMessages.some(msg => msg.contactPhone === c.phone)
      );
      
      if (contactsWithHistory.length > 0) {
        setShowHistoryNotification(true);
      }
    }
  };

  const handleSelectAll = () => {
    if (selectedContacts.length === filteredContacts.length) {
      onContactsSelected([]);
    } else {
      onContactsSelected(filteredContacts);
    }
  };

  const handleApplyFilters = (newFilters: FilterOptions) => {
    setFilters(newFilters);
    setShowFilters(false);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleContinueToComposer = () => {
    // Call the parent component's handler to navigate to composer
    onContinueToComposer();
  };

  const renderCompactContact = (contact: Contact) => {
    const isSelected = selectedContacts.find(c => c.id === contact.id);
    const warning = getContactWarning(contact);
    const messageCount = sentMessages.filter(msg => msg.contactPhone === contact.phone).length;
    
    return (
      <div key={contact.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
        <div className="flex items-center space-x-3 flex-1">
          <Checkbox
            checked={!!isSelected}
            onCheckedChange={() => handleContactToggle(contact)}
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <RequirementsHoverPopup contact={contact}>
                <h4 className="font-medium truncate cursor-help">{contact.name}</h4>
              </RequirementsHoverPopup>
              {contact.isFavorite && <Badge variant="secondary" className="text-xs">★</Badge>}
              {contact.isPinned && <Badge variant="secondary" className="text-xs">📌</Badge>}
              {contact.isShortlisted && <Badge variant="secondary" className="text-xs">✓</Badge>}
              {messageCount > 0 && (
                <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                  <MessageCircle className="w-3 h-3 mr-1" />
                  {messageCount}
                </Badge>
              )}
            </div>
            <p className="text-sm text-gray-600 truncate">{contact.phone}</p>
            <p className="text-xs text-gray-500 truncate">{contact.city}, {contact.state}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {warning && (
            <div className="flex items-center gap-1 text-amber-600">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-xs">Sent today</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderDetailedContact = (contact: Contact) => {
    const isSelected = selectedContacts.find(c => c.id === contact.id);
    const warning = getContactWarning(contact);
    const messageCount = sentMessages.filter(msg => msg.contactPhone === contact.phone).length;
    
    return (
      <div key={contact.id} className="p-4 border rounded-lg hover:bg-gray-50">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3 flex-1">
            <Checkbox
              checked={!!isSelected}
              onCheckedChange={() => handleContactToggle(contact)}
              className="mt-1"
            />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <RequirementsHoverPopup contact={contact}>
                  <h4 className="font-medium cursor-help">{contact.name}</h4>
                </RequirementsHoverPopup>
                {contact.isFavorite && <Badge variant="secondary">Favorite</Badge>}
                {contact.isPinned && <Badge variant="secondary">Pinned</Badge>}
                {contact.isShortlisted && <Badge variant="secondary">Shortlisted</Badge>}
                {contact.hasActiveReminder && <Badge variant="outline">Reminder</Badge>}
                {messageCount > 0 && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                    <MessageCircle className="w-3 h-3 mr-1" />
                    {messageCount} messages
                  </Badge>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                <div>📞 {contact.phone}</div>
                <div>🏠 {contact.buildType}</div>
                <div>📍 {contact.city}, {contact.state}</div>
                <div>📅 {new Date(contact.date).toLocaleDateString()}</div>
              </div>
              {contact.requirements && (
                <p className="text-sm text-gray-600 mt-2 line-clamp-2">{contact.requirements}</p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {warning && (
              <div className="flex items-center gap-1 text-amber-600">
                <AlertTriangle className="w-4 h-4" />
                <span className="text-xs">Sent today</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pages = [];
      const maxVisiblePages = 5;
      
      if (totalPages <= maxVisiblePages) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) {
            pages.push(i);
          }
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) {
            pages.push(i);
          }
          pages.push('...');
          pages.push(totalPages);
        }
      }
      
      return pages;
    };

    return (
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Showing {startIndex + 1} to {Math.min(endIndex, filteredContacts.length)} of {filteredContacts.length} contacts
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="w-4 h-4" />
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {getPageNumbers().map((page, index) => (
              <div key={index}>
                {page === '...' ? (
                  <span className="px-2 text-gray-500">...</span>
                ) : (
                  <Button
                    variant={currentPage === page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePageChange(page as number)}
                    className="w-8 h-8 p-0"
                  >
                    {page}
                  </Button>
                )}
              </div>
            ))}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          )}
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            <h2 className="text-xl font-semibold">Select Contacts ({selectedContacts.length} selected)</h2>
          </div>
        </div>
      </div>

      {/* Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Select value={selectedFilter} onValueChange={setSelectedFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Contacts</SelectItem>
              <SelectItem value="favorites">Favorites</SelectItem>
              <SelectItem value="pinned">Pinned</SelectItem>
              <SelectItem value="shortlisted">Shortlisted</SelectItem>
              <SelectItem value="reminders">With Reminders</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="w-4 h-4" />
            Filters
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showFilters && (
        <FilterPanel
          contacts={contacts}
          onClose={() => setShowFilters(false)}
          onApplyFilters={handleApplyFilters}
        />
      )}

      {/* View Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">
            {filteredContacts.length} contacts found
          </span>
          {(Object.values(filters).some(v => v && (Array.isArray(v) ? v.length > 0 : true)) || selectedFilter !== 'all') && (
            <Badge variant="outline">Filtered</Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            disabled={filteredContacts.length === 0}
          >
            {selectedContacts.length === filteredContacts.length ? 'Deselect All' : 'Select All'}
          </Button>
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'compact' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('compact')}
              className="rounded-r-none"
            >
              <List className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'detailed' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('detailed')}
              className="rounded-l-none border-l"
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Contact List */}
      <Card>
        <CardContent className="p-4">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading contacts...</p>
            </div>
          ) : filteredContacts.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No contacts found matching your criteria.</p>
            </div>
          ) : (
            <>
              <div className={`space-y-3 ${
                viewMode === 'detailed' ? 'space-y-4' : 'space-y-2'
              }`}>
                {currentContacts.map(contact => 
                  viewMode === 'compact' 
                    ? renderCompactContact(contact)
                    : renderDetailedContact(contact)
                )}
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-6">
                  {renderPagination()}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Selected Contacts Summary */}
      {selectedContacts.length > 0 && (
        <Alert>
          <Users className="h-4 w-4" />
          <AlertDescription>
            You have selected <strong>{selectedContacts.length}</strong> contacts for messaging.
            {selectedContacts.filter(c => getContactWarning(c)).length > 0 && (
              <> <strong>{selectedContacts.filter(c => getContactWarning(c)).length}</strong> of them already received a message today.</>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Continue Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleContinueToComposer}
          disabled={selectedContacts.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          Continue with {selectedContacts.length} contacts
        </Button>
      </div>

      {/* Contact History Notification */}
      <ContactHistoryNotification
        contacts={selectedContacts}
        sentMessages={sentMessages}
        isOpen={showHistoryNotification}
        onClose={() => setShowHistoryNotification(false)}
      />
    </div>
  );
};
