import React, { useState, useEffect } from 'react';
import { Al<PERSON><PERSON>riangle, MessageCircle, Clock, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Contact } from '@/types/Contact';
import { SentMessage } from '@/pages/WhatsAppSender';

interface ContactHistoryNotificationProps {
  contacts: Contact[];
  sentMessages: SentMessage[];
  onClose: () => void;
  isOpen: boolean;
}

interface ContactWithHistory {
  phone: string;
  name: string;
  messageCount: number;
  lastMessageDate: string;
}

export const ContactHistoryNotification: React.FC<ContactHistoryNotificationProps> = ({
  contacts,
  sentMessages,
  onClose,
  isOpen
}) => {
  const [contactsWithHistory, setContactsWithHistory] = useState<ContactWithHistory[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Get contacts with previous message history
  useEffect(() => {
    const loadContactsWithHistory = () => {
      if (contacts.length === 0) {
        setContactsWithHistory([]);
        return;
      }

      setIsLoading(true);
      try {
        const history: ContactWithHistory[] = [];
        
        console.log('🔍 Checking history for contacts:', contacts.map(c => c.name));
        console.log('🔍 Available sent messages:', sentMessages.length);
        
        contacts.forEach(contact => {
          const contactMessages = sentMessages.filter(msg => msg.contactPhone === contact.phone);
          console.log(`🔍 Contact ${contact.name} (${contact.phone}): ${contactMessages.length} messages`);
          
          if (contactMessages.length > 0) {
            // Get the most recent message date
            const lastMessage = contactMessages.sort((a, b) => 
              new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime()
            )[0];
            
            history.push({
              phone: contact.phone,
              name: contact.name,
              messageCount: contactMessages.length,
              lastMessageDate: lastMessage.sentAt
            });
          }
        });
        
        console.log('✅ Contacts with history found:', history.length);
        setContactsWithHistory(history);
      } catch (error) {
        console.error('Error loading contact history:', error);
        setContactsWithHistory([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadContactsWithHistory();
  }, [contacts, sentMessages]);

  if (contactsWithHistory.length === 0 || isLoading) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMessageCountText = (count: number) => {
    if (count === 1) return '1 message';
    return `${count} messages`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-amber-500" />
            Previous Message History Found
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-900 mb-1">
                  Message History Detected
                </h4>
                <p className="text-sm text-amber-700">
                  {contactsWithHistory.length === 1 
                    ? `You have previously sent ${getMessageCountText(contactsWithHistory[0].messageCount)} to this contact.`
                    : `${contactsWithHistory.length} of the selected contacts have previous message history.`
                  }
                </p>
              </div>
            </div>
          </div>

          <ScrollArea className="max-h-96">
            <div className="space-y-3">
              {contactsWithHistory.map((contact) => (
                <Card key={contact.phone} className="border-amber-200 bg-amber-50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                          <User className="w-5 h-5 text-amber-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-amber-900">{contact.name}</h4>
                          <p className="text-sm text-amber-700">{contact.phone}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary" className="bg-amber-200 text-amber-800">
                          <MessageCircle className="w-3 h-3 mr-1" />
                          {getMessageCountText(contact.messageCount)}
                        </Badge>
                        <div className="text-xs text-amber-600 mt-1 flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatDate(contact.lastMessageDate)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>

          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Continue Anyway
            </Button>
            <Button onClick={onClose} className="bg-amber-600 hover:bg-amber-700">
              Got it
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 