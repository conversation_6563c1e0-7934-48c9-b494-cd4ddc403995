import React, { useState, useEffect, useRef } from 'react';
import { QrCode, Smartphone, CheckCircle, XCircle, RefreshCw, Server, Wifi, WifiOff, Terminal, Command } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { whatsAppService, WhatsAppStatus } from '@/services/whatsappService';
import { BackendUrlConfig } from './BackendUrlConfig';

interface WhatsAppConnectionProps {
  onConnectionSuccess: () => void;
}

export const WhatsAppConnection: React.FC<WhatsAppConnectionProps> = ({
  onConnectionSuccess
}) => {
  const [connectionStatus, setConnectionStatus] = useState<WhatsAppStatus>({ isConnected: false });
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [qrRefreshCountdown, setQrRefreshCountdown] = useState(15);
  const [showQR, setShowQR] = useState(false);
  const [backendConnected, setBackendConnected] = useState(false);
  const [serverMessages, setServerMessages] = useState<string[]>([]);
  const [connectionSuccessTriggered, setConnectionSuccessTriggered] = useState(false);
  
  const statusIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const qrIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Add server message
  const addServerMessage = (message: string) => {
    setServerMessages(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Handle backend connection success
  const handleBackendConnectionSuccess = () => {
    setBackendConnected(true);
    addServerMessage('Backend server connected successfully');
  };

  // Test backend connection on component mount
  useEffect(() => {
    const testInitialConnection = async () => {
      try {
        addServerMessage('Testing initial backend connection...');
        const result = await whatsAppService.testBackendConnection();
        if (result.success) {
          setBackendConnected(true);
          addServerMessage('Backend connection verified');
        } else {
          addServerMessage(`Backend connection failed: ${result.message}`);
        }
      } catch (error) {
        addServerMessage('Backend connection test failed');
        console.error('Initial backend connection test failed:', error);
      }
    };

    testInitialConnection();
  }, []);

  // QR code refresh countdown
  useEffect(() => {
    if (showQR && qrCode && !connectionStatus.isConnected && backendConnected) {
      const interval = setInterval(() => {
        setQrRefreshCountdown((prev) => {
          if (prev <= 1) {
            refreshQRCode();
            return 15;
          }
          return prev - 1;
        });
      }, 1000);

      qrIntervalRef.current = interval;
      
      return () => {
        if (qrIntervalRef.current) {
          clearInterval(qrIntervalRef.current);
        }
      };
    }
  }, [showQR, qrCode, connectionStatus.isConnected, backendConnected]);

  // Check connection status periodically using polling instead of Socket.IO
  useEffect(() => {
    if (!backendConnected) return;

    const checkStatus = async () => {
      try {
        const response = await fetch(`${whatsAppService.getApiBaseUrl()}/api/whatsapp/status`);
        const data = await response.json();
        
        const status: WhatsAppStatus = {
          isConnected: data.status === 'ready',
          qrCode: data.qrCode || undefined,
          phoneNumber: data.connectionInfo?.phoneNumber || 'Unknown',
          profileName: data.connectionInfo?.profileName || 'Unknown',
          deviceInfo: data.connectionInfo ? {
            platform: data.connectionInfo.platform,
            deviceType: data.connectionInfo.deviceType,
            businessName: data.connectionInfo.businessName,
            status: data.connectionInfo.status
          } : undefined,
          lastSeen: data.lastSeen,
          serverTime: data.serverTime,
          initializationInProgress: data.initializationInProgress
        };
        
        setConnectionStatus(status);
        
        // Handle QR code
        if (data.qrCode && data.status !== 'ready') {
          setQrCode(data.qrCode);
          setShowQR(true);
          setQrRefreshCountdown(15);
          setError(null);
          addServerMessage('QR code received');
        }
        
        // Handle connection success
        if (data.status === 'ready' && !connectionSuccessTriggered) {
          setShowQR(false);
          setQrCode(null);
          addServerMessage('WhatsApp connection detected!');
          onConnectionSuccess();
          setConnectionSuccessTriggered(true);
          // Stop polling once connected
          if (statusIntervalRef.current) {
            clearInterval(statusIntervalRef.current);
            statusIntervalRef.current = null;
          }
        }
        
        // Handle disconnection
        if (data.status !== 'ready' && connectionSuccessTriggered) {
          setConnectionSuccessTriggered(false);
          addServerMessage('WhatsApp disconnected');
        }
        
        // Handle initialization in progress
        if (data.initializationInProgress) {
          addServerMessage('WhatsApp initialization in progress...');
        }
        
      } catch (error) {
        console.error('Error checking status:', error);
        setError('Failed to check WhatsApp status');
      }
    };

    // Initial check
    checkStatus();
    
    // Set up interval for status checking - 2 seconds for responsive updates
    statusIntervalRef.current = setInterval(checkStatus, 2000);
    
    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current);
      }
    };
  }, [onConnectionSuccess, backendConnected, connectionSuccessTriggered]);

  // Handle page visibility changes (when user returns to tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && backendConnected) {
        console.log('📱 Page became visible - checking WhatsApp status...');
        addServerMessage('Page became visible - checking connection...');
        
        // Trigger an immediate status check
        const checkStatus = async () => {
          try {
            const response = await fetch(`${whatsAppService.getApiBaseUrl()}/api/whatsapp/status`);
            const data = await response.json();
            
            if (data.status === 'ready') {
              addServerMessage('WhatsApp connection verified');
              setConnectionStatus({
                isConnected: true,
                phoneNumber: data.connectionInfo?.phoneNumber || 'Unknown',
                profileName: data.connectionInfo?.profileName || 'Unknown'
              });
            } else {
              addServerMessage('WhatsApp not connected - ready to connect');
              setConnectionStatus({ isConnected: false });
            }
          } catch (error) {
            console.error('Error checking status on page visibility:', error);
            addServerMessage('Failed to check connection status');
          }
        };
        
        checkStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [backendConnected]);

  const getQRCode = async () => {
    try {
      addServerMessage('Requesting QR code...');
      const response = await fetch(`${whatsAppService.getApiBaseUrl()}/api/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      const data = await response.json();
      
      if (data.success) {
        addServerMessage('QR code request sent successfully');
        setError(null);
        // The QR code will be fetched by the polling mechanism
      } else {
        setError(data.message || 'Failed to get QR code');
        addServerMessage(`Error: ${data.message || 'Failed to get QR code'}`);
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Failed to get QR code';
      setError(errorMsg);
      addServerMessage(`Error: ${errorMsg}`);
    }
  };

  const refreshQRCode = async () => {
    addServerMessage('Refreshing QR code...');
    await getQRCode();
  };

  const handleConnect = async () => {
    if (!backendConnected) {
      setError('Please connect to backend first');
      return;
    }

    setIsConnecting(true);
    setError(null);
    addServerMessage('Connection requested...');
    await getQRCode();
    setIsConnecting(false);
  };

  const handleDisconnect = async () => {
    try {
      setIsConnecting(true);
      setError(null);
      addServerMessage('Disconnecting WhatsApp...');
      
      const result = await whatsAppService.disconnect();
      
      if (result.success) {
        setConnectionStatus({ isConnected: false });
        setShowQR(false);
        setQrCode(null);
        setConnectionSuccessTriggered(false);
        addServerMessage('WhatsApp disconnected successfully');
        
        // Clear any intervals
        if (statusIntervalRef.current) {
          clearInterval(statusIntervalRef.current);
        }
        if (qrIntervalRef.current) {
          clearInterval(qrIntervalRef.current);
        }
      } else {
        setError(result.message || 'Failed to disconnect');
        addServerMessage(`Error: ${result.message || 'Failed to disconnect'}`);
      }
    } catch (error) {
      console.error('Error disconnecting:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to disconnect';
      setError(errorMsg);
      addServerMessage(`Error: ${errorMsg}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleReconnect = async () => {
    try {
      setIsConnecting(true);
      setError(null);
      addServerMessage('Reconnecting WhatsApp...');
      
      const result = await whatsAppService.reconnect();
      
      if (result.success) {
        addServerMessage('WhatsApp reconnection started');
        setConnectionSuccessTriggered(false);
        // The polling mechanism will handle the rest
      } else {
        setError(result.message || 'Failed to reconnect');
        addServerMessage(`Error: ${result.message || 'Failed to reconnect'}`);
      }
    } catch (error) {
      console.error('Error reconnecting:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to reconnect';
      setError(errorMsg);
      addServerMessage(`Error: ${errorMsg}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleRefresh = async () => {
    setError(null);
    setQrRefreshCountdown(15);
    await handleConnect();
  };

  const handleClearSessions = async () => {
    try {
      setIsConnecting(true);
      setError(null);
      addServerMessage('Clearing WhatsApp sessions...');
      
      // First disconnect
      await handleDisconnect();
      
      // Then reconnect to get a fresh session
      setTimeout(async () => {
        await handleConnect();
      }, 2000);
      
      addServerMessage('WhatsApp sessions cleared, reconnecting...');
    } catch (error) {
      console.error('Error clearing sessions:', error);
      const errorMsg = error instanceof Error ? error.message : 'Failed to clear sessions';
      setError(errorMsg);
      addServerMessage(`Error: ${errorMsg}`);
    } finally {
      setIsConnecting(false);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (statusIntervalRef.current) {
        clearInterval(statusIntervalRef.current);
      }
      if (qrIntervalRef.current) {
        clearInterval(qrIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Backend Configuration */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900 rounded-2xl shadow-xl overflow-hidden">
              <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
                <h2 className="font-bold text-white flex items-center gap-2">
                  <Terminal className="w-5 h-5 text-green-400" />
                  Backend Configuration
                </h2>
              </div>
              
              <div className="p-6 space-y-4">
                {/* Terminal Prompt */}
                <div className="flex items-center gap-2 text-green-400 font-mono text-sm">
                  <span className="text-green-500">$</span>
                  <span>./configure-backend.sh</span>
                </div>
                
                {/* Configuration Component */}
                <div className="bg-gray-800 rounded border border-gray-600 p-4">
                  <BackendUrlConfig onConnectionSuccess={handleBackendConnectionSuccess} />
                </div>
                
                {/* Terminal Status */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-cyan-400 font-mono text-sm">
                    <span className="text-cyan-500">&gt;</span>
                    <span>Checking server status...</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300 font-mono text-sm">
                    <span className="text-gray-500">&gt;</span>
                    <span>Backend URL: {backendConnected ? '✓ Connected' : '✗ Not Connected'}</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-300 font-mono text-sm">
                    <span className="text-gray-500">&gt;</span>
                    <span>Server Health: {backendConnected ? '✓ Online' : '✗ Offline'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Terminal-style Server Status */}
            <div className="mt-6 bg-gray-900 rounded-2xl shadow-xl overflow-hidden">
              <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
                <h3 className="font-bold text-white flex items-center gap-2">
                  <Server className="w-5 h-5 text-blue-400" />
                  Server Logs
                </h3>
              </div>
              <div className="p-4 bg-black rounded-b-2xl">
                <div className="h-48 overflow-y-auto font-mono text-xs text-green-400 space-y-1">
                  {serverMessages.length === 0 ? (
                    <div className="text-gray-500">Waiting for server messages...</div>
                  ) : (
                    serverMessages.map((message, index) => (
                      <div key={index} className="text-green-400">
                        {message}
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - WhatsApp Connection */}
          <div className="lg:col-span-2 space-y-6">
            {/* WhatsApp Connection Card */}
            <div className="bg-white rounded-2xl shadow-lg border border-green-200 overflow-hidden">
              <div className="bg-green-50 px-6 py-4 border-b border-green-200">
                <h2 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Smartphone className="w-5 h-5 text-green-600" />
                  WhatsApp Connection
                </h2>
              </div>
              
              <div className="p-6 space-y-6">
                {/* WhatsApp Status */}
                <div className="flex items-center gap-4 p-4 bg-white rounded-xl shadow-sm border border-green-100">
                  <div className={`w-4 h-4 rounded-full ${connectionStatus.isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">
                      {connectionStatus.isConnected ? 'Connected to WhatsApp' : 'Not Connected'}
                    </p>
                    {connectionStatus.isConnected && connectionStatus.profileName && (
                      <p className="text-sm text-gray-600">
                        Profile: {connectionStatus.profileName}
                      </p>
                    )}
                    {connectionStatus.isConnected && connectionStatus.phoneNumber && (
                      <p className="text-sm text-gray-600">
                        Phone: {connectionStatus.phoneNumber}
                      </p>
                    )}
                    {connectionStatus.isConnected && connectionStatus.deviceInfo && (
                      <div className="text-xs text-gray-500 mt-1">
                        <p>Platform: {connectionStatus.deviceInfo.platform}</p>
                        <p>Device: {connectionStatus.deviceInfo.deviceType}</p>
                        {connectionStatus.deviceInfo.businessName && (
                          <p>Business: {connectionStatus.deviceInfo.businessName}</p>
                        )}
                        {connectionStatus.deviceInfo.status && (
                          <p>Status: {connectionStatus.deviceInfo.status}</p>
                        )}
                      </div>
                    )}
                  </div>
                  {connectionStatus.isConnected ? (
                    <CheckCircle className="w-6 h-6 text-green-500" />
                  ) : (
                    <XCircle className="w-6 h-6 text-red-500" />
                  )}
                </div>

                {/* QR Code */}
                {showQR && qrCode && !connectionStatus.isConnected && (
                  <div className="text-center space-y-4">
                    <div className="flex items-center justify-center gap-2">
                      <QrCode className="w-6 h-6 text-green-600" />
                      <h3 className="font-semibold text-gray-900 text-lg">Scan QR Code</h3>
                    </div>
                    <div className="flex justify-center">
                      <div className="p-6 bg-white border-2 border-green-200 rounded-2xl shadow-lg">
                        <img
                          src={qrCode}
                          alt="WhatsApp QR Code"
                          className="w-48 h-48 object-contain"
                          style={{ 
                            minWidth: '192px',
                            minHeight: '192px',
                            maxWidth: '300px',
                            maxHeight: '300px'
                          }}
                        />
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 space-y-2">
                      <p>Open WhatsApp on your phone and scan this QR code</p>
                      <p className="text-orange-600 font-medium">
                        Refreshes in {qrRefreshCountdown} seconds
                      </p>
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {error && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
                    <p className="text-red-800 text-sm">{error}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  {!connectionStatus.isConnected ? (
                    <>
                      <Button
                        onClick={handleConnect}
                        disabled={isConnecting || !backendConnected}
                        className="flex-1 bg-green-500 hover:bg-green-600 text-white border-0"
                        size="lg"
                      >
                        {isConnecting ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Connecting...
                          </>
                        ) : (
                          <>
                            <Smartphone className="w-4 h-4 mr-2" />
                            Connect WhatsApp
                          </>
                        )}
                      </Button>
                      {showQR && qrCode && (
                        <Button
                          variant="outline"
                          onClick={handleRefresh}
                          disabled={isConnecting}
                          size="lg"
                          className="border-green-300 text-green-700 hover:bg-green-50"
                        >
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Refresh
                        </Button>
                      )}
                    </>
                  ) : (
                    <div className="flex gap-3 w-full">
                      <Button
                        onClick={handleDisconnect}
                        disabled={isConnecting}
                        variant="outline"
                        size="lg"
                        className="flex-1 border-red-300 text-red-700 hover:bg-red-50"
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Disconnect
                      </Button>
                      <Button
                        onClick={handleReconnect}
                        disabled={isConnecting}
                        variant="outline"
                        size="lg"
                        className="border-orange-300 text-orange-700 hover:bg-orange-50"
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Reconnect
                      </Button>
                      <Button
                        onClick={handleClearSessions}
                        disabled={isConnecting}
                        variant="outline"
                        size="lg"
                        className="border-orange-300 text-orange-700 hover:bg-orange-50"
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Clear Sessions
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Quick Instructions */}
            <div className="bg-white rounded-2xl border border-green-200 shadow-lg overflow-hidden">
              <div className="bg-green-50 px-6 py-4 border-b border-green-200">
                <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                  <Command className="w-5 h-5 text-green-600" />
                  Quick Instructions
                </h3>
              </div>
              <div className="p-6">
                <div className="text-sm text-gray-600 space-y-2">
                  <ol className="list-decimal list-inside space-y-1">
                    <li>Ensure backend server is connected (left panel)</li>
                    <li>Click "Connect WhatsApp" to get QR code</li>
                    <li>Scan QR code with your WhatsApp mobile app</li>
                    <li>Wait for connection confirmation</li>
                    <li>Start sending messages to your contacts!</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
