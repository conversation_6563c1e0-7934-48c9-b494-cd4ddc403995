import React, { useState } from 'react';
import { History, MessageCircle, Calendar, User, Search, RotateCcw, ArrowLeft, Send, Filter, Trash2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SentMessage } from '@/pages/WhatsAppSender';
import { whatsAppService } from '@/services/whatsappService';
import { useToast } from '@/hooks/use-toast';

interface SentMessagesProps {
  sentMessages: SentMessage[];
  onStartNew: () => void;
  onBack: () => void;
  onClearHistory?: () => void;
}

export const SentMessages: React.FC<SentMessagesProps> = ({
  sentMessages,
  onStartNew,
  onBack,
  onClearHistory
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [retryingMessages, setRetryingMessages] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const filteredMessages = sentMessages.filter(msg => {
    const matchesSearch = msg.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         msg.contactPhone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || msg.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const groupedMessages = filteredMessages.reduce((groups, message) => {
    const date = new Date(message.sentAt).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {} as Record<string, SentMessage[]>);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'not_delivered': return 'bg-yellow-100 text-yellow-800';
      case 'contact_not_found': return 'bg-orange-100 text-orange-800';
      case 'network_error': return 'bg-blue-100 text-blue-800';
      case 'rate_limited': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return '✓✓';
      case 'failed': return '✗';
      case 'not_delivered': return '⚠️';
      case 'contact_not_found': return '👤';
      case 'network_error': return '🌐';
      case 'rate_limited': return '⏱️';
      default: return '?';
    }
  };

  const totalSent = sentMessages.filter(m => m.status === 'sent').length;
  const totalFailed = sentMessages.filter(m => ['failed', 'contact_not_found', 'network_error', 'rate_limited'].includes(m.status)).length;
  const totalNotDelivered = sentMessages.filter(m => m.status === 'not_delivered').length;

  // Retry function for single messages
  const retryMessage = async (message: SentMessage) => {
    if (retryingMessages.has(message.id)) return;
    
    setRetryingMessages(prev => new Set(prev).add(message.id));
    
    try {
      console.log(`🔄 Retrying message to ${message.contactName}...`);
      
      // Personalize message with contact name
      let personalizedMessage = message.message.text;
      if (message.contactName) {
        personalizedMessage = message.message.text.replace(/\{\{name\}\}/g, message.contactName);
      }

      const retryResult = await whatsAppService.sendMessage(
        message.contactPhone,
        personalizedMessage,
        message.message.attachments,
        message.message.links
      );

      if (retryResult.success) {
        // Update the message in the parent component
        const updatedMessage = {
          ...message,
          status: 'sent' as const,
          retryCount: message.retryCount + 1,
          error: undefined
        };
        
        // Update localStorage
        const existingMessages = localStorage.getItem('whatsapp_sent_messages');
        if (existingMessages) {
          const allMessages = JSON.parse(existingMessages);
          const updatedMessages = allMessages.map((msg: SentMessage) => 
            msg.id === message.id ? updatedMessage : msg
          );
          localStorage.setItem('whatsapp_sent_messages', JSON.stringify(updatedMessages));
        }
        
        toast({
          title: "Retry Successful",
          description: `Message to ${message.contactName} sent successfully.`,
        });
        
        // Force a page refresh to update the UI
        window.location.reload();
      } else {
        toast({
          title: "Retry Failed",
          description: `Failed to send message to ${message.contactName}.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error(`❌ Error retrying message to ${message.contactName}:`, error);
      toast({
        title: "Retry Failed",
        description: `Error retrying message to ${message.contactName}.`,
        variant: "destructive",
      });
    } finally {
      setRetryingMessages(prev => {
        const newSet = new Set(prev);
        newSet.delete(message.id);
        return newSet;
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <History className="w-5 h-5" />
          <h2 className="text-xl font-semibold">Sent Messages History</h2>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onBack} size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          {onClearHistory && sentMessages.length > 0 && (
            <Button variant="outline" onClick={onClearHistory} size="sm" className="text-red-600 hover:text-red-700">
              <RotateCcw className="w-4 h-4 mr-2" />
              Clear History
            </Button>
          )}
          <Button onClick={onStartNew} className="bg-green-600 hover:bg-green-700">
            <MessageCircle className="w-4 h-4 mr-2" />
            Send New Messages
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{sentMessages.length}</div>
            <div className="text-sm text-blue-700">Total Messages</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{totalSent}</div>
            <div className="text-sm text-green-700">Successfully Sent</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{totalFailed}</div>
            <div className="text-sm text-red-700">Failed</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{totalNotDelivered}</div>
            <div className="text-sm text-yellow-700">Not Delivered</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by contact name or phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="sent">Successfully Sent</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="not_delivered">Not Delivered</SelectItem>
                <SelectItem value="contact_not_found">Contact Not Found</SelectItem>
                <SelectItem value="network_error">Network Error</SelectItem>
                <SelectItem value="rate_limited">Rate Limited</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Messages List */}
      <Card>
        <CardHeader>
          <CardTitle>Message History ({filteredMessages.length} messages)</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(groupedMessages).length === 0 ? (
            <div className="text-center py-8">
              <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No messages found matching your criteria.</p>
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-6">
                {Object.entries(groupedMessages)
                  .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                  .map(([date, messages]) => (
                    <div key={date}>
                      <div className="flex items-center gap-2 mb-3">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <h3 className="font-medium text-gray-900">
                          {new Date(date).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </h3>
                        <Badge variant="outline">{messages.length} messages</Badge>
                      </div>
                      
                      <div className="space-y-2 pl-6">
                        {messages.map(message => (
                          <div key={message.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <User className="w-4 h-4 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium">{message.contactName}</div>
                                <div className="text-sm text-gray-600">{message.contactPhone}</div>
                                <div className="text-xs text-gray-500">
                                  {new Date(message.sentAt).toLocaleTimeString()}
                                </div>
                                {message.error && (
                                  <div className="text-xs text-red-600 mt-1">
                                    Error: {message.error}
                                  </div>
                                )}
                                <div className="text-xs text-gray-400 mt-1 max-w-xs truncate">
                                  {message.message.text}
                                </div>
                                {message.message.links && message.message.links.length > 0 && (
                                  <div className="text-xs text-blue-600 mt-1">
                                    📎 {message.message.links.length} link(s)
                                  </div>
                                )}
                                {message.message.attachments && message.message.attachments.length > 0 && (
                                  <div className="text-xs text-purple-600 mt-1">
                                    📎 {message.message.attachments.length} attachment(s)
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant="secondary" 
                                className={getStatusColor(message.status)}
                              >
                                {getStatusIcon(message.status)} {message.status.replace('_', ' ')}
                              </Badge>
                              
                              {message.retryCount > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  <RotateCcw className="w-3 h-3 mr-1" />
                                  {message.retryCount} retries
                                </Badge>
                              )}
                              
                              {message.status !== 'sent' && message.retryCount < 3 && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => retryMessage(message)}
                                  disabled={retryingMessages.has(message.id)}
                                  className="ml-2"
                                >
                                  <RotateCcw className={`w-3 h-3 mr-1 ${retryingMessages.has(message.id) ? 'animate-spin' : ''}`} />
                                  {retryingMessages.has(message.id) ? 'Retrying...' : 'Retry'}
                                </Button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {sentMessages.length === 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6 text-center">
            <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="font-semibold text-blue-900 mb-2">No Messages Sent Yet</h3>
            <p className="text-blue-700 mb-4">
              Start by sending your first bulk WhatsApp message to your contacts.
            </p>
            <Button onClick={onStartNew} className="bg-green-600 hover:bg-green-700">
              <MessageCircle className="w-4 h-4 mr-2" />
              Send Your First Message
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
