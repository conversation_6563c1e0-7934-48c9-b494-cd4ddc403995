import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, Paperclip, Link as LinkIcon, Save, Upload, Trash2, ArrowLeft, Bold, Italic, Strikethrough, X, Image as ImageIcon, FileText, Video, ExternalLink, Folder, Search, Info, ChevronDown, ChevronUp, AlertTriangle, Edit3, Copy, Loader2, Plus, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { WhatsAppMessage, MessageAttachment, MessageTemplate, LinkPreview } from '@/pages/WhatsAppSender';
import { Contact } from '@/types/Contact';
import { MediaFile } from '@/pages/MediaAttachments';
import { useToast } from '@/hooks/use-toast';
import { linkPreviewService } from '@/services/linkPreviewService';

// WhatsApp attachment limits
const WHATSAPP_LIMITS = {
  MAX_FILES_PER_MESSAGE: 10,
  MAX_FILE_SIZE_BYTES: 16 * 1024 * 1024, // 16MB
  MAX_FILE_SIZE_MB: 16,
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  SUPPORTED_VIDEO_TYPES: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'],
  SUPPORTED_AUDIO_TYPES: ['audio/mp3', 'audio/ogg', 'audio/wav', 'audio/m4a']
};

interface MessageComposerProps {
  message: WhatsAppMessage;
  onMessageChange: (message: WhatsAppMessage) => void;
  onCompose: (message: WhatsAppMessage) => void;
  onBack: () => void;
  selectedContacts: Contact[];
}

interface MediaFolder {
  id: string;
  name: string;
  files: MediaFile[];
  isFolder: boolean;
}

// Mock templates with enhanced structure
const mockTemplates: MessageTemplate[] = [
  {
    id: '1',
    name: 'Welcome Message',
    content: 'Hello {{name}}! Welcome to our service. We\'re excited to have you on board.',
    category: 'welcome',
    createdAt: new Date().toISOString(),
    attachments: [],
    links: []
  },
  {
    id: '2',
    name: 'Product Announcement',
    content: 'Hi {{name}}! Check out our latest product. We think you\'ll love it!',
    category: 'marketing',
    createdAt: new Date().toISOString(),
    attachments: [],
    links: [
      {
        url: 'https://example.com/product',
        title: 'Amazing Product - Check it out!',
        description: 'Discover our latest innovation that will change your life.',
        image: 'https://via.placeholder.com/300x200?text=Product+Image',
        siteName: 'example.com',
        isEditable: true
      }
    ]
  },
  {
    id: '3',
    name: 'Event Invitation',
    content: 'Hey {{name}}! You\'re invited to our special event. Please RSVP.',
    category: 'events',
    createdAt: new Date().toISOString(),
    attachments: [],
    links: [
      {
        url: 'https://example.com/event',
        title: 'Special Event - RSVP Now',
        description: 'Join us for an amazing evening of networking and fun.',
        image: 'https://via.placeholder.com/300x200?text=Event+Image',
        siteName: 'example.com',
        isEditable: true
      }
    ]
  }
];

export const MessageComposer: React.FC<MessageComposerProps> = ({
  message,
  onMessageChange,
  onCompose,
  onBack,
  selectedContacts
}) => {
  const [templates, setTemplates] = useState<MessageTemplate[]>(mockTemplates);
  const [isTemplateDialogOpen, setIsTemplateDialogOpen] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [showWhatsAppInstructions, setShowWhatsAppInstructions] = useState(false);
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false);
  const [selectedMediaFiles, setSelectedMediaFiles] = useState<MediaFile[]>([]);
  const [mediaFolders, setMediaFolders] = useState<MediaFolder[]>([]);
  const [currentMediaFolder, setCurrentMediaFolder] = useState<MediaFolder | null>(null);
  const [mediaBreadcrumb, setMediaBreadcrumb] = useState<MediaFolder[]>([]);
  const [mediaSearchTerm, setMediaSearchTerm] = useState('');
  const { toast } = useToast();

  // New URL Links State
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [newLinkUrl, setNewLinkUrl] = useState('');
  const [newLinkTitle, setNewLinkTitle] = useState('');
  const [isLoadingLinkPreview, setIsLoadingLinkPreview] = useState(false);
  const [editingLinkIndex, setEditingLinkIndex] = useState<number | null>(null);
  const [editingLinkTitle, setEditingLinkTitle] = useState('');

  useEffect(() => {
    const savedTemplates = localStorage.getItem('messageTemplates');
    
    if (savedTemplates) {
      try {
        const parsedTemplates = JSON.parse(savedTemplates);
        setTemplates([...mockTemplates, ...parsedTemplates]);
      } catch (error) {
        console.error('Error loading templates:', error);
      }
    }
  }, []);

  if (!message) {
    return <div className="text-red-600">Error: No message to compose.</div>;
    }

  const handleTextChange = (text: string) => {
    onMessageChange({
      ...message,
      text
    });
      };
      
  const validateFileCount = (currentCount: number, newCount: number): boolean => {
    return currentCount + newCount <= WHATSAPP_LIMITS.MAX_FILES_PER_MESSAGE;
  };

  const validateFileType = (file: File): boolean => {
    const allSupportedTypes = [
      ...WHATSAPP_LIMITS.SUPPORTED_IMAGE_TYPES,
      ...WHATSAPP_LIMITS.SUPPORTED_VIDEO_TYPES,
      ...WHATSAPP_LIMITS.SUPPORTED_AUDIO_TYPES,
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'text/plain',
      'text/csv'
    ];
    return allSupportedTypes.includes(file.type);
  };

  const validateFileSize = (file: File): boolean => {
    return file.size <= WHATSAPP_LIMITS.MAX_FILE_SIZE_BYTES;
  };

  const getFileType = (file: File): 'image' | 'video' | 'audio' | 'document' => {
    if (WHATSAPP_LIMITS.SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      return 'image';
    } else if (WHATSAPP_LIMITS.SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      return 'video';
    } else if (WHATSAPP_LIMITS.SUPPORTED_AUDIO_TYPES.includes(file.type)) {
      return 'audio';
    } else {
    return 'document';
    }
  };

  const handleRemoveAttachment = (attachmentId: string) => {
    onMessageChange({
      ...message,
      attachments: message.attachments.filter(att => att.id !== attachmentId)
    });
  };

  // New URL Links Functions
  const testLinkPreview = async () => {
    console.log('🧪 Testing link preview service...');
    
    // Clear cache to ensure fresh data
    linkPreviewService.clearCache();
    console.log('🗑️ Cache cleared');
    
    try {
      // Test 1: Regular preview
      const testUrl = 'https://www.google.com';
      const preview = await linkPreviewService.fetchPreview(testUrl);
      console.log('✅ Test preview result:', preview);
      
      // Test 2: Custom title preview
      const customTitle = 'My Custom Title: Test';
      const customPreview = await linkPreviewService.fetchPreviewWithCustomTitle(testUrl, customTitle);
      console.log('✅ Custom title preview result:', customPreview);
      
      toast({
        title: "Test Successful",
        description: `Regular: "${preview.title}" | Custom: "${customPreview.title}"`,
      });
    } catch (error) {
      console.error('❌ Test failed:', error);
      toast({
        title: "Test Failed",
        description: "Link preview service is not working properly.",
        variant: "destructive",
      });
    }
  };

  const handleAddLink = async () => {
    if (!newLinkUrl.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }
      
      // Validate URL format
      try {
      new URL(newLinkUrl.trim());
      } catch (error) {
        toast({
          title: "Invalid URL",
          description: "Please enter a valid URL (e.g., https://example.com)",
          variant: "destructive",
        });
        return;
      }
      
    setIsLoadingLinkPreview(true);
    
    try {
      const url = newLinkUrl.trim();
      const customTitle = newLinkTitle.trim();
      
      console.log('🔗 Fetching preview for URL:', url, 'with custom title:', customTitle);
      
      // Use the appropriate method based on whether custom title is provided
      const linkPreview = customTitle 
        ? await linkPreviewService.fetchPreviewWithCustomTitle(url, customTitle)
        : await linkPreviewService.fetchPreview(url);
      
      console.log('✅ Final link preview:', linkPreview);
      
      onMessageChange({
        ...message,
        links: [...message.links, linkPreview]
      });

      // Reset form
      setNewLinkUrl('');
      setNewLinkTitle('');
      setIsLinkDialogOpen(false);

        toast({
        title: "Link Added Successfully",
          description: `Preview for ${new URL(url).hostname} loaded successfully.`,
        });
    } catch (error) {
      console.error('❌ Error fetching link preview:', error);
      
      // Create fallback preview
      const url = newLinkUrl.trim();
      const customTitle = newLinkTitle.trim();
      const urlObj = new URL(url);
      const fallbackPreview: LinkPreview = {
        url,
        title: customTitle || urlObj.hostname.replace('www.', ''),
        description: '', // Empty for clean look
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDMwMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ci8+Cjx0ZXh0IHg9IjE1MCIgeT0iNjQiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzlCOUJBMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K',
        siteName: urlObj.hostname,
        isEditable: true
      };
      
      onMessageChange({
        ...message,
        links: [...message.links, fallbackPreview]
      });

      // Reset form
      setNewLinkUrl('');
      setNewLinkTitle('');
      setIsLinkDialogOpen(false);

      toast({
        title: "Link Added",
        description: `Link added with fallback preview. You can edit the title manually.`,
      });
    } finally {
      setIsLoadingLinkPreview(false);
    }
  };

  const handleRemoveLink = (index: number) => {
    const updatedLinks = message.links.filter((_, i) => i !== index);
    onMessageChange({
      ...message,
      links: updatedLinks
    });
    
    toast({
      title: "Link Removed",
      description: "Link has been removed from the message.",
    });
  };

  const handleEditLinkStart = (index: number) => {
    setEditingLinkIndex(index);
    setEditingLinkTitle(message.links[index].title);
  };

  const handleEditLinkSave = (index: number) => {
    if (editingLinkTitle.trim()) {
      const updatedLinks = [...message.links];
      // Preserve the custom title exactly as entered (no cleaning)
      updatedLinks[index] = {
        ...updatedLinks[index],
        title: editingLinkTitle.trim()
      };
      
      onMessageChange({
        ...message,
        links: updatedLinks
      });
        
      setEditingLinkIndex(null);
      setEditingLinkTitle('');
      
      toast({
        title: "Title Updated",
        description: "Link title has been updated successfully.",
      });
    }
  };

  const handleEditLinkCancel = () => {
    setEditingLinkIndex(null);
    setEditingLinkTitle('');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileArray = Array.from(files);
      
      // Check file count limit
      if (!validateFileCount(message.attachments.length, fileArray.length)) {
        toast({
          title: "Too Many Files",
          description: `You can only attach up to ${WHATSAPP_LIMITS.MAX_FILES_PER_MESSAGE} files per message.`,
          variant: "destructive",
        });
        return;
      }

      const newAttachments: MessageAttachment[] = [];
      const invalidFiles: string[] = [];

      fileArray.forEach((file: File) => {
        // Validate file type
        if (!validateFileType(file)) {
          invalidFiles.push(`${file.name} (unsupported type)`);
          return;
        }

        // Validate file size
        if (!validateFileSize(file)) {
          invalidFiles.push(`${file.name} (too large - max ${WHATSAPP_LIMITS.MAX_FILE_SIZE_MB}MB)`);
          return;
        }

        const attachment: MessageAttachment = {
          id: Date.now().toString() + Math.random(),
          name: file.name,
          type: getFileType(file),
          url: URL.createObjectURL(file),
          size: file.size
        };
        newAttachments.push(attachment);
      });

      // Add valid attachments
      if (newAttachments.length > 0) {
        onMessageChange({
          ...message,
          attachments: [...message.attachments, ...newAttachments]
        });
      }

      // Show errors for invalid files
      if (invalidFiles.length > 0) {
      toast({
          title: "Some Files Skipped",
          description: `The following files were not added: ${invalidFiles.join(', ')}`,
        variant: "destructive",
      });
      }
    }
  };

  const loadTemplate = (template: MessageTemplate) => {
    // Only load text and links from template, preserve existing attachments
    // unless the template has attachments, then use template attachments
    onMessageChange({
      ...message,
      text: template.content,
      attachments: template.attachments.length > 0 ? template.attachments : message.attachments,
      links: template.links
    });
    setIsTemplateDialogOpen(false);
    
    toast({
      title: "Template Loaded",
      description: `Template "${template.name}" has been loaded.${template.attachments.length > 0 ? ' Template attachments have been applied.' : ' Your existing attachments have been preserved.'}`,
    });
  };

  const saveAsTemplate = () => {
    if (newTemplateName.trim() && message.text.trim()) {
      const newTemplate: MessageTemplate = {
        id: Date.now().toString(),
        name: newTemplateName.trim(),
        content: message.text,
        category: 'custom',
        createdAt: new Date().toISOString(),
        attachments: message.attachments,
        links: message.links
      };
      
      // Add to templates array
      const updatedTemplates = [...templates, newTemplate];
      setTemplates(updatedTemplates);
      
      // Save to localStorage
      const customTemplates = updatedTemplates.filter(t => t.category === 'custom');
      localStorage.setItem('messageTemplates', JSON.stringify(customTemplates));
      
      setNewTemplateName('');
      toast({
        title: "Template Saved",
        description: `Template "${newTemplate.name}" has been saved successfully.`,
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Media Library Functions
  const getFilteredMediaItems = () => {
    let items: (MediaFile | MediaFolder)[] = currentMediaFolder ? currentMediaFolder.files : [];
    
    if (mediaSearchTerm) {
      items = items.filter((item) => 
        item.name.toLowerCase().includes(mediaSearchTerm.toLowerCase())
      );
    }
    
    return {
      files: items.filter((item): item is MediaFile => !('isFolder' in item) || !item.isFolder),
      folders: items.filter((item): item is MediaFolder => 'isFolder' in item && item.isFolder)
    };
  };

  const handleMediaFileSelect = (file: MediaFile) => {
    const isSelected = selectedMediaFiles.find(f => f.id === file.id);
    
    if (isSelected) {
      setSelectedMediaFiles(prev => prev.filter(f => f.id !== file.id));
    } else {
      setSelectedMediaFiles(prev => [...prev, file]);
    }
  };

  const handleAddSelectedMedia = () => {
    const newAttachments: MessageAttachment[] = selectedMediaFiles.map(file => {
      // Map MediaFile types to MessageAttachment types
      let attachmentType: 'image' | 'video' | 'audio' | 'document';
      
      switch (file.type) {
        case 'image':
          attachmentType = 'image';
          break;
        case 'video':
          attachmentType = 'video';
          break;
        case 'pdf':
        case 'excel':
        case 'document':
        case 'other':
          attachmentType = 'document';
          break;
        default:
          attachmentType = 'document';
      }

      return {
        id: file.id,
        name: file.name,
        type: attachmentType,
        url: file.url,
        size: file.size || 0
      };
    });

    onMessageChange({
      ...message,
      attachments: [...message.attachments, ...newAttachments]
    });

    setSelectedMediaFiles([]);
    setIsMediaDialogOpen(false);
    
    toast({
      title: "Media Added",
      description: `${selectedMediaFiles.length} media file(s) added to the message.`,
    });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            Compose Message
          </CardTitle>
          <Button variant="outline" onClick={onBack} size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Selected Contacts */}
        {selectedContacts.length > 0 && (
          <div>
            <label className="block text-sm font-medium mb-2">Selected Contacts ({selectedContacts.length})</label>
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
              {selectedContacts.map(contact => (
                <Badge key={contact.id} variant="secondary" className="text-xs">
                  {contact.name}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Template Actions */}
        <div>
          <label className="block text-sm font-medium mb-2">Message Templates</label>
          <div className="flex gap-2 mb-3">
          <Dialog open={isTemplateDialogOpen} onOpenChange={setIsTemplateDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                  <FileText className="w-4 h-4 mr-2" />
                Load Template
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                  <DialogTitle>Select a Template</DialogTitle>
              </DialogHeader>
                <div className="space-y-3">
                {templates.map(template => (
                    <div
                      key={template.id}
                      className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                      onClick={() => loadTemplate(template)}
                    >
                    <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{template.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          {template.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{template.content}</p>
                    
                    {/* Template attachments and links info */}
                    <div className="flex gap-4 text-xs text-gray-500">
                      {template.attachments.length > 0 && (
                        <span>📎 {template.attachments.length} attachment(s)</span>
                      )}
                      {template.links.length > 0 && (
                        <span>🔗 {template.links.length} link(s)</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>

          <div className="flex gap-2">
            <Input
              placeholder="Template name"
              value={newTemplateName}
              onChange={(e) => setNewTemplateName(e.target.value)}
              className="w-48"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={saveAsTemplate}
              disabled={!newTemplateName.trim() || !message.text.trim()}
            >
              <Save className="w-4 h-4 mr-1" />
              Save Template
            </Button>
            </div>
          </div>
        </div>

        {/* Message Text with Formatting */}
        <div>
          <label className="block text-sm font-medium mb-2">Message Text</label>
          <div className="space-y-2">
            <div className="flex gap-2 mb-2">
            <Button
                variant="outline"
              size="sm"
                onClick={() => {
                  const textarea = document.getElementById('message-textarea') as HTMLTextAreaElement;
                  const start = textarea.selectionStart;
                  const end = textarea.selectionEnd;
                  const text = message.text;
                  const before = text.substring(0, start);
                  const selected = text.substring(start, end);
                  const after = text.substring(end);
                  const newText = before + '*' + selected + '*' + after;
                  handleTextChange(newText);
                }}
            >
              <Bold className="w-4 h-4" />
            </Button>
            <Button
                variant="outline"
              size="sm"
                onClick={() => {
                  const textarea = document.getElementById('message-textarea') as HTMLTextAreaElement;
                  const start = textarea.selectionStart;
                  const end = textarea.selectionEnd;
                  const text = message.text;
                  const before = text.substring(0, start);
                  const selected = text.substring(start, end);
                  const after = text.substring(end);
                  const newText = before + '_' + selected + '_' + after;
                  handleTextChange(newText);
                }}
            >
              <Italic className="w-4 h-4" />
            </Button>
            <Button
                variant="outline"
              size="sm"
                onClick={() => {
                  const textarea = document.getElementById('message-textarea') as HTMLTextAreaElement;
                  const start = textarea.selectionStart;
                  const end = textarea.selectionEnd;
                  const text = message.text;
                  const before = text.substring(0, start);
                  const selected = text.substring(start, end);
                  const after = text.substring(end);
                  const newText = before + '~' + selected + '~' + after;
                  handleTextChange(newText);
                }}
            >
              <Strikethrough className="w-4 h-4" />
            </Button>
          </div>

          <div className="relative">
            <Textarea
              id="message-textarea"
              placeholder="Type your message here... Use *bold*, _italic_, ~strikethrough~ formatting. Insert {{name}} for personalization."
              value={message.text}
              onChange={(e) => handleTextChange(e.target.value)}
              rows={6}
              className="resize-none border-2 border-gray-800 focus:border-gray-900 focus:ring-2 focus:ring-gray-900 focus:ring-opacity-20 min-h-[150px] transition-all duration-200 text-base leading-relaxed p-4 bg-white shadow-sm"
              style={{
                minHeight: '150px',
                height: 'auto',
                overflow: 'hidden'
              }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = 'auto';
                target.style.height = Math.max(150, target.scrollHeight) + 'px';
              }}
            />
            <div className="absolute bottom-2 right-2 text-xs text-gray-400 bg-white px-2 py-1 rounded">
              {message.text.length} characters
              </div>
            </div>
          </div>
        </div>

        {/* Media Selection from MediaAttachments */}
        <div>
          <label className="block text-sm font-medium mb-2">Media & Attachments</label>
          <div className="space-y-3">
            <div className="flex gap-2">
              <Dialog open={isMediaDialogOpen} onOpenChange={setIsMediaDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Folder className="w-4 h-4 mr-2" />
                    Select from Media Library
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh]">
                  <DialogHeader>
                    <DialogTitle>Select Media Files</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    {/* Breadcrumb Navigation */}
                    {mediaBreadcrumb.length > 0 && (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentMediaFolder(null)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Folder className="w-4 h-4 mr-1" />
                          Root
                        </Button>
                        {mediaBreadcrumb.map((folder, index) => (
                          <div key={folder.id} className="flex items-center gap-2">
                            <span className="text-gray-400">/</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setCurrentMediaFolder(folder)}
                              className="text-blue-600 hover:text-blue-700"
                            >
                              {folder.name}
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Search and Actions */}
                    <div className="flex items-center gap-2">
                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Search media files..."
                          value={mediaSearchTerm}
                          onChange={(e) => setMediaSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                        <Button
                          variant="outline"
                        onClick={() => setShowWhatsAppInstructions(!showWhatsAppInstructions)}
                      >
                        <Info className="w-4 h-4" />
                        </Button>
                    </div>

                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {/* Folders */}
                        {getFilteredMediaItems().folders.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Folders</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                              {getFilteredMediaItems().folders.map(folder => (
                                <div
                                  key={folder.id}
                                  className="relative border rounded-lg p-3 cursor-pointer transition-colors hover:border-blue-300 hover:bg-blue-50"
                                  onClick={() => setCurrentMediaFolder(folder)}
                                >
                                  <div className="flex flex-col items-center text-center">
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2">
                                      <Folder className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <span className="text-xs font-medium truncate w-full">{folder.name}</span>
                                    <span className="text-xs text-gray-500">
                                      {folder.files.length} files
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Files */}
                        {getFilteredMediaItems().files.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Files</h4>
                            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                              {getFilteredMediaItems().files.map(file => {
                                const isSelected = selectedMediaFiles.find(f => f.id === file.id);
                                return (
                                  <div
                                    key={file.id}
                                    className={`relative border rounded-lg p-3 cursor-pointer transition-colors ${
                                      isSelected ? 'border-blue-500 bg-blue-50' : 'hover:border-gray-300'
                                    }`}
                                    onClick={() => handleMediaFileSelect(file)}
                                  >
                                    <div className="flex flex-col items-center text-center">
                                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                                        {file.type === 'image' && <ImageIcon className="w-6 h-6 text-green-600" />}
                                        {file.type === 'video' && <Video className="w-6 h-6 text-red-600" />}
                                        {(file.type === 'pdf' || file.type === 'excel' || file.type === 'document' || file.type === 'other') && <FileText className="w-6 h-6 text-blue-600" />}
                                        </div>
                                      <span className="text-xs font-medium truncate w-full">{file.name}</span>
                                      <span className="text-xs text-gray-500">
                                        {file.size ? formatFileSize(file.size) : 'Unknown size'}
                                      </span>
                                    </div>
                                    {isSelected && (
                                      <div className="absolute top-2 right-2 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span className="text-white text-xs">✓</span>
                                      </div>
                                    )}
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}

                        {/* Empty State */}
                        {getFilteredMediaItems().files.length === 0 && getFilteredMediaItems().folders.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            <Folder className="w-12 h-12 mx-auto mb-2 opacity-50" />
                            <p>No media files found</p>
                          </div>
                        )}
                      </div>
                    </ScrollArea>

                    {/* Selected Files Summary */}
                    {selectedMediaFiles.length > 0 && (
                      <div className="border-t pt-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium">
                            Selected: {selectedMediaFiles.length} file(s)
                          </span>
                                <Button
                                  variant="outline"
                                  size="sm"
                            onClick={() => setSelectedMediaFiles([])}
                                >
                            Clear All
                                </Button>
                              </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={handleAddSelectedMedia}
                            className="flex-1"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add to Message
                          </Button>
                        </div>
                          </div>
                        )}
                  </div>
                </DialogContent>
              </Dialog>

              <Button variant="outline" onClick={() => document.getElementById('file-upload')?.click()}>
                <Upload className="w-4 h-4 mr-2" />
                Upload Files
              </Button>
                <input
                  id="file-upload"
                type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="hidden"
                accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv"
              />
            </div>

            {/* Current Attachments */}
            {message.attachments.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Current Attachments ({message.attachments.length})</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {message.attachments.map(attachment => (
                    <div key={attachment.id} className="flex items-center gap-3 p-2 border rounded-lg">
                      <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                        {attachment.type === 'image' && <ImageIcon className="w-4 h-4 text-green-600" />}
                        {attachment.type === 'video' && <Video className="w-4 h-4 text-red-600" />}
                        {attachment.type === 'audio' && <span className="text-purple-600 text-sm">🎵</span>}
                        {attachment.type === 'document' && <FileText className="w-4 h-4 text-blue-600" />}
                              </div>
                        <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{attachment.name}</div>
                        <div className="text-xs text-gray-500">{formatFileSize(attachment.size)}</div>
                          </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAttachment(attachment.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* WhatsApp Instructions */}
            <Button
              variant="ghost"
              size="sm"
            onClick={() => setShowWhatsAppInstructions(!showWhatsAppInstructions)}
              className="text-blue-600 hover:text-blue-700"
            >
              {showWhatsAppInstructions ? <ChevronUp className="w-4 h-4 mr-1" /> : <ChevronDown className="w-4 h-4 mr-1" />}
              WhatsApp File Requirements
            </Button>
          
          {showWhatsAppInstructions && (
            <div className="mt-3 space-y-2 text-sm text-blue-700">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-2">📁 Supported File Types:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• <strong>Images:</strong> JPG, PNG, WebP</li>
                    <li>• <strong>Videos:</strong> MP4, AVI, MOV, WMV</li>
                    <li>• <strong>Documents:</strong> PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV</li>
                    <li>• <strong>Audio:</strong> MP3, OGG, WAV, M4A</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">⚠️ Limitations:</h4>
                  <ul className="space-y-1 text-xs">
                    <li>• <strong>Max files per message:</strong> {WHATSAPP_LIMITS.MAX_FILES_PER_MESSAGE}</li>
                    <li>• <strong>Max file size:</strong> {WHATSAPP_LIMITS.MAX_FILE_SIZE_MB}MB per file</li>
                    <li>• <strong>Current attachments:</strong> {message.attachments.length}/{WHATSAPP_LIMITS.MAX_FILES_PER_MESSAGE}</li>
                  </ul>
                </div>
              </div>
              <div className="mt-3 p-2 bg-blue-100 rounded text-xs">
                <strong>💡 Tip:</strong> Each message will be sent with all its attachments first, then the next message will be sent after a 3-second delay to ensure proper delivery.
              </div>
            </div>
          )}
          </div>
        </div>

        {/* URL Links Section */}
        <div>
          <label className="block text-sm font-medium mb-2">URL Links</label>
          <div className="space-y-3">
            {/* Add Link Button */}
            <div className="flex gap-2">
              <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" className="flex-1">
                    <Plus className="w-4 h-4 mr-2" />
                    Add URL Link
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                  <DialogHeader>
                    <DialogTitle>Add URL Link</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">URL</label>
              <Input
                        placeholder="https://example.com"
                        value={newLinkUrl}
                        onChange={(e) => setNewLinkUrl(e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Custom Title (Optional)</label>
                      <Input
                        placeholder="Leave empty to use auto-generated title"
                        value={newLinkTitle}
                        onChange={(e) => setNewLinkTitle(e.target.value)}
              />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={handleAddLink}
                        disabled={!newLinkUrl.trim() || isLoadingLinkPreview}
                        className="flex-1"
                      >
                        {isLoadingLinkPreview ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Loading...
                          </>
                        ) : (
                          <>
                            <LinkIcon className="w-4 h-4 mr-2" />
                            Add Link
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsLinkDialogOpen(false);
                          setNewLinkUrl('');
                          setNewLinkTitle('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="outline"
                onClick={testLinkPreview}
                className="px-3"
                title="Test link preview service"
              >
                🧪
              </Button>
            </div>

            {/* Current Links */}
            {message.links.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Current Links ({message.links.length})</h4>
                {message.links.map((link, index) => (
                  <div key={index} className="border rounded-lg overflow-hidden bg-white">
                    {/* Link Header */}
                    <div className="flex items-center justify-between p-3 bg-gray-50 border-b">
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <Globe className="w-4 h-4 text-blue-600 flex-shrink-0" />
                        <span className="text-sm text-gray-600 truncate">{link.url}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditLinkStart(index)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit3 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveLink(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Link Preview */}
                    <div className="p-3">
                      {/* Large Thumbnail */}
                      <div className="w-full mb-3">
                        {link.image && (
                          <img
                            src={link.image}
                            alt="Preview"
                            className="w-full h-32 object-cover rounded border"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDMwMCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iNjQiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzlCOUJBMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
                              target.onerror = null; // Prevent further error handling
                            }}
                          />
                        )}
                        {!link.image && (
                          <div className="w-full h-32 bg-gray-200 rounded border flex items-center justify-center">
                            <span className="text-2xl text-gray-500">📷</span>
                          </div>
                        )}
                      </div>
                      
                      {/* Title and Edit Controls */}
                      <div className="w-full">
                        {editingLinkIndex === index ? (
                          <div className="space-y-2">
                            <Input
                              value={editingLinkTitle}
                              onChange={(e) => setEditingLinkTitle(e.target.value)}
                              className="text-sm"
                              placeholder="Enter title..."
                            />
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleEditLinkSave(index)}
                                disabled={!editingLinkTitle.trim()}
                                className="text-xs"
                              >
                                <Save className="w-3 h-3 mr-1" />
                                Save
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={handleEditLinkCancel}
                                className="text-xs"
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">{link.title}</h4>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Compose Button */}
        <Button
          onClick={() => onCompose({ ...message, id: Date.now().toString() })}
          disabled={!message.text.trim()}
          className="w-full bg-green-600 hover:bg-green-700"
        >
          <MessageCircle className="w-4 h-4 mr-2" />
          Preview Message
        </Button>
      </CardContent>
    </Card>
  );
};
