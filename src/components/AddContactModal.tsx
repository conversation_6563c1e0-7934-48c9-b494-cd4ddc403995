import React, { useState, useEffect } from 'react';
import { X, Plus, Save } from 'lucide-react';
import { Contact } from '../types/Contact';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getAllStates, getCitiesByState } from '../data/indianStatesAndCities';

interface AddContactModalProps {
  onClose: () => void;
  onAdd: (contact: Contact) => void;
  existingCategories: string[];
  existingBuildTypes: string[];
}

export const AddContactModal: React.FC<AddContactModalProps> = ({
  onClose,
  onAdd,
  existingCategories,
  existingBuildTypes
}) => {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    requirements: '',
    categories: [] as string[],
    state: '',
    city: '',
    date: new Date().toISOString().split('T')[0],
    buildType: 'Residential',
    notes: ''
  });
  const [newCategory, setNewCategory] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availableCities, setAvailableCities] = useState<string[]>([]);

  // Get all Indian states
  const allStates = getAllStates();

  // Update cities when state changes
  useEffect(() => {
    if (formData.state) {
      const cities = getCitiesByState(formData.state);
      setAvailableCities(cities);
      
      // Clear city if it's not in the new state's cities
      if (formData.city && !cities.includes(formData.city)) {
        setFormData(prev => ({ ...prev, city: '' }));
      }
    } else {
      setAvailableCities([]);
      setFormData(prev => ({ ...prev, city: '' }));
    }
  }, [formData.state, formData.city]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    if (!formData.state.trim()) {
      newErrors.state = 'State is required';
    }

    if (!formData.city.trim()) {
      newErrors.city = 'City is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const newContact: Contact = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: formData.name.trim(),
      phone: formData.phone.trim(),
      requirements: formData.requirements.trim(),
      categories: formData.categories,
      state: formData.state.trim(),
      city: formData.city.trim(),
      date: formData.date,
      isFavorite: false,
      isPinned: false,
      isShortlisted: false,
      notes: formData.notes.trim(),
      attachments: [],
      buildType: formData.buildType,
      kanbanStage: 'lead',
      reminders: [],
      hasActiveReminder: false
    };

    onAdd(newContact);
    onClose();
  };

  const addCategory = () => {
    if (newCategory.trim() && !formData.categories.includes(newCategory.trim())) {
      setFormData(prev => ({
        ...prev,
        categories: [...prev.categories, newCategory.trim()]
      }));
      setNewCategory('');
    }
  };

  const removeCategory = (categoryToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.filter(cat => cat !== categoryToRemove)
    }));
  };

  const getCategoryColor = (category: string) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-purple-100 text-purple-800',
      'bg-orange-100 text-orange-800',
      'bg-red-100 text-red-800',
      'bg-yellow-100 text-yellow-800',
      'bg-indigo-100 text-indigo-800',
      'bg-pink-100 text-pink-800'
    ];
    return colors[category.length % colors.length];
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            Add New Contact
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter full name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <Label htmlFor="phone" className="text-sm font-medium text-gray-700">
                Phone Number *
              </Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+91 9876543210"
                className={errors.phone ? 'border-red-500' : ''}
              />
              {errors.phone && <p className="text-red-500 text-xs mt-1">{errors.phone}</p>}
            </div>

            <div>
              <Label htmlFor="state" className="text-sm font-medium text-gray-700">
                State *
              </Label>
              <Select
                value={formData.state}
                onValueChange={(value) => setFormData(prev => ({ ...prev, state: value }))}
              >
                <SelectTrigger className={errors.state ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select state" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  {allStates.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.state && <p className="text-red-500 text-xs mt-1">{errors.state}</p>}
            </div>

            <div>
              <Label htmlFor="city" className="text-sm font-medium text-gray-700">
                City *
              </Label>
              <Select
                value={formData.city}
                onValueChange={(value) => setFormData(prev => ({ ...prev, city: value }))}
                disabled={!formData.state}
              >
                <SelectTrigger className={errors.city ? 'border-red-500' : ''}>
                  <SelectValue placeholder={formData.state ? "Select city" : "Select state first"} />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                  {availableCities.map((city) => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.city && <p className="text-red-500 text-xs mt-1">{errors.city}</p>}
              {formData.state && availableCities.length === 0 && (
                <p className="text-gray-500 text-xs mt-1">No cities available for selected state</p>
              )}
            </div>

            <div>
              <Label htmlFor="buildType" className="text-sm font-medium text-gray-700">
                Build Type
              </Label>
              <Select
                value={formData.buildType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, buildType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Residential">Residential</SelectItem>
                  <SelectItem value="Commercial">Commercial</SelectItem>
                  <SelectItem value="Healthcare">Healthcare</SelectItem>
                  <SelectItem value="Institutional / Educational">Institutional / Educational</SelectItem>
                  <SelectItem value="Religious / Spiritual">Religious / Spiritual</SelectItem>
                  <SelectItem value="Government / Civic">Government / Civic</SelectItem>
                  <SelectItem value="Industrial">Industrial</SelectItem>
                  <SelectItem value="Recreational / Cultural / Sports">Recreational / Cultural / Sports</SelectItem>
                  <SelectItem value="Mixed-Use">Mixed-Use</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="date" className="text-sm font-medium text-gray-700">
                Date
              </Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
              />
            </div>
          </div>

          {/* Requirements */}
          <div>
            <Label htmlFor="requirements" className="text-sm font-medium text-gray-700">
              Requirements
            </Label>
            <Textarea
              id="requirements"
              value={formData.requirements}
              onChange={(e) => setFormData(prev => ({ ...prev, requirements: e.target.value }))}
              placeholder="Describe the project requirements..."
              rows={3}
            />
          </div>

          {/* Categories */}
          <div>
            <Label className="text-sm font-medium text-gray-700">
              Categories
            </Label>
            <div className="space-y-3">
              {/* Existing Categories */}
              <div className="flex flex-wrap gap-2">
                {formData.categories.map((category) => (
                  <Badge key={category} className={`${getCategoryColor(category)} cursor-pointer`}>
                    {category}
                    <button
                      type="button"
                      onClick={() => removeCategory(category)}
                      className="ml-2 hover:text-red-600"
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>

              {/* Add New Category */}
              <div className="flex gap-2">
                <Input
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder="Add new category"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCategory())}
                />
                <Button
                  type="button"
                  onClick={addCategory}
                  variant="outline"
                  size="sm"
                  disabled={!newCategory.trim()}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              {/* Quick Add Common Categories */}
              <div className="flex flex-wrap gap-1">
                {existingCategories.slice(0, 8).map((category) => (
                  <Button
                    key={category}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!formData.categories.includes(category)) {
                        setFormData(prev => ({
                          ...prev,
                          categories: [...prev.categories, category]
                        }));
                      }
                    }}
                    disabled={formData.categories.includes(category)}
                    className="text-xs"
                  >
                    + {category}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
              Notes
            </Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Add any additional notes..."
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
              <Save className="w-4 h-4 mr-2" />
              Add Contact
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}; 