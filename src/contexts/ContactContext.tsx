import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Contact } from '../types/Contact';
import { mysqlContactService } from '../services/mysqlContactService';
import { useToast } from '@/hooks/use-toast';

interface ContactContextType {
  contacts: Contact[];
  deletedContacts: Contact[];
  isLoading: boolean;
  storageInfo: any;
  addContact: (contact: Contact) => Promise<void>;
  updateContact: (contact: Contact) => Promise<void>;
  deleteContact: (id: string) => Promise<void>;
  restoreContact: (id: string) => Promise<void>;
  permanentlyDeleteContact: (id: string) => Promise<void>;
  importContacts: (contacts: Contact[]) => Promise<{ success: number; failed: number }>;
  refreshContacts: () => Promise<void>;
  refreshStorageDetection: () => Promise<void>;
}

const ContactContext = createContext<ContactContextType | undefined>(undefined);

interface ContactProviderProps {
  children: ReactNode;
}

export const ContactProvider: React.FC<ContactProviderProps> = ({ children }) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [deletedContacts, setDeletedContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const { toast } = useToast();

  // Initialize and load contacts on mount
  useEffect(() => {
    initializeAndLoadContacts();
  }, []);

  const initializeAndLoadContacts = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Initializing ContactContext with MySQL database...');

      // Load contacts using MySQL service
      const loadedContacts = await mysqlContactService.getAllContacts();
      setContacts(loadedContacts);
      setDeletedContacts([]); // MySQL service doesn't support soft deletes yet

      // Get storage info
      const info = mysqlContactService.getStorageInfo();
      setStorageInfo(info);

      console.log(`✅ ContactContext initialized with ${info.type}, loaded ${loadedContacts.length} contacts`);

      toast({
        title: `📊 Storage Mode: ${info.type}`,
        description: info.description,
        duration: 3000,
      });
    } catch (error) {
      console.error('❌ Failed to initialize contacts from MySQL database:', error);
      setContacts([]);
      setDeletedContacts([]);
      setStorageInfo({
        type: 'MySQL Database (Error)',
        description: 'Failed to connect to MySQL database. Please check your internet connection.',
        persistent: true
      });

      toast({
        title: '❌ Database Connection Failed',
        description: 'Could not connect to MySQL database. Please check your internet connection and try again.',
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshContacts = async () => {
    try {
      console.log('🔄 Refreshing contacts from MySQL database...');
      const loadedContacts = await mysqlContactService.getAllContacts();
      setContacts(loadedContacts);

      // MySQL service doesn't support soft deletes yet
      setDeletedContacts([]);

      console.log(`📋 Loaded ${loadedContacts.length} contacts from MySQL database`);
    } catch (error) {
      console.error('❌ Failed to refresh contacts from MySQL database:', error);
      toast({
        title: "❌ Error Loading Contacts",
        description: "Failed to load contacts from MySQL database. Please check your internet connection.",
        variant: "destructive",
      });
    }
  };

  const addContact = async (contact: Contact) => {
    try {
      console.log('📝 Adding contact to MySQL database:', contact.name);
      const addedContact = await mysqlContactService.addContact(contact);
      setContacts(prev => [...prev, addedContact]);

      toast({
        title: "✅ Contact Added",
        description: `${contact.name} has been added to the database successfully.`,
      });

      console.log('✅ Contact added to MySQL database:', contact.name);
    } catch (error) {
      console.error('❌ Failed to add contact to MySQL database:', error);
      toast({
        title: "❌ Error Adding Contact",
        description: "Failed to add contact to database. Please check your internet connection and try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const updateContact = async (updatedContact: Contact) => {
    try {
      console.log('📝 Updating contact in MySQL database:', updatedContact.name);
      const result = await mysqlContactService.updateContact(updatedContact);
      if (result) {
        setContacts(prev =>
          prev.map(contact =>
            contact.id === updatedContact.id ? result : contact
          )
        );

        toast({
          title: "✅ Contact Updated",
          description: `${updatedContact.name} has been updated in the database successfully.`,
        });

        console.log('✅ Contact updated in MySQL database:', updatedContact.name);
      }
    } catch (error) {
      console.error('❌ Failed to update contact in MySQL database:', error);
      toast({
        title: "❌ Error Updating Contact",
        description: "Failed to update contact in database. Please check your internet connection and try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const deleteContact = async (id: string) => {
    try {
      console.log('🗑️ Deleting contact from MySQL database:', id);
      const success = await mysqlContactService.deleteContact(id);
      if (success) {
        setContacts(prev => prev.filter(contact => contact.id !== id));

        toast({
          title: "✅ Contact Deleted",
          description: "Contact has been deleted from the database successfully.",
        });

        console.log('✅ Contact deleted from MySQL database:', id);
      }
    } catch (error) {
      console.error('❌ Failed to delete contact from MySQL database:', error);
      toast({
        title: "❌ Error Deleting Contact",
        description: "Failed to delete contact from database. Please check your internet connection and try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // MySQL service doesn't support soft deletes yet
  const restoreContact = async (id: string) => {
    console.log('ℹ️ Restore contact not implemented in MySQL service');
    toast({
      title: "ℹ️ Feature Not Available",
      description: "Restore contact feature is not implemented yet.",
    });
  };

  const permanentlyDeleteContact = async (id: string) => {
    // Same as regular delete in MySQL service
    await deleteContact(id);
  };

  const importContacts = async (contactsToImport: Contact[]): Promise<{ success: number; failed: number }> => {
    console.log(`📥 Importing ${contactsToImport.length} contacts to MySQL database...`);

    try {
      // Use the MySQL service's batch import method
      const result = await mysqlContactService.importContacts(contactsToImport);

      // Refresh contacts to get the updated list

      toast({
        title: "📥 Import Completed",
        description: `${result.success} contacts imported to database, ${result.failed} failed.`,
      });

      console.log(`✅ Import completed: ${result.success} success, ${result.failed} failed`);
      return result;
    } catch (error) {
      console.error('❌ Failed to import contacts to MySQL database:', error);
      toast({
        title: "❌ Import Error",
        description: "Failed to import contacts to database. Please check your internet connection and try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const refreshStorageDetection = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 Refreshing MySQL database connection...');

      // Test MySQL connection
      const isConnected = await mysqlContactService.testConnection();
      const info = mysqlContactService.getStorageInfo();
      setStorageInfo({
        ...info,
        description: isConnected ? info.description : 'MySQL database connection failed'
      });

      toast({
        title: `🔄 Storage Status: ${info.type}`,
        description: isConnected ? 'MySQL database connection successful' : 'MySQL database connection failed',
        variant: isConnected ? 'default' : 'destructive'
      });

      // Reload contacts if connected
      if (isConnected) {
        await refreshContacts();
      }
    } catch (error) {
      console.error('❌ Failed to refresh MySQL database connection:', error);
      toast({
        title: '❌ Connection Test Failed',
        description: 'Failed to test MySQL database connection',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const value: ContactContextType = {
    contacts,
    deletedContacts,
    isLoading,
    storageInfo,
    addContact,
    updateContact,
    deleteContact,
    restoreContact,
    permanentlyDeleteContact,
    importContacts,
    refreshContacts,
    refreshStorageDetection,
  };

  return (
    <ContactContext.Provider value={value}>
      {children}
    </ContactContext.Provider>
  );
};

export const useContacts = (): ContactContextType => {
  const context = useContext(ContactContext);
  if (context === undefined) {
    throw new Error('useContacts must be used within a ContactProvider');
  }
  return context;
}; 