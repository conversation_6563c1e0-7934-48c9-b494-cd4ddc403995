import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Contact } from '../types/Contact';
import { hybridContactService } from '../services/hybridContactService';
import { useToast } from '@/hooks/use-toast';

interface ContactContextType {
  contacts: Contact[];
  deletedContacts: Contact[];
  isLoading: boolean;
  storageInfo: any;
  addContact: (contact: Contact) => Promise<void>;
  updateContact: (contact: Contact) => Promise<void>;
  deleteContact: (id: string) => Promise<void>;
  restoreContact: (id: string) => Promise<void>;
  permanentlyDeleteContact: (id: string) => Promise<void>;
  importContacts: (contacts: Contact[]) => Promise<{ success: number; failed: number }>;
  refreshContacts: () => Promise<void>;
  refreshStorageDetection: () => Promise<void>;
}

const ContactContext = createContext<ContactContextType | undefined>(undefined);

interface ContactProviderProps {
  children: ReactNode;
}

export const ContactProvider: React.FC<ContactProviderProps> = ({ children }) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [deletedContacts, setDeletedContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const { toast } = useToast();

  // Initialize and load contacts on mount
  useEffect(() => {
    initializeAndLoadContacts();
  }, []);

  const initializeAndLoadContacts = async () => {
    try {
      // Don't show loading immediately to prevent blinking
      // Load contacts using hybrid service
      const loadedContacts = await hybridContactService.getAllContacts();
      setContacts(loadedContacts);
      setDeletedContacts([]); // For now, we don't have deleted contacts in the hybrid service
      
      // Get storage info
      const info = hybridContactService.getStorageInfo();
      setStorageInfo(info);
      
      console.log(`✅ ContactContext initialized with ${info.type}, loaded ${loadedContacts.length} contacts`);
      
      // Only show toast if not browser storage (to reduce noise)
      if (info.type !== 'Browser Storage') {
        toast({
          title: `📊 Storage Mode: ${info.type}`,
          description: info.description,
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('❌ Failed to initialize ContactContext:', error);
      toast({
        title: "❌ Initialization Error",
        description: "Failed to load contacts. Please refresh the page.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshContacts = async () => {
    try {
      const loadedContacts = await hybridContactService.getAllContacts();
      setContacts(loadedContacts);
      
      // For now, we don't have deleted contacts in the hybrid service
      // This would need to be implemented if soft delete is required
      setDeletedContacts([]);
      
      console.log(`📋 Loaded ${loadedContacts.length} contacts`);
    } catch (error) {
      console.error('❌ Failed to refresh contacts:', error);
      toast({
        title: "❌ Error Loading Contacts",
        description: "Failed to load contacts from storage.",
        variant: "destructive",
      });
    }
  };

  const addContact = async (contact: Contact) => {
    try {
      const addedContact = await hybridContactService.addContact(contact);
      setContacts(prev => [...prev, addedContact]);
      
      toast({
        title: "✅ Contact Added",
        description: `${contact.name} has been added successfully.`,
      });
      
      console.log('✅ Contact added:', contact.name);
    } catch (error) {
      console.error('❌ Failed to add contact:', error);
      toast({
        title: "❌ Error Adding Contact",
        description: "Failed to add contact. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const updateContact = async (updatedContact: Contact) => {
    try {
      const result = await hybridContactService.updateContact(updatedContact.id, updatedContact);
      if (result) {
        setContacts(prev => 
          prev.map(contact => 
            contact.id === updatedContact.id ? result : contact
          )
        );
        
        toast({
          title: "✅ Contact Updated",
          description: `${updatedContact.name} has been updated successfully.`,
        });
        
        console.log('✅ Contact updated:', updatedContact.name);
      }
    } catch (error) {
      console.error('❌ Failed to update contact:', error);
      toast({
        title: "❌ Error Updating Contact",
        description: "Failed to update contact. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const deleteContact = async (id: string) => {
    try {
      const success = await hybridContactService.deleteContact(id);
      if (success) {
        setContacts(prev => prev.filter(contact => contact.id !== id));
        
        toast({
          title: "✅ Contact Deleted",
          description: "Contact has been deleted successfully.",
        });
        
        console.log('✅ Contact deleted:', id);
      }
    } catch (error) {
      console.error('❌ Failed to delete contact:', error);
      toast({
        title: "❌ Error Deleting Contact",
        description: "Failed to delete contact. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  // For compatibility - these might not be implemented in hybrid service yet
  const restoreContact = async (id: string) => {
    console.log('Restore contact not implemented in hybrid service');
  };

  const permanentlyDeleteContact = async (id: string) => {
    // Same as regular delete in hybrid service
    await deleteContact(id);
  };

  const importContacts = async (contactsToImport: Contact[]): Promise<{ success: number; failed: number }> => {
    let success = 0;
    let failed = 0;
    
    try {
      for (const contact of contactsToImport) {
        try {
          await hybridContactService.addContact(contact);
          success++;
        } catch (error) {
          console.error('Failed to import contact:', contact.name, error);
          failed++;
        }
      }
      
      // Refresh contacts to show imported data
      await refreshContacts();
      
      toast({
        title: "📥 Import Completed",
        description: `${success} contacts imported, ${failed} failed.`,
      });
      
      console.log(`✅ Import completed: ${success} success, ${failed} failed`);
      return { success, failed };
    } catch (error) {
      console.error('❌ Failed to import contacts:', error);
      toast({
        title: "❌ Import Error",
        description: "Failed to import contacts. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const refreshStorageDetection = async () => {
    try {
      setIsLoading(true);
      const detected = await hybridContactService.refreshStorageDetection();
      const info = hybridContactService.getStorageInfo();
      setStorageInfo(info);
      
      toast({
        title: `🔄 Storage Refreshed: ${info.type}`,
        description: info.description,
      });
      
      // Reload contacts with new storage
      await refreshContacts();
    } catch (error) {
      console.error('❌ Failed to refresh storage detection:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const value: ContactContextType = {
    contacts,
    deletedContacts,
    isLoading,
    storageInfo,
    addContact,
    updateContact,
    deleteContact,
    restoreContact,
    permanentlyDeleteContact,
    importContacts,
    refreshContacts,
    refreshStorageDetection,
  };

  return (
    <ContactContext.Provider value={value}>
      {children}
    </ContactContext.Provider>
  );
};

export const useContacts = (): ContactContextType => {
  const context = useContext(ContactContext);
  if (context === undefined) {
    throw new Error('useContacts must be used within a ContactProvider');
  }
  return context;
}; 