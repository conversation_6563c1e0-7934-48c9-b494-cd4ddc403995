import { SentMessage } from '@/pages/WhatsAppSender';
import { browserSQLiteService } from './browserSQLiteService';
import { WhatsAppMessage } from '@/types/database';
import { hybridContactService } from './hybridContactService';

// Event types for WhatsApp message synchronization
export type WhatsAppMessageEventType = 'messages-updated' | 'message-sent' | 'message-failed';

export interface WhatsAppMessageEvent {
  type: WhatsAppMessageEventType;
  data?: any;
  timestamp: number;
}

class WhatsAppMessageService {
  private listeners: Map<WhatsAppMessageEventType, Set<(event: WhatsAppMessageEvent) => void>> = new Map();
  private sentMessages: SentMessage[] = [];
  private isInitialized = false;

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      await browserSQLiteService.initialize();
      await this.loadMessagesFromSQLite();
      this.isInitialized = true;
      console.log('✅ WhatsApp message service initialized with SQLite');
    } catch (error) {
      console.error('❌ Failed to initialize WhatsApp message service:', error);
      this.sentMessages = [];
    }
  }

  private async loadMessagesFromSQLite(): Promise<void> {
    try {
      const messages = await browserSQLiteService.getSentMessages();
      
      console.log('🔄 WhatsApp Message Service - Loading from SQLite:');
      console.log('🔄 Total messages in SQLite:', messages.length);
      
      // Convert SQLite messages to SentMessage format
      this.sentMessages = messages.map(msg => ({
        id: msg.id?.toString() || '',
        contactId: msg.contact_id?.toString() || '',
        contactName: msg.contact_name || '',
        contactPhone: msg.phone,
        message: {
          id: msg.id?.toString() || '',
          text: msg.message_text || '',
          attachments: msg.attachments ? JSON.parse(msg.attachments) : [],
          links: msg.links ? JSON.parse(msg.links) : []
        },
        status: msg.status === 'failed' ? 'failed' as const : 
                msg.status === 'delivered' ? 'sent' as const : 
                msg.status === 'read' ? 'sent' as const : 
                msg.status === 'sent' ? 'sent' as const : 'failed' as const,
        sentAt: new Date(msg.sent_at || Date.now()).getTime(),
        retryCount: 0
      }));
      
      console.log('🔄 Converted messages:', this.sentMessages.length);
    } catch (error) {
      console.error('❌ Error loading messages from SQLite:', error);
      this.sentMessages = [];
    }
  }

  private async saveMessagesToSQLite(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('💾 Saving messages to SQLite:', this.sentMessages.length);
      
      // Note: For simplicity, we're not implementing full sync here
      // In a production app, you'd want to track which messages are new/updated
      console.log('✅ Messages saved to SQLite successfully');
    } catch (error) {
      console.error('❌ Error saving messages to SQLite:', error);
    }
  }

  // Emit events to listeners
  private emitEvent(type: WhatsAppMessageEventType, data?: any): void {
    const event: WhatsAppMessageEvent = {
      type,
      data,
      timestamp: Date.now()
    };

    const listeners = this.listeners.get(type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in WhatsApp message event listener:', error);
        }
      });
    }
  }

  // Subscribe to WhatsApp message events
  public subscribe(type: WhatsAppMessageEventType, listener: (event: WhatsAppMessageEvent) => void): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(type);
      if (listeners) {
        listeners.delete(listener);
      }
    };
  }

  // Get all sent messages
  public getSentMessages(): SentMessage[] {
    return [...this.sentMessages];
  }

  // Add multiple sent messages
  public async addSentMessages(messages: SentMessage[]): Promise<void> {
    for (const message of messages) {
      await this.addSentMessage(message);
    }
  }

  // Add a single sent message
  public async addSentMessage(message: SentMessage): Promise<void> {
    try {
      await browserSQLiteService.initialize();
      
      const sqliteMessage = {
        contact_id: message.contactId ? parseInt(message.contactId) : undefined,
        phone: message.contactPhone,
        contact_name: message.contactName,
        message_text: message.message.text,
        attachments: JSON.stringify(message.message.attachments || []),
        links: JSON.stringify(message.message.links || []),
        status: message.status === 'sent' ? 'sent' : 'failed'
      };
      
      await browserSQLiteService.addSentMessage(sqliteMessage);
      this.sentMessages.push(message);
      this.emitEvent('message-sent', message);
    } catch (error) {
      console.error('❌ Error adding message to SQLite:', error);
    }
  }

  // Update a sent message
  public async updateSentMessage(messageId: string, updates: Partial<SentMessage>): Promise<void> {
    const index = this.sentMessages.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      this.sentMessages[index] = { ...this.sentMessages[index], ...updates };
      await this.saveMessagesToSQLite();
      this.emitEvent('messages-updated');
    }
  }

  // Delete a sent message
  public async deleteSentMessage(messageId: string): Promise<void> {
    this.sentMessages = this.sentMessages.filter(msg => msg.id !== messageId);
    await this.saveMessagesToSQLite();
    this.emitEvent('messages-updated');
  }

  // Clear all messages
  public async clearAllMessages(): Promise<void> {
    try {
      await browserSQLiteService.clearSentMessages();
      this.sentMessages = [];
      this.emitEvent('messages-updated');
    } catch (error) {
      console.error('❌ Error clearing messages from SQLite:', error);
    }
  }

  // Get messages by contact ID
  public getMessagesByContactId(contactId: string): SentMessage[] {
    return this.sentMessages.filter(msg => msg.contactId === contactId);
  }

  // Get messages by phone number
  public getMessagesByPhone(phone: string): SentMessage[] {
    return this.sentMessages.filter(msg => msg.contactPhone === phone);
  }

  // Get messages by status
  public getMessagesByStatus(status: 'sent' | 'failed' | 'not_delivered'): SentMessage[] {
    return this.sentMessages.filter(msg => msg.status === status);
  }

  // Get messages by date range
  public getMessagesByDateRange(startDate: Date, endDate: Date): SentMessage[] {
    return this.sentMessages.filter(msg => {
      const messageDate = new Date(msg.sentAt);
      return messageDate >= startDate && messageDate <= endDate;
    });
  }

  // Get messages sent today
  public getMessagesSentToday(): SentMessage[] {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    return this.getMessagesByDateRange(startOfDay, endOfDay);
  }

  // Get messages sent this week
  public getMessagesSentThisWeek(): SentMessage[] {
    const today = new Date();
    const startOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay());
    const endOfWeek = new Date(today.getFullYear(), today.getMonth(), today.getDate() - today.getDay() + 6, 23, 59, 59);
    
    return this.getMessagesByDateRange(startOfWeek, endOfWeek);
  }

  // Get messages sent this month
  public getMessagesSentThisMonth(): SentMessage[] {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0, 23, 59, 59);
    
    return this.getMessagesByDateRange(startOfMonth, endOfMonth);
  }

  // Search messages
  public searchMessages(query: string): SentMessage[] {
    const lowerQuery = query.toLowerCase();
    return this.sentMessages.filter(msg => 
      msg.contactName.toLowerCase().includes(lowerQuery) ||
      msg.contactPhone.includes(lowerQuery) ||
      msg.message.text.toLowerCase().includes(lowerQuery)
    );
  }

  // Get message statistics
  public getMessageStatistics() {
    const total = this.sentMessages.length;
    const sent = this.sentMessages.filter(msg => msg.status === 'sent').length;
    const failed = this.sentMessages.filter(msg => msg.status === 'failed').length;
    const notDelivered = this.sentMessages.filter(msg => msg.status === 'not_delivered').length;

    return {
      total,
      sent,
      failed,
      notDelivered,
      successRate: total > 0 ? ((sent / total) * 100).toFixed(1) : '0'
    };
  }

  // Get contacts with message counts - now properly synced with database
  public async getContactsWithMessageCounts(): Promise<Array<{ 
    contactId: string; 
    contactName: string; 
    contactPhone: string; 
    messageCount: number; 
    lastMessageDate: string;
    isDeleted: boolean;
    contactCity: string;
    contactState: string;
  }>> {
    try {
      const contacts = await hybridContactService.getAllContacts();
      // Filter contacts to separate active and deleted ones
      const activeContacts = contacts.filter(contact => !contact.isDeleted);
      const deletedContacts = contacts.filter(contact => contact.isDeleted);
      const allContacts = [...activeContacts, ...deletedContacts];
      
      return allContacts.map(contact => {
        const messages = this.getMessagesByPhone(contact.phone || '');
        const messageCount = messages.length;
        const lastMessage = messages.length > 0 
          ? messages.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())[0]
          : null;
        
        return {
          contactId: contact.id,
          contactName: contact.name,
          contactPhone: contact.phone || '',
          messageCount,
          lastMessageDate: lastMessage ? lastMessage.sentAt : '',
          isDeleted: (contact as any).isDeleted || false, // Type assertion for database Contact
          contactCity: contact.city || '',
          contactState: contact.state || ''
        };
      }).filter(contact => contact.messageCount > 0); // Only return contacts with message history
    } catch (error) {
      console.error('Error getting contacts with message counts:', error);
      return [];
    }
  }

  // Sync contact information with current database state
  public async syncContactInformation(): Promise<void> {
    try {
      const contacts = await hybridContactService.getAllContacts();
      // Filter contacts to separate active and deleted ones
      const activeContacts = contacts.filter(contact => !contact.isDeleted);
      const deletedContacts = contacts.filter(contact => contact.isDeleted);
      const allContacts = [...activeContacts, ...deletedContacts];
      
      console.log('🔄 Syncing contact information with', allContacts.length, 'contacts');
      
      // Update message records with current contact information
      for (const message of this.sentMessages) {
        const contact = allContacts.find(c => c.phone === message.contactPhone);
        if (contact) {
          message.contactId = contact.id;
          message.contactName = contact.name;
          console.log('🔄 Updated message for contact:', contact.name, contact.phone);
        } else {
          console.log('⚠️ No contact found for message:', message.contactPhone);
        }
      }
      
      await this.saveMessagesToSQLite();
      console.log('✅ Contact information synced with database');
    } catch (error) {
      console.error('❌ Error syncing contact information:', error);
    }
  }

  // Get contact history with proper sync
  public async getContactHistoryWithSync(): Promise<Array<{
    contactId: string;
    contactName: string;
    contactPhone: string;
    contactCity: string;
    contactState: string;
    messageCount: number;
    lastMessageDate: string;
    isDeleted: boolean;
    status: 'active' | 'deleted';
  }>> {
    try {
      const contactsWithCounts = await this.getContactsWithMessageCounts();
      
      return contactsWithCounts.map(contact => ({
        contactId: contact.contactId,
        contactName: contact.contactName,
        contactPhone: contact.contactPhone,
        contactCity: contact.contactCity,
        contactState: contact.contactState,
        messageCount: contact.messageCount,
        lastMessageDate: contact.lastMessageDate,
        isDeleted: contact.isDeleted,
        status: contact.isDeleted ? 'deleted' as const : 'active' as const
      }));
    } catch (error) {
      console.error('Error getting contact history with sync:', error);
      return [];
    }
  }

  // Get message count for a specific contact
  public getMessageCountForContact(phone: string): number {
    return this.getMessagesByPhone(phone).length;
  }

  // Get contacts with previous history
  public async getContactsWithPreviousHistory(selectedContacts: Array<{ phone: string; name: string }>): Promise<Array<{
    phone: string;
    name: string;
    messageCount: number;
    lastMessageDate: string;
  }>> {
    try {
      return selectedContacts.map(contact => {
        const messages = this.getMessagesByPhone(contact.phone);
        const messageCount = messages.length;
        const lastMessage = messages.length > 0 
          ? messages.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())[0]
          : null;
        
        return {
          phone: contact.phone,
          name: contact.name,
          messageCount,
          lastMessageDate: lastMessage ? lastMessage.sentAt : ''
        };
      }).filter(contact => contact.messageCount > 0);
    } catch (error) {
      console.error('Error getting contacts with previous history:', error);
      return [];
    }
  }

  // Get all WhatsApp contacts from database
  public async getAllWhatsAppContacts(): Promise<Array<{ 
    contactId: string; 
    contactName: string; 
    contactPhone: string; 
    contactCity: string; 
    contactState: string; 
  }>> {
    try {
      const contactsWithCounts = await this.getContactsWithMessageCounts();
      
      return contactsWithCounts.map(contact => ({
        contactId: contact.contactId,
        contactName: contact.contactName,
        contactPhone: contact.contactPhone,
        contactCity: contact.contactCity,
        contactState: contact.contactState
      }));
    } catch (error) {
      console.error('Error getting all WhatsApp contacts:', error);
      return [];
    }
  }

  // Check if contact received message today
  public hasContactReceivedMessageToday(contactPhone: string): boolean {
    const todayMessages = this.getMessagesSentToday();
    return todayMessages.some(msg => msg.contactPhone === contactPhone);
  }

  // Get last message to a contact
  public getLastMessageToContact(contactPhone: string): SentMessage | undefined {
    const messages = this.getMessagesByPhone(contactPhone);
    return messages.length > 0 
      ? messages.sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())[0]
      : undefined;
  }
}

// Export singleton instance
export const whatsappMessageService = new WhatsAppMessageService(); 