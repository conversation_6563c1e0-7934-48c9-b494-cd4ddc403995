import { Contact } from '../types/Contact';
import { getBackendUrl } from '../config/api';

class MySQLContactService {
  private baseUrl: string = '';

  constructor() {
    this.updateBaseUrl();
  }

  private updateBaseUrl() {
    this.baseUrl = getBackendUrl() || '';
  }

  private async fetchWithErrorHandling(url: string, options: RequestInit = {}) {
    try {
      this.updateBaseUrl();
      if (!this.baseUrl) {
        throw new Error('Backend URL not configured. Please configure it in WhatsApp settings.');
      }

      const response = await fetch(`${this.baseUrl}${url}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Get all contacts
  async getAllContacts(): Promise<Contact[]> {
    try {
      const response = await this.fetchWithErrorHandling('/api/contacts');
      return response.data || [];
    } catch (error) {
      console.error('❌ Error getting contacts:', error);
      // Return empty array if database is not available
      return [];
    }
  }

  // Get contact by ID
  async getContactById(id: string): Promise<Contact | null> {
    try {
      const response = await this.fetchWithErrorHandling(`/api/contacts/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('❌ Error getting contact by ID:', error);
      return null;
    }
  }

  // Add new contact
  async addContact(contact: Contact): Promise<Contact> {
    try {
      const response = await this.fetchWithErrorHandling('/api/contacts', {
        method: 'POST',
        body: JSON.stringify(contact),
      });
      
      console.log('✅ Contact added to database:', contact.name);
      return response.data;
    } catch (error) {
      console.error('❌ Error adding contact:', error);
      throw error;
    }
  }

  // Update contact
  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact | null> {
    try {
      const response = await this.fetchWithErrorHandling(`/api/contacts/${id}`, {
        method: 'PUT',
        body: JSON.stringify(updates),
      });
      
      console.log('✅ Contact updated in database:', id);
      return response.data;
    } catch (error) {
      console.error('❌ Error updating contact:', error);
      throw error;
    }
  }

  // Delete contact (soft delete)
  async deleteContact(id: string): Promise<boolean> {
    try {
      await this.fetchWithErrorHandling(`/api/contacts/${id}`, {
        method: 'DELETE',
      });
      
      console.log('✅ Contact deleted from database:', id);
      return true;
    } catch (error) {
      console.error('❌ Error deleting contact:', error);
      throw error;
    }
  }

  // Get deleted contacts
  async getDeletedContacts(): Promise<Contact[]> {
    try {
      const response = await this.fetchWithErrorHandling('/api/contacts/deleted/all');
      return response.data || [];
    } catch (error) {
      console.error('❌ Error getting deleted contacts:', error);
      return [];
    }
  }

  // Restore deleted contact
  async restoreContact(id: string): Promise<Contact | null> {
    try {
      const response = await this.fetchWithErrorHandling(`/api/contacts/${id}/restore`, {
        method: 'POST',
      });
      
      console.log('✅ Contact restored in database:', id);
      return response.data;
    } catch (error) {
      console.error('❌ Error restoring contact:', error);
      throw error;
    }
  }

  // Permanently delete contact
  async permanentlyDeleteContact(id: string): Promise<boolean> {
    try {
      await this.fetchWithErrorHandling(`/api/contacts/${id}/permanent`, {
        method: 'DELETE',
      });
      
      console.log('✅ Contact permanently deleted from database:', id);
      return true;
    } catch (error) {
      console.error('❌ Error permanently deleting contact:', error);
      throw error;
    }
  }

  // Search contacts
  async searchContacts(searchTerm: string): Promise<Contact[]> {
    try {
      if (!searchTerm.trim()) {
        return await this.getAllContacts();
      }

      const response = await this.fetchWithErrorHandling(`/api/contacts/search/${encodeURIComponent(searchTerm)}`);
      return response.data || [];
    } catch (error) {
      console.error('❌ Error searching contacts:', error);
      return [];
    }
  }

  // Get database statistics
  async getDatabaseStats(): Promise<{ totalContacts: number; deletedContacts: number }> {
    try {
      const response = await this.fetchWithErrorHandling('/api/database/stats');
      return response.data || { totalContacts: 0, deletedContacts: 0 };
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      return { totalContacts: 0, deletedContacts: 0 };
    }
  }

  // Test database connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.fetchWithErrorHandling('/api/database/test');
      return response.success || false;
    } catch (error) {
      console.error('❌ Error testing database connection:', error);
      return false;
    }
  }

  // Check if backend is available
  async isBackendAvailable(): Promise<boolean> {
    try {
      this.updateBaseUrl();
      if (!this.baseUrl) {
        return false;
      }

      const response = await fetch(`${this.baseUrl}/api/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Get backend health status
  async getBackendHealth(): Promise<any> {
    try {
      const response = await this.fetchWithErrorHandling('/api/health');
      return response;
    } catch (error) {
      console.error('❌ Error getting backend health:', error);
      return { status: 'error', database: false, whatsapp: { connected: false } };
    }
  }
}

// Export singleton instance
export const mysqlContactService = new MySQLContactService(); 