import { Contact, FileAttachment, Reminder } from '../types/Contact';

// Direct MySQL API Configuration - Your hosted server
const MYSQL_API_BASE_URL = 'https://saamrajyam.com';

class MySQLContactService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = MYSQL_API_BASE_URL;
  }

  private async fetchWithErrorHandling(url: string, options: RequestInit = {}) {
    try {
      const fullUrl = `${this.baseUrl}${url}`;
      console.log('🔗 Making API request to:', fullUrl);
      console.log('📤 Request options:', JSON.stringify({
        method: options.method || 'GET',
        headers: options.headers,
        body: options.body ? 'Present' : 'None'
      }, null, 2));

      const response = await fetch(fullUrl, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      console.log('📥 Response status:', response.status, response.statusText);
      console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error Response:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log('📊 Response data:', JSON.stringify(data, null, 2));

      if (!data.success) {
        throw new Error(data.error || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('❌ API Error:', error);
      throw error;
    }
  }

  // Transform database contact to frontend Contact type
  private transformContact(dbContact: any): Contact {
    return {
      id: dbContact.id?.toString() || '',
      name: dbContact.name || '',
      phone: dbContact.phone || '',
      requirements: dbContact.notes || '',
      categories: dbContact.category_list ? dbContact.category_list.map((cat: any) => cat.name) : [],
      state: dbContact.state || '',
      city: dbContact.city || '',
      date: dbContact.date || dbContact.created_at || new Date().toISOString(),
      isFavorite: Boolean(dbContact.is_favorite),
      isPinned: Boolean(dbContact.is_pinned),
      isShortlisted: Boolean(dbContact.is_shortlisted),
      notes: dbContact.notes || '',
      attachments: [], // Will be populated from files if needed
      buildType: dbContact.build_type || 'Residential',
      kanbanStage: this.mapProjectStatusToKanbanStage(dbContact.project_status),
      reminders: [], // Will be populated from reminders table if needed
      hasActiveReminder: Boolean(dbContact.has_active_reminder)
    };
  }

  // Map database project_status to frontend kanbanStage
  private mapProjectStatusToKanbanStage(projectStatus: string): Contact['kanbanStage'] {
    const statusMap: Record<string, Contact['kanbanStage']> = {
      'Prospect': 'lead',
      'In Discussion': 'contacted',
      'Shortlisted': 'profile-sent',
      'Confirmed': 'advance-received',
      'Completed': 'advance-received',
      'Lost': 'lead'
    };
    return statusMap[projectStatus] || 'lead';
  }

  // Transform frontend Contact to database format
  private transformContactForDB(contact: Partial<Contact>): any {
    return {
      name: contact.name || '',
      phone: contact.phone || '',
      email: null, // Use null instead of empty string
      address: null, // Use null instead of empty string
      city: contact.city || null,
      state: contact.state || null,
      country: 'India',
      notes: contact.requirements || contact.notes || null,
      build_type: contact.buildType || 'Residential',
      project_status: this.mapKanbanStageToProjectStatus(contact.kanbanStage),
      is_favorite: Boolean(contact.isFavorite),
      is_pinned: Boolean(contact.isPinned),
      is_shortlisted: Boolean(contact.isShortlisted),
      has_active_reminder: Boolean(contact.hasActiveReminder),
      date: contact.date || null,
      categories: contact.categories || []
    };
  }

  // Map frontend kanbanStage to database project_status
  private mapKanbanStageToProjectStatus(kanbanStage?: Contact['kanbanStage']): string {
    const stageMap: Record<Contact['kanbanStage'], string> = {
      'lead': 'Prospect',
      'contacted': 'In Discussion',
      'profile-sent': 'Shortlisted',
      'followup': 'In Discussion',
      'meeting-done': 'In Discussion',
      'client-interested': 'Shortlisted',
      'advance-received': 'Confirmed'
    };
    return stageMap[kanbanStage || 'lead'] || 'Prospect';
  }

  // Get all contacts
  async getAllContacts(): Promise<Contact[]> {
    try {
      console.log('📊 Fetching all contacts from MySQL database...');
      const response = await this.fetchWithErrorHandling('/api/contacts.php?limit=1000');
      const contacts = response.data || [];
      const transformedContacts = contacts.map((contact: any) => this.transformContact(contact));
      console.log(`✅ Successfully loaded ${transformedContacts.length} contacts from MySQL database`);
      return transformedContacts;
    } catch (error) {
      console.error('❌ Error getting contacts from MySQL database:', error);
      throw new Error('Failed to fetch contacts from database. Please check your internet connection and try again.');
    }
  }

  // Get contact by ID
  async getContactById(id: string): Promise<Contact | null> {
    try {
      console.log('📊 Fetching contact by ID from MySQL database:', id);
      const response = await this.fetchWithErrorHandling(`/api/contacts.php?id=${id}`);
      const contacts = response.data || [];
      if (contacts.length > 0) {
        const transformedContact = this.transformContact(contacts[0]);
        console.log('✅ Successfully loaded contact from MySQL database:', transformedContact.name);
        return transformedContact;
      }
      return null;
    } catch (error) {
      console.error('❌ Error getting contact by ID from MySQL database:', error);
      return null;
    }
  }

  // Add new contact
  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    try {
      console.log('📝 Adding new contact to MySQL database:', contact.name);
      console.log('📋 Original contact data:', JSON.stringify(contact, null, 2));

      const dbContact = this.transformContactForDB(contact);
      console.log('🔄 Transformed contact data for DB:', JSON.stringify(dbContact, null, 2));

      // Validate required fields before sending
      if (!dbContact.name || !dbContact.phone) {
        throw new Error(`Missing required fields: name=${dbContact.name}, phone=${dbContact.phone}`);
      }

      console.log('🌐 Sending POST request to:', `${this.baseUrl}/api/contacts.php`);
      const response = await this.fetchWithErrorHandling('/api/contacts.php', {
        method: 'POST',
        body: JSON.stringify(dbContact),
      });

      console.log('✅ Contact added to MySQL database:', contact.name);
      console.log('📊 API Response:', JSON.stringify(response, null, 2));

      // Fetch the created contact to get the full data with ID
      const createdId = response.data?.id;
      if (createdId) {
        const createdContact = await this.getContactById(createdId.toString());
        if (createdContact) {
          return createdContact;
        }
      }

      // Fallback: create a contact object with generated ID
      return {
        ...contact,
        id: createdId?.toString() || Date.now().toString(),
      } as Contact;
    } catch (error) {
      console.error('❌ Error adding contact to MySQL database:', error);
      console.error('❌ Error details:', error instanceof Error ? error.message : String(error));
      throw new Error('Failed to add contact to database. Please check your internet connection and try again.');
    }
  }

  // Update contact
  async updateContact(contact: Contact): Promise<Contact | null> {
    try {
      console.log('📝 Updating contact in MySQL database:', contact.name);
      const dbContact = this.transformContactForDB(contact);
      dbContact.id = contact.id;

      const response = await this.fetchWithErrorHandling(`/api/contacts.php`, {
        method: 'PUT',
        body: JSON.stringify(dbContact),
      });

      console.log('✅ Contact updated in MySQL database:', contact.name);

      // Return the updated contact
      return contact;
    } catch (error) {
      console.error('❌ Error updating contact in MySQL database:', error);
      throw error;
    }
  }

  // Delete contact
  async deleteContact(id: string): Promise<boolean> {
    try {
      console.log('🗑️ Deleting contact from MySQL database:', id);
      await this.fetchWithErrorHandling(`/api/contacts.php?id=${id}`, {
        method: 'DELETE',
      });

      console.log('✅ Contact deleted from MySQL database:', id);
      return true;
    } catch (error) {
      console.error('❌ Error deleting contact from MySQL database:', error);
      throw error;
    }
  }

  // Get deleted contacts (not implemented in current API)
  async getDeletedContacts(): Promise<Contact[]> {
    console.log('ℹ️ Deleted contacts feature not implemented in MySQL API');
    return [];
  }

  // Restore deleted contact (not implemented in current API)
  async restoreContact(id: string): Promise<Contact | null> {
    console.log('ℹ️ Restore contact feature not implemented in MySQL API');
    return null;
  }

  // Permanently delete contact (same as delete in current API)
  async permanentlyDeleteContact(id: string): Promise<boolean> {
    return this.deleteContact(id);
  }

  // Search contacts
  async searchContacts(searchTerm: string): Promise<Contact[]> {
    try {
      if (!searchTerm.trim()) {
        return await this.getAllContacts();
      }

      console.log('🔍 Searching contacts in MySQL database:', searchTerm);
      const response = await this.fetchWithErrorHandling(`/api/contacts.php?search=${encodeURIComponent(searchTerm)}`);
      const contacts = response.data || [];
      const transformedContacts = contacts.map((contact: any) => this.transformContact(contact));
      console.log(`✅ Found ${transformedContacts.length} contacts matching search term`);
      return transformedContacts;
    } catch (error) {
      console.error('❌ Error searching contacts in MySQL database:', error);
      return [];
    }
  }

  // Get database statistics
  async getDatabaseStats(): Promise<{ totalContacts: number; deletedContacts: number }> {
    try {
      const contacts = await this.getAllContacts();
      return {
        totalContacts: contacts.length,
        deletedContacts: 0 // Not implemented in current API
      };
    } catch (error) {
      console.error('❌ Error getting database stats:', error);
      return { totalContacts: 0, deletedContacts: 0 };
    }
  }

  // Test database connection
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔗 Testing MySQL database connection...');
      console.log('🌐 Testing URL:', `${this.baseUrl}/api/contacts.php`);
      const response = await fetch(`${this.baseUrl}/api/contacts.php`);
      const isConnected = response.ok;
      console.log('📥 Response status:', response.status);
      console.log('📥 Response ok:', response.ok);
      console.log(isConnected ? '✅ MySQL database connection successful' : '❌ MySQL database connection failed');
      return isConnected;
    } catch (error) {
      console.error('❌ Error testing MySQL database connection:', error);
      console.error('❌ Error details:', error);
      return false;
    }
  }

  // Check if MySQL API is available
  async isBackendAvailable(): Promise<boolean> {
    return this.testConnection();
  }

  // Get MySQL API health status
  async getBackendHealth(): Promise<any> {
    try {
      const isConnected = await this.testConnection();
      return {
        status: isConnected ? 'ok' : 'error',
        database: isConnected,
        storage: 'MySQL Database',
        url: this.baseUrl
      };
    } catch (error) {
      console.error('❌ Error getting MySQL API health:', error);
      return {
        status: 'error',
        database: false,
        storage: 'MySQL Database',
        url: this.baseUrl,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Import contacts (batch add)
  async importContacts(contacts: Omit<Contact, 'id'>[]): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    console.log(`📥 Importing ${contacts.length} contacts to MySQL database...`);

    for (const contact of contacts) {
      try {
        await this.addContact(contact);
        success++;
      } catch (error) {
        console.error('❌ Failed to import contact:', contact.name, error);
        failed++;
      }
    }

    console.log(`✅ Import completed: ${success} successful, ${failed} failed`);
    return { success, failed };
  }

  // Get storage info
  getStorageInfo(): any {
    return {
      type: 'MySQL Database',
      description: 'All contacts are stored in MySQL database on your hosted server',
      persistent: true,
      url: this.baseUrl
    };
  }
}

// Export singleton instance
export const mysqlContactService = new MySQLContactService();

// Export class for direct instantiation if needed
export { MySQLContactService };