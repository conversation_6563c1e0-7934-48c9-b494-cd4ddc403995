// Hybrid Contact Service
// Automatically uses server storage when available, falls back to SQLite browser storage

import { Contact } from '../types/Contact';
import { serverContactService } from './serverContactService';
import { browserSQLiteService } from './browserSQLiteService';

// Define missing types locally
interface Category {
  id: string;
  name: string;
  color: string;
  description: string;
}

interface Settings {
  theme: string;
  language: string;
  notifications: boolean;
  autoBackup: boolean;
}

// Type adapters for browser SQLite
interface SQLiteContact {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags?: string;
  created_at?: string;
  updated_at?: string;
}

function sqliteToFrontendContact(sqliteContact: SQLiteContact): Contact {
  // Parse stored JSON data for complex fields
  let attachments = [];
  let reminders = [];
  let categories = ['General'];
  
  try {
    // Try to parse notes as JSON for attachments
    if (sqliteContact.notes && sqliteContact.notes.startsWith('{')) {
      const parsed = JSON.parse(sqliteContact.notes);
      attachments = parsed.attachments || [];
      reminders = parsed.reminders || [];
    }
  } catch (e) {
    // If not JSON, treat as regular notes
  }
  
  // Parse categories from tags
  if (sqliteContact.tags) {
    categories = sqliteContact.tags.split(',').map(t => t.trim()).filter(t => t);
    if (categories.length === 0) categories = ['General'];
  }

  return {
    id: sqliteContact.id?.toString() || '',
    name: sqliteContact.name,
    phone: sqliteContact.phone,
    requirements: sqliteContact.notes && !sqliteContact.notes.startsWith('{') ? sqliteContact.notes : '',
    categories,
    state: sqliteContact.company || 'Unknown',
    city: sqliteContact.position || 'Unknown',
    date: sqliteContact.created_at || new Date().toISOString(),
    isFavorite: false,
    isPinned: false,
    isShortlisted: false,
    notes: sqliteContact.notes && !sqliteContact.notes.startsWith('{') ? sqliteContact.notes : '',
    attachments,
    buildType: 'Residential',
    kanbanStage: 'lead',
    reminders,
    hasActiveReminder: reminders.length > 0
  };
}

function frontendToSqliteContact(contact: Partial<Contact>): Partial<SQLiteContact> {
  // Store complex data as JSON in notes field if needed
  let notesData = contact.requirements || contact.notes || '';
  
  if (contact.attachments?.length || contact.reminders?.length) {
    notesData = JSON.stringify({
      notes: contact.requirements || contact.notes || '',
      attachments: contact.attachments || [],
      reminders: contact.reminders || []
    });
  }

  return {
    id: contact.id ? parseInt(contact.id) : undefined,
    name: contact.name || '',
    phone: contact.phone || '',
    email: contact.phone || '',
    company: contact.state || 'Unknown',
    position: contact.city || 'Unknown',
    notes: notesData,
    tags: contact.categories?.filter(c => c).join(', ') || 'General',
    created_at: contact.date || undefined,
    updated_at: new Date().toISOString()
  };
}

// SQLite-based contact service for persistent browser storage
class SQLiteContactService {
  
  async getAllContacts(): Promise<Contact[]> {
    try {
      await browserSQLiteService.initialize();
      const sqliteContacts = await browserSQLiteService.getAllContacts();
      return sqliteContacts.map(sqliteToFrontendContact);
    } catch (error) {
      console.error('Error loading contacts from SQLite:', error);
      return [];
    }
  }
  
  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    try {
      await browserSQLiteService.initialize();
      const sqliteContact = frontendToSqliteContact(contact);
      // Ensure required fields are present
      const contactToAdd = {
        name: sqliteContact.name || '',
        phone: sqliteContact.phone || '',
        email: sqliteContact.email,
        company: sqliteContact.company,
        position: sqliteContact.position,
        notes: sqliteContact.notes,
        tags: sqliteContact.tags
      };
      const result = await browserSQLiteService.addContact(contactToAdd);
      return sqliteToFrontendContact(result);
    } catch (error) {
      console.error('Error adding contact to SQLite:', error);
      throw error;
    }
  }
  
  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact | null> {
    try {
      await browserSQLiteService.initialize();
      const sqliteUpdates = frontendToSqliteContact(updates);
      const result = await browserSQLiteService.updateContact(parseInt(id), sqliteUpdates);
      return result ? sqliteToFrontendContact(result) : null;
    } catch (error) {
      console.error('Error updating contact in SQLite:', error);
      return null;
    }
  }
  
  async deleteContact(id: string): Promise<boolean> {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.deleteContact(parseInt(id));
    } catch (error) {
      console.error('Error deleting contact from SQLite:', error);
      return false;
    }
  }
  
  async getContactById(id: string): Promise<Contact | null> {
    try {
      const contacts = await this.getAllContacts();
      return contacts.find(c => c.id === id) || null;
    } catch (error) {
      console.error('Error fetching contact from SQLite:', error);
      return null;
    }
  }
}

const sqliteService = new SQLiteContactService();

class HybridContactService {
  private backendUrl: string = 'http://localhost:3001';

  constructor() {
    // Get backend URL from localStorage or use default
    const storedUrl = localStorage.getItem('whatsapp_backend_url');
    if (storedUrl) {
      this.backendUrl = storedUrl;
    }
  }

  private getBackendUrl(): string {
    return localStorage.getItem('whatsapp_backend_url') || this.backendUrl;
  }

  // Check if backend is available
  private async isBackendAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.getBackendUrl()}/api/contacts`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      return response.ok;
    } catch (error) {
      console.log('Backend not available, using local storage');
      return false;
    }
  }

  // Get all contacts
  async getAllContacts(): Promise<Contact[]> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/contacts`);
        if (response.ok) {
          const contacts = await response.json();
          console.log('✅ Loaded', contacts.length, 'contacts from server');
          return contacts;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const sqliteContacts = await browserSQLiteService.getAllContacts();
      console.log('📱 Loaded', sqliteContacts.length, 'contacts from local storage');
      return sqliteContacts.map(this.mapSQLiteToContact);
    } catch (error) {
      console.error('Error loading contacts:', error);
      return [];
    }
  }

  // Get contact by ID
  async getContactById(id: string): Promise<Contact | null> {
    const useServer = await this.checkServerStorage();
    
    if (useServer) {
      try {
        return await serverContactService.getContact(id);
      } catch (error) {
        console.error('Server storage failed, falling back to SQLite:', error);
        this.useServerStorage = false;
      }
    }

    // SQLite fallback for persistent storage
    try {
      return await sqliteService.getContactById(id);
    } catch (error) {
      console.error('Error fetching contact from SQLite:', error);
      return null;
    }
  }

  // Add contact
  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/contacts`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(contact)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ Contact added to server:', contact.name);
          return result.contact;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const contactToAdd = this.mapContactToSQLite(contact);
      const result = await browserSQLiteService.addContact(contactToAdd);
      console.log('📱 Contact added to local storage:', contact.name);
      return this.mapSQLiteToContact(result);
    } catch (error) {
      console.error('Error adding contact:', error);
      throw error;
    }
  }

  // Update contact
  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/contacts/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updates)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ Contact updated on server:', updates.name);
          return result.contact;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const sqliteUpdates = this.mapContactToSQLite(updates);
      const result = await browserSQLiteService.updateContact(parseInt(id), sqliteUpdates);
      console.log('📱 Contact updated in local storage:', updates.name);
      return this.mapSQLiteToContact(result);
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  }

  // Delete contact
  async deleteContact(id: string): Promise<boolean> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/contacts/${id}`, {
          method: 'DELETE'
        });
        
        if (response.ok) {
          console.log('✅ Contact deleted from server');
          return true;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const result = await browserSQLiteService.deleteContact(parseInt(id));
      console.log('📱 Contact deleted from local storage');
      return result;
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  // Search contacts
  async searchContacts(query: string): Promise<Contact[]> {
    const contacts = await this.getAllContacts();
    
    if (!query.trim()) {
      return contacts;
    }

    const searchTerm = query.toLowerCase();
    return contacts.filter(contact => 
      contact.name.toLowerCase().includes(searchTerm) ||
      contact.phone.includes(searchTerm) ||
      contact.requirements?.toLowerCase().includes(searchTerm) ||
      contact.state?.toLowerCase().includes(searchTerm) ||
      contact.city?.toLowerCase().includes(searchTerm) ||
      contact.categories?.some(cat => cat.toLowerCase().includes(searchTerm)) ||
      contact.notes?.toLowerCase().includes(searchTerm)
    );
  }

  // File upload methods (using server storage)
  async uploadFiles(files: FileList): Promise<any[]> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const uploadedFiles = [];
        
        for (const file of Array.from(files)) {
          try {
            // Convert file to base64
            const base64Data = await this.fileToBase64(file);
            
            const response = await fetch(`${this.getBackendUrl()}/api/files.php`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                filename: file.name,
                mimeType: file.type,
                size: file.size,
                data: base64Data,
                folder: this.getFileFolder(file)
              })
            });

            if (response.ok) {
              const result = await response.json();
              uploadedFiles.push({
                id: result.id,
                name: file.name,
                type: file.type,
                size: file.size,
                url: result.url,
                folder: this.getFileFolder(file),
                uploadDate: new Date().toISOString()
              });
              console.log('✅ File uploaded to server:', file.name);
            }
          } catch (error) {
            console.error('Failed to upload file:', file.name, error);
          }
        }
        
        return uploadedFiles;
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const folders = await browserSQLiteService.getMediaFolders();
      let defaultFolder = folders.find(f => f.name === 'Default');
      
      if (!defaultFolder) {
        defaultFolder = await browserSQLiteService.addMediaFolder({
          name: 'Default',
          description: 'Default media folder'
        });
      }

      const uploadedFiles = [];
      for (const file of Array.from(files)) {
        const mediaFile = await browserSQLiteService.addMediaFile(file, defaultFolder.id!);
        uploadedFiles.push({
          id: mediaFile.id!.toString(),
          name: mediaFile.original_name,
          type: mediaFile.mime_type,
          size: mediaFile.file_size,
          url: `data:${mediaFile.mime_type};base64,${mediaFile.file_data}`,
          folder: 'Default',
          uploadDate: mediaFile.created_at || new Date().toISOString()
        });
      }
      
      console.log('📱 Files uploaded to local storage');
      return uploadedFiles;
    } catch (error) {
      console.error('Error uploading files:', error);
      return [];
    }
  }

  // Get uploaded files
  async getUploadedFiles(): Promise<any[]> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/uploaded-files`);
        if (response.ok) {
          const files = await response.json();
          console.log('✅ Loaded', files.length, 'files from server');
          return files;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const mediaFiles = await browserSQLiteService.getMediaFiles();
      console.log('📱 Loaded', mediaFiles.length, 'files from local storage');
      return mediaFiles.map(file => ({
        id: file.id!.toString(),
        name: file.original_name,
        type: file.mime_type,
        size: file.file_size,
        url: `data:${file.mime_type};base64,${file.file_data}`,
        folder: 'Default',
        uploadDate: file.created_at || new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error loading uploaded files:', error);
      return [];
    }
  }

  // Helper methods
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  private getFileFolder(file: File): string {
    const type = file.type.toLowerCase();
    if (type.startsWith('image/')) return 'Images';
    if (type.startsWith('video/')) return 'Videos';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.includes('pdf')) return 'Documents/PDF';
    if (type.includes('word') || type.includes('doc')) return 'Documents/Word';
    if (type.includes('excel') || type.includes('sheet')) return 'Documents/Excel';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'Documents/PowerPoint';
    if (type.includes('text')) return 'Documents/Text';
    return 'Other';
  }

  private mapSQLiteToContact(sqliteContact: any): Contact {
    return {
      id: sqliteContact.id?.toString() || '',
      name: sqliteContact.name || '',
      phone: sqliteContact.phone || '',
      requirements: sqliteContact.requirements || '',
      categories: sqliteContact.categories ? JSON.parse(sqliteContact.categories) : [],
      state: sqliteContact.state || '',
      city: sqliteContact.city || '',
      date: sqliteContact.date || new Date().toISOString(),
      isFavorite: sqliteContact.is_favorite || false,
      isPinned: sqliteContact.is_pinned || false,
      isShortlisted: sqliteContact.is_shortlisted || false,
      notes: sqliteContact.notes || '',
      attachments: sqliteContact.attachments ? JSON.parse(sqliteContact.attachments) : [],
      buildType: sqliteContact.build_type || '',
      kanbanStage: sqliteContact.kanban_stage || 'lead',
      reminders: sqliteContact.reminders ? JSON.parse(sqliteContact.reminders) : [],
      hasActiveReminder: sqliteContact.has_active_reminder || false
    };
  }

  private mapContactToSQLite(contact: Partial<Contact>): any {
    return {
      name: contact.name || '',
      phone: contact.phone || '',
      requirements: contact.requirements || '',
      categories: contact.categories ? JSON.stringify(contact.categories) : '[]',
      state: contact.state || '',
      city: contact.city || '',
      date: contact.date || new Date().toISOString(),
      is_favorite: contact.isFavorite || false,
      is_pinned: contact.isPinned || false,
      is_shortlisted: contact.isShortlisted || false,
      notes: contact.notes || '',
      attachments: contact.attachments ? JSON.stringify(contact.attachments) : '[]',
      build_type: contact.buildType || '',
      kanban_stage: contact.kanbanStage || 'lead',
      reminders: contact.reminders ? JSON.stringify(contact.reminders) : '[]',
      has_active_reminder: contact.hasActiveReminder || false
    };
  }

  private mapSQLiteToCategory(sqliteCategory: any): Category {
    return {
      id: sqliteCategory.id?.toString() || '',
      name: sqliteCategory.name || '',
      color: sqliteCategory.color || '#3b82f6',
      description: sqliteCategory.description || ''
    };
  }

  // Get categories
  async getCategories(): Promise<Category[]> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/categories`);
        if (response.ok) {
          const categories = await response.json();
          console.log('✅ Loaded', categories.length, 'categories from server');
          return categories;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const categories = await browserSQLiteService.getContactGroups();
      console.log('📱 Loaded', categories.length, 'categories from local storage');
      return categories.map(this.mapSQLiteToCategory);
    } catch (error) {
      console.error('Error loading categories:', error);
      return [];
    }
  }

  // Add category
  async addCategory(category: Omit<Category, 'id'>): Promise<Category> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/categories`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(category)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ Category added to server:', category.name);
          return result.category;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const result = await browserSQLiteService.addContactGroup(category);
      console.log('📱 Category added to local storage:', category.name);
      return this.mapSQLiteToCategory(result);
    } catch (error) {
      console.error('Error adding category:', error);
      throw error;
    }
  }

  // Get settings
  async getSettings(): Promise<Settings> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/settings`);
        if (response.ok) {
          const settings = await response.json();
          console.log('✅ Loaded settings from server');
          return settings;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    return {
      theme: 'light',
      language: 'en',
      notifications: true,
      autoBackup: true
    };
  }

  // Update settings
  async updateSettings(settings: Partial<Settings>): Promise<Settings> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/settings`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(settings)
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('✅ Settings updated on server');
          return result.settings;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    console.log('📱 Settings updated in local storage');
    return { ...await this.getSettings(), ...settings };
  }

  // Export data
  async exportData(): Promise<any> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/export-data`);
        if (response.ok) {
          const result = await response.json();
          console.log('✅ Data exported from server');
          return result.data;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const data = await browserSQLiteService.exportDatabase();
      console.log('📱 Data exported from local storage');
      return data;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  }

  // Import data
  async importData(data: any): Promise<boolean> {
    try {
      const backendAvailable = await this.isBackendAvailable();
      
      if (backendAvailable) {
        const response = await fetch(`${this.getBackendUrl()}/api/import-data`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        });
        
        if (response.ok) {
          console.log('✅ Data imported to server');
          return true;
        }
      }
    } catch (error) {
      console.log('Server storage failed, using local storage');
    }

    // Fallback to local storage
    try {
      await browserSQLiteService.initialize();
      const result = await browserSQLiteService.importDatabase(data);
      console.log('📱 Data imported to local storage');
      return result;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // WhatsApp message methods (using local storage for now)
  async addSentMessage(message: any): Promise<any> {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.addSentMessage(message);
    } catch (error) {
      console.error('Error adding sent message:', error);
      throw error;
    }
  }

  async getSentMessages(): Promise<any[]> {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.getSentMessages();
    } catch (error) {
      console.error('Error getting sent messages:', error);
      return [];
    }
  }

  async clearSentMessages(): Promise<void> {
    try {
      await browserSQLiteService.initialize();
      await browserSQLiteService.clearSentMessages();
    } catch (error) {
      console.error('Error clearing sent messages:', error);
    }
  }

  // Storage info
  getStorageInfo(): any {
    return browserSQLiteService.getStorageInfo();
  }

  // Check if using server storage
  isUsingServerStorage(): boolean {
    return this.useServerStorage === true;
  }

  // Refresh storage detection
  async refreshStorageDetection(): Promise<boolean> {
    this.useServerStorage = null;
    return await this.checkServerStorage();
  }
}

export const hybridContactService = new HybridContactService();
export default hybridContactService; 