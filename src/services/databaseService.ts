import { Contact, WhatsAppMessage, Category, Settings } from '@/types/database';

// Database interface
interface DatabaseService {
  initialize(): Promise<void>;
  getContacts(): Promise<Contact[]>;
  addContact(contact: Contact): Promise<void>;
  updateContact(contact: Contact): Promise<void>;
  deleteContact(id: string): Promise<void>;
  restoreContact(id: string): Promise<void>;
  getWhatsAppMessages(): Promise<WhatsAppMessage[]>;
  addWhatsAppMessage(message: WhatsAppMessage): Promise<void>;
  getCategories(): Promise<Category[]>;
  addCategory(category: Category): Promise<void>;
  getSettings(): Promise<Settings>;
  updateSetting(key: string, value: string): Promise<void>;
  exportData(): Promise<string>;
  importData(data: string): Promise<void>;
  backup(): Promise<void>;
  restore(backupData: string): Promise<void>;
}

// Frontend Database Service using IndexedDB
// This provides persistent storage that works across devices and users

class DatabaseService {
  private dbName = 'ContactSphereDB';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  // Initialize the database
  async initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('Database failed to open');
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('Database opened successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create contacts object store
        if (!db.objectStoreNames.contains('contacts')) {
          const contactStore = db.createObjectStore('contacts', { keyPath: 'id' });
          contactStore.createIndex('name', 'name', { unique: false });
          contactStore.createIndex('email', 'email', { unique: false });
          contactStore.createIndex('phone', 'phone', { unique: false });
          contactStore.createIndex('city', 'city', { unique: false });
          contactStore.createIndex('state', 'state', { unique: false });
          contactStore.createIndex('category', 'category', { unique: false });
        }

        // Create deleted contacts object store
        if (!db.objectStoreNames.contains('deletedContacts')) {
          const deletedStore = db.createObjectStore('deletedContacts', { keyPath: 'id' });
          deletedStore.createIndex('deletedAt', 'deletedAt', { unique: false });
        }

        // Create app settings object store
        if (!db.objectStoreNames.contains('appSettings')) {
          db.createObjectStore('appSettings', { keyPath: 'key' });
        }

        console.log('Database setup complete');
      };
    });
  }

  // Ensure database is initialized
  private async ensureDatabase(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.initializeDatabase();
    }
    return this.db!;
  }

  // Get all contacts
  async getAllContacts(): Promise<Contact[]> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readonly');
      const store = transaction.objectStore('contacts');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get contacts'));
      };
    });
  }

  // Add a new contact
  async addContact(contact: Contact): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.add(contact);

      request.onsuccess = () => {
        console.log('Contact added successfully:', contact.name);
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to add contact'));
      };
    });
  }

  // Update an existing contact
  async updateContact(contact: Contact): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.put(contact);

      request.onsuccess = () => {
        console.log('Contact updated successfully:', contact.name);
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to update contact'));
      };
    });
  }

  // Delete a contact (move to deleted contacts)
  async deleteContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    
    // First get the contact
    const contact = await this.getContactById(contactId);
    if (!contact) {
      throw new Error('Contact not found');
    }

    // Add deletedAt timestamp
    const deletedContact = {
      ...contact,
      deletedAt: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts'], 'readwrite');
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');

      // Remove from contacts
      const deleteRequest = contactStore.delete(contactId);
      
      deleteRequest.onsuccess = () => {
        // Add to deleted contacts
        const addRequest = deletedStore.add(deletedContact);
        
        addRequest.onsuccess = () => {
          console.log('Contact moved to deleted:', contact.name);
          resolve();
        };
        
        addRequest.onerror = () => {
          reject(new Error('Failed to move contact to deleted'));
        };
      };

      deleteRequest.onerror = () => {
        reject(new Error('Failed to delete contact'));
      };
    });
  }

  // Get a contact by ID
  async getContactById(contactId: string): Promise<Contact | null> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readonly');
      const store = transaction.objectStore('contacts');
      const request = store.get(contactId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get contact'));
      };
    });
  }

  // Get all deleted contacts
  async getDeletedContacts(): Promise<Contact[]> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['deletedContacts'], 'readonly');
      const store = transaction.objectStore('deletedContacts');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get deleted contacts'));
      };
    });
  }

  // Restore a deleted contact
  async restoreContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts'], 'readwrite');
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');

      // Get from deleted contacts
      const getRequest = deletedStore.get(contactId);
      
      getRequest.onsuccess = () => {
        const deletedContact = getRequest.result;
        if (!deletedContact) {
          reject(new Error('Deleted contact not found'));
          return;
        }

        // Remove deletedAt timestamp
        const { deletedAt, ...restoredContact } = deletedContact;

        // Add back to contacts
        const addRequest = contactStore.add(restoredContact);
        
        addRequest.onsuccess = () => {
          // Remove from deleted contacts
          const deleteRequest = deletedStore.delete(contactId);
          
          deleteRequest.onsuccess = () => {
            console.log('Contact restored:', restoredContact.name);
            resolve();
          };
          
          deleteRequest.onerror = () => {
            reject(new Error('Failed to remove from deleted contacts'));
          };
        };
        
        addRequest.onerror = () => {
          reject(new Error('Failed to restore contact'));
        };
      };

      getRequest.onerror = () => {
        reject(new Error('Failed to get deleted contact'));
      };
    });
  }

  // Permanently delete a contact
  async permanentlyDeleteContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['deletedContacts'], 'readwrite');
      const store = transaction.objectStore('deletedContacts');
      const request = store.delete(contactId);

      request.onsuccess = () => {
        console.log('Contact permanently deleted');
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to permanently delete contact'));
      };
    });
  }

  // Bulk import contacts
  async importContacts(contacts: Contact[]): Promise<{ success: number; failed: number }> {
    const db = await this.ensureDatabase();
    let success = 0;
    let failed = 0;

    for (const contact of contacts) {
      try {
        await this.addContact(contact);
        success++;
      } catch (error) {
        console.error('Failed to import contact:', contact.name, error);
        failed++;
      }
    }

    console.log(`Import complete: ${success} success, ${failed} failed`);
    return { success, failed };
  }

  // Clear all data (for testing/reset)
  async clearAllData(): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts', 'appSettings'], 'readwrite');
      
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');
      const settingsStore = transaction.objectStore('appSettings');

      const clearContacts = contactStore.clear();
      const clearDeleted = deletedStore.clear();
      const clearSettings = settingsStore.clear();

      let completed = 0;
      const total = 3;

      const checkComplete = () => {
        completed++;
        if (completed === total) {
          console.log('All data cleared');
          resolve();
        }
      };

      clearContacts.onsuccess = checkComplete;
      clearDeleted.onsuccess = checkComplete;
      clearSettings.onsuccess = checkComplete;

      clearContacts.onerror = () => reject(new Error('Failed to clear contacts'));
      clearDeleted.onerror = () => reject(new Error('Failed to clear deleted contacts'));
      clearSettings.onerror = () => reject(new Error('Failed to clear settings'));
    });
  }

  // Get database statistics
  async getDatabaseStats(): Promise<{ totalContacts: number; deletedContacts: number }> {
    const [contacts, deleted] = await Promise.all([
      this.getAllContacts(),
      this.getDeletedContacts()
    ]);

    return {
      totalContacts: contacts.length,
      deletedContacts: deleted.length
    };
  }

  // Save app settings
  async saveSetting(key: string, value: any): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appSettings'], 'readwrite');
      const store = transaction.objectStore('appSettings');
      const request = store.put({ key, value });

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        reject(new Error('Failed to save setting'));
      };
    });
  }

  // Get app setting
  async getSetting(key: string): Promise<any> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appSettings'], 'readonly');
      const store = transaction.objectStore('appSettings');
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.value : null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get setting'));
      };
    });
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();

// Development: SQLite implementation
class SQLiteDatabaseService implements DatabaseService {
  private db: any = null;

  async initialize(): Promise<void> {
    // In development, we'll use a simple in-memory database
    // For production, this would be replaced with SQL.js
    console.log('Initializing SQLite database...');
    
    // For now, we'll use localStorage as a fallback
    // In a real implementation, you'd use a proper SQLite library
    this.db = {
      contacts: JSON.parse(localStorage.getItem('contacts') || '[]'),
      whatsappMessages: JSON.parse(localStorage.getItem('whatsappMessages') || '[]'),
      categories: JSON.parse(localStorage.getItem('categories') || '[]'),
      settings: JSON.parse(localStorage.getItem('settings') || '{}')
    };
  }

  async getContacts(): Promise<Contact[]> {
    return this.db.contacts.filter((contact: Contact) => !contact.isDeleted);
  }

  async addContact(contact: Contact): Promise<void> {
    contact.id = `contact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    contact.createdAt = new Date().toISOString();
    contact.updatedAt = new Date().toISOString();
    this.db.contacts.push(contact);
    this.saveToStorage();
  }

  async updateContact(contact: Contact): Promise<void> {
    const index = this.db.contacts.findIndex((c: Contact) => c.id === contact.id);
    if (index !== -1) {
      contact.updatedAt = new Date().toISOString();
      this.db.contacts[index] = contact;
      this.saveToStorage();
    }
  }

  async deleteContact(id: string): Promise<void> {
    const contact = this.db.contacts.find((c: Contact) => c.id === id);
    if (contact) {
      contact.isDeleted = true;
      contact.deletedAt = new Date().toISOString();
      this.saveToStorage();
    }
  }

  async restoreContact(id: string): Promise<void> {
    const contact = this.db.contacts.find((c: Contact) => c.id === id);
    if (contact) {
      contact.isDeleted = false;
      contact.deletedAt = null;
      this.saveToStorage();
    }
  }

  async getWhatsAppMessages(): Promise<WhatsAppMessage[]> {
    return this.db.whatsappMessages;
  }

  async addWhatsAppMessage(message: WhatsAppMessage): Promise<void> {
    message.id = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    message.timestamp = new Date().toISOString();
    this.db.whatsappMessages.push(message);
    this.saveToStorage();
  }

  async getCategories(): Promise<Category[]> {
    return this.db.categories;
  }

  async addCategory(category: Category): Promise<void> {
    category.id = `cat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    category.createdAt = new Date().toISOString();
    this.db.categories.push(category);
    this.saveToStorage();
  }

  async getSettings(): Promise<Settings> {
    return this.db.settings;
  }

  async updateSetting(key: string, value: string): Promise<void> {
    this.db.settings[key] = value;
    this.saveToStorage();
  }

  async exportData(): Promise<string> {
    return JSON.stringify(this.db, null, 2);
  }

  async importData(data: string): Promise<void> {
    try {
      const importedData = JSON.parse(data);
      this.db = importedData;
      this.saveToStorage();
    } catch (error) {
      throw new Error('Invalid data format');
    }
  }

  async backup(): Promise<void> {
    const backupData = await this.exportData();
    localStorage.setItem('backup_' + new Date().toISOString(), backupData);
  }

  async restore(backupData: string): Promise<void> {
    await this.importData(backupData);
  }

  private saveToStorage(): void {
    localStorage.setItem('contacts', JSON.stringify(this.db.contacts));
    localStorage.setItem('whatsappMessages', JSON.stringify(this.db.whatsappMessages));
    localStorage.setItem('categories', JSON.stringify(this.db.categories));
    localStorage.setItem('settings', JSON.stringify(this.db.settings));
  }
}

// Production: SQL.js implementation
class SQLJsDatabaseService implements DatabaseService {
  private db: any = null;
  private sql: any = null;

  async initialize(): Promise<void> {
    // Load SQL.js
    if (typeof window !== 'undefined') {
      // @ts-ignore
      this.sql = await initSqlJs({
        locateFile: file => `https://sql.js.org/dist/${file}`
      });
      
      // Try to load existing database from localStorage
      const existingDb = localStorage.getItem('sqlite_database');
      if (existingDb) {
        const uint8Array = new Uint8Array(existingDb.split(',').map(Number));
        this.db = new this.sql.Database(uint8Array);
      } else {
        // Create new database
        this.db = new this.sql.Database();
        await this.createTables();
      }
    }
  }

  private async createTables(): Promise<void> {
    // Read and execute schema
    const response = await fetch('/database/schema.sql');
    const schema = await response.text();
    this.db.exec(schema);
  }

  async getContacts(): Promise<Contact[]> {
    const result = this.db.exec(`
      SELECT * FROM contacts 
      WHERE is_deleted = 0 
      ORDER BY name COLLATE NOCASE
    `);
    return result[0]?.values.map((row: any) => this.mapContactFromRow(row)) || [];
  }

  async addContact(contact: Contact): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT INTO contacts (
        id, name, phone, email, category, state, city, build_type, 
        address, notes, is_favorite, is_pinned, is_shortlisted, 
        has_reminder, reminder_date, reminder_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run([
      contact.id,
      contact.name,
      contact.phone,
      contact.email,
      contact.category,
      contact.state,
      contact.city,
      contact.buildType,
      contact.address,
      contact.notes,
      contact.isFavorite ? 1 : 0,
      contact.isPinned ? 1 : 0,
      contact.isShortlisted ? 1 : 0,
      contact.hasReminder ? 1 : 0,
      contact.reminderDate,
      contact.reminderNotes
    ]);
    
    stmt.free();
    this.saveDatabase();
  }

  async updateContact(contact: Contact): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE contacts SET 
        name = ?, phone = ?, email = ?, category = ?, state = ?, 
        city = ?, build_type = ?, address = ?, notes = ?, 
        is_favorite = ?, is_pinned = ?, is_shortlisted = ?, 
        has_reminder = ?, reminder_date = ?, reminder_notes = ?,
        updated_at = datetime('now')
      WHERE id = ?
    `);
    
    stmt.run([
      contact.name,
      contact.phone,
      contact.email,
      contact.category,
      contact.state,
      contact.city,
      contact.buildType,
      contact.address,
      contact.notes,
      contact.isFavorite ? 1 : 0,
      contact.isPinned ? 1 : 0,
      contact.isShortlisted ? 1 : 0,
      contact.hasReminder ? 1 : 0,
      contact.reminderDate,
      contact.reminderNotes,
      contact.id
    ]);
    
    stmt.free();
    this.saveDatabase();
  }

  async deleteContact(id: string): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE contacts SET 
        is_deleted = 1, 
        deleted_at = datetime('now') 
      WHERE id = ?
    `);
    stmt.run([id]);
    stmt.free();
    this.saveDatabase();
  }

  async restoreContact(id: string): Promise<void> {
    const stmt = this.db.prepare(`
      UPDATE contacts SET 
        is_deleted = 0, 
        deleted_at = NULL 
      WHERE id = ?
    `);
    stmt.run([id]);
    stmt.free();
    this.saveDatabase();
  }

  async getWhatsAppMessages(): Promise<WhatsAppMessage[]> {
    const result = this.db.exec(`
      SELECT * FROM whatsapp_messages 
      ORDER BY timestamp DESC
    `);
    return result[0]?.values.map((row: any) => this.mapWhatsAppMessageFromRow(row)) || [];
  }

  async addWhatsAppMessage(message: WhatsAppMessage): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT INTO whatsapp_messages (
        id, contact_id, phone_number, message, message_type, 
        attachments, status, message_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run([
      message.id,
      message.contactId,
      message.phoneNumber,
      message.message,
      message.messageType,
      JSON.stringify(message.attachments),
      message.status,
      message.messageId
    ]);
    
    stmt.free();
    this.saveDatabase();
  }

  async getCategories(): Promise<Category[]> {
    const result = this.db.exec(`
      SELECT * FROM categories 
      ORDER BY name COLLATE NOCASE
    `);
    return result[0]?.values.map((row: any) => this.mapCategoryFromRow(row)) || [];
  }

  async addCategory(category: Category): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT INTO categories (id, name, color) VALUES (?, ?, ?)
    `);
    stmt.run([category.id, category.name, category.color]);
    stmt.free();
    this.saveDatabase();
  }

  async getSettings(): Promise<Settings> {
    const result = this.db.exec(`
      SELECT key, value FROM settings
    `);
    const settings: Settings = {};
    result[0]?.values.forEach((row: any) => {
      settings[row[0]] = row[1];
    });
    return settings;
  }

  async updateSetting(key: string, value: string): Promise<void> {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updated_at) 
      VALUES (?, ?, datetime('now'))
    `);
    stmt.run([key, value]);
    stmt.free();
    this.saveDatabase();
  }

  async exportData(): Promise<string> {
    const data = this.db.export();
    return JSON.stringify(Array.from(data));
  }

  async importData(data: string): Promise<void> {
    try {
      const uint8Array = new Uint8Array(JSON.parse(data));
      this.db = new this.sql.Database(uint8Array);
      this.saveDatabase();
    } catch (error) {
      throw new Error('Invalid data format');
    }
  }

  async backup(): Promise<void> {
    const backupData = await this.exportData();
    localStorage.setItem('backup_' + new Date().toISOString(), backupData);
  }

  async restore(backupData: string): Promise<void> {
    await this.importData(backupData);
  }

  private saveDatabase(): void {
    if (this.db) {
      const data = this.db.export();
      localStorage.setItem('sqlite_database', Array.from(data).toString());
    }
  }

  private mapContactFromRow(row: any): Contact {
    return {
      id: row[0],
      name: row[1],
      phone: row[2],
      email: row[3],
      category: row[4],
      state: row[5],
      city: row[6],
      buildType: row[7],
      address: row[8],
      notes: row[9],
      isFavorite: Boolean(row[10]),
      isPinned: Boolean(row[11]),
      isShortlisted: Boolean(row[12]),
      hasReminder: Boolean(row[13]),
      reminderDate: row[14],
      reminderNotes: row[15],
      createdAt: row[16],
      updatedAt: row[17],
      isDeleted: Boolean(row[18]),
      deletedAt: row[19]
    };
  }

  private mapWhatsAppMessageFromRow(row: any): WhatsAppMessage {
    return {
      id: row[0],
      contactId: row[1],
      phoneNumber: row[2],
      message: row[3],
      messageType: row[4],
      attachments: row[5] ? JSON.parse(row[5]) : [],
      status: row[6],
      timestamp: row[7],
      messageId: row[8]
    };
  }

  private mapCategoryFromRow(row: any): Category {
    return {
      id: row[0],
      name: row[1],
      color: row[2],
      createdAt: row[3]
    };
  }
}

// Factory function to create the appropriate database service
export function createDatabaseService(): DatabaseService {
  // Check if we're in development or production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    return new SQLiteDatabaseService();
  } else {
    return new SQLJsDatabaseService();
  }
}

// Export singleton instance
export const databaseService = createDatabaseService(); 