import { Contact, WhatsAppMessage, Category, Settings, FileAttachment } from '@/types/database';

// Enhanced database service with IndexedDB and File System Access
class EnhancedDatabaseService {
  private db: IDBDatabase | null = null;
  private fileHandle: FileSystemDirectoryHandle | null = null;
  private readonly DB_NAME = 'ContactSphereDB';
  private readonly DB_VERSION = 1;

  async initialize(): Promise<void> {
    console.log('🔄 Initializing Enhanced Database Service...');
    
    try {
      // Initialize IndexedDB
      await this.initIndexedDB();
      console.log('✅ IndexedDB initialized successfully');
      
      // Initialize File System Access (if supported)
      await this.initFileSystem();
      
      // Migrate existing localStorage data
      await this.migrateFromLocalStorage();
      
      console.log('✅ Enhanced Database Service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced Database Service:', error);
      
      // Check if it's a browser security or permission issue
      if (error instanceof Error) {
        if (error.name === 'SecurityError' || error.name === 'NotAllowedError') {
          console.log('🔄 Browser security restrictions detected, falling back to localStorage');
        } else if (error.name === 'QuotaExceededError') {
          console.log('🔄 Storage quota exceeded, falling back to localStorage');
        } else {
          console.log('🔄 Unknown database error, falling back to localStorage');
        }
      }
      
      throw error;
    }
  }

  private async initIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('🔄 Opening IndexedDB...');
      
      // Check if IndexedDB is supported
      if (!window.indexedDB) {
        const error = new Error('IndexedDB is not supported in this browser');
        console.error('❌ IndexedDB not supported:', error);
        reject(error);
        return;
      }
      
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = () => {
        console.error('❌ IndexedDB open error:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB opened successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        console.log('🔄 IndexedDB upgrade needed, creating object stores...');
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores
        if (!db.objectStoreNames.contains('contacts')) {
          const contactStore = db.createObjectStore('contacts', { keyPath: 'id' });
          contactStore.createIndex('name', 'name', { unique: false });
          contactStore.createIndex('phone', 'phone', { unique: false });
          contactStore.createIndex('category', 'category', { unique: false });
          contactStore.createIndex('isDeleted', 'isDeleted', { unique: false });
          console.log('✅ Contacts object store created');
        }

        if (!db.objectStoreNames.contains('whatsappMessages')) {
          const messageStore = db.createObjectStore('whatsappMessages', { keyPath: 'id' });
          messageStore.createIndex('contactId', 'contactId', { unique: false });
          messageStore.createIndex('phoneNumber', 'phoneNumber', { unique: false });
          messageStore.createIndex('timestamp', 'timestamp', { unique: false });
          console.log('✅ WhatsApp messages object store created');
        }

        if (!db.objectStoreNames.contains('categories')) {
          const categoryStore = db.createObjectStore('categories', { keyPath: 'id' });
          categoryStore.createIndex('name', 'name', { unique: true });
          console.log('✅ Categories object store created');
        }

        if (!db.objectStoreNames.contains('settings')) {
          db.createObjectStore('settings', { keyPath: 'key' });
          console.log('✅ Settings object store created');
        }

        if (!db.objectStoreNames.contains('files')) {
          const fileStore = db.createObjectStore('files', { keyPath: 'id' });
          fileStore.createIndex('contactId', 'contactId', { unique: false });
          fileStore.createIndex('messageId', 'messageId', { unique: false });
          fileStore.createIndex('type', 'type', { unique: false });
          console.log('✅ Files object store created');
        }

        if (!db.objectStoreNames.contains('backups')) {
          db.createObjectStore('backups', { keyPath: 'timestamp' });
          console.log('✅ Backups object store created');
        }
      };
    });
  }

  private async initFileSystem(): Promise<void> {
    try {
      // Request permission to access file system
      if ('showDirectoryPicker' in window) {
        this.fileHandle = await (window as any).showDirectoryPicker({
          mode: 'readwrite',
          startIn: 'documents'
        });
        console.log('File System Access initialized');
      } else {
        console.log('File System Access API not supported, using IndexedDB for files');
      }
    } catch (error) {
      console.log('File System Access not available:', error);
    }
  }

  private async migrateFromLocalStorage(): Promise<void> {
    try {
      // Check for contacts in the old contact service format
      const oldContacts = JSON.parse(localStorage.getItem('contactSphereContacts') || '[]');
      const oldDeletedContacts = JSON.parse(localStorage.getItem('contactSphereDeletedContacts') || '[]');
      
      // Also check for contacts in the new format
      const newContacts = JSON.parse(localStorage.getItem('contacts') || '[]');
      const whatsappMessages = JSON.parse(localStorage.getItem('whatsappMessages') || '[]');
      const whatsappSentMessages = JSON.parse(localStorage.getItem('whatsappSentMessages') || '[]');
      const categories = JSON.parse(localStorage.getItem('categories') || '[]');
      const settings = JSON.parse(localStorage.getItem('settings') || '{}');

      // Migrate contacts from old format
      for (const contact of oldContacts) {
        await this.addContact(contact);
      }

      // Migrate deleted contacts from old format
      for (const contact of oldDeletedContacts) {
        contact.isDeleted = true;
        contact.deletedAt = contact.deletedAt || new Date().toISOString();
        await this.addContact(contact);
      }

      // Migrate contacts from new format (if any)
      for (const contact of newContacts) {
        await this.addContact(contact);
      }

      // Migrate WhatsApp messages from new format
      for (const message of whatsappMessages) {
        await this.addWhatsAppMessage(message);
      }

      // Migrate WhatsApp sent messages from old format
      for (const message of whatsappSentMessages) {
        const dbMessage: WhatsAppMessage = {
          id: message.id,
          contactId: message.contactId,
          phoneNumber: message.contactPhone,
          message: message.message.text || message.message,
          messageType: 'text',
          attachments: message.message.attachments || [],
          status: message.status,
          timestamp: message.sentAt,
          messageId: message.id
        };
        await this.addWhatsAppMessage(dbMessage);
      }

      // Migrate categories
      for (const category of categories) {
        await this.addCategory(category);
      }

      // Migrate settings
      for (const [key, value] of Object.entries(settings)) {
        await this.updateSetting(key, value as string);
      }

      console.log('Migration from localStorage completed');
      console.log(`Migrated ${oldContacts.length + newContacts.length} contacts, ${oldDeletedContacts.length} deleted contacts, ${whatsappMessages.length + whatsappSentMessages.length} WhatsApp messages`);
    } catch (error) {
      console.log('No existing data to migrate or migration failed:', error);
    }
  }

  // Contact operations
  async getContacts(): Promise<Contact[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        console.error('❌ Database not initialized in getContacts');
        reject(new Error('Database not initialized'));
        return;
      }

      console.log('🔄 Getting contacts from IndexedDB...');
      const transaction = this.db.transaction(['contacts'], 'readonly');
      const store = transaction.objectStore('contacts');
      const index = store.index('isDeleted');
      const request = index.getAll(0); // Get non-deleted contacts

      request.onsuccess = () => {
        const contacts = request.result.sort((a, b) => a.name.localeCompare(b.name));
        console.log(`✅ Retrieved ${contacts.length} contacts from IndexedDB`);
        resolve(contacts);
      };

      request.onerror = () => {
        console.error('❌ Error getting contacts from IndexedDB:', request.error);
        reject(request.error);
      };
    });
  }

  async addContact(contact: Contact): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      contact.id = contact.id || `contact-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      contact.createdAt = contact.createdAt || new Date().toISOString();
      contact.updatedAt = new Date().toISOString();

      const transaction = this.db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.add(contact);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async updateContact(contact: Contact): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      contact.updatedAt = new Date().toISOString();

      const transaction = this.db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.put(contact);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async deleteContact(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const contact = getRequest.result;
        if (contact) {
          contact.isDeleted = true;
          contact.deletedAt = new Date().toISOString();
          
          const putRequest = store.put(contact);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve();
        }
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  async restoreContact(id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const contact = getRequest.result;
        if (contact) {
          contact.isDeleted = false;
          contact.deletedAt = null;
          
          const putRequest = store.put(contact);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve();
        }
      };

      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // WhatsApp message operations
  async getWhatsAppMessages(): Promise<WhatsAppMessage[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['whatsappMessages'], 'readonly');
      const store = transaction.objectStore('whatsappMessages');
      const index = store.index('timestamp');
      const request = index.getAll();

      request.onsuccess = () => {
        const messages = request.result.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
        
        console.log('🗄️ Database Service - Retrieved WhatsApp messages:');
        console.log('🗄️ Total messages in database:', messages.length);
        console.log('🗄️ Messages with timestamps:', messages.map(msg => ({
          id: msg.id,
          phoneNumber: msg.phoneNumber,
          timestamp: msg.timestamp,
          date: new Date(msg.timestamp).toDateString()
        })));
        
        resolve(messages);
      };

      request.onerror = () => reject(request.error);
    });
  }

  async addWhatsAppMessage(message: WhatsAppMessage): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      message.id = message.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      message.timestamp = message.timestamp || new Date().toISOString();

      const transaction = this.db.transaction(['whatsappMessages'], 'readwrite');
      const store = transaction.objectStore('whatsappMessages');
      const request = store.add(message);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Category operations
  async getCategories(): Promise<Category[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['categories'], 'readonly');
      const store = transaction.objectStore('categories');
      const request = store.getAll();

      request.onsuccess = () => {
        const categories = request.result.sort((a, b) => a.name.localeCompare(b.name));
        resolve(categories);
      };

      request.onerror = () => reject(request.error);
    });
  }

  async addCategory(category: Category): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      category.id = category.id || `cat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      category.createdAt = category.createdAt || new Date().toISOString();

      const transaction = this.db.transaction(['categories'], 'readwrite');
      const store = transaction.objectStore('categories');
      const request = store.add(category);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Settings operations
  async getSettings(): Promise<Settings> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['settings'], 'readonly');
      const store = transaction.objectStore('settings');
      const request = store.getAll();

      request.onsuccess = () => {
        const settings: Settings = {};
        request.result.forEach(item => {
          settings[item.key] = item.value;
        });
        resolve(settings);
      };

      request.onerror = () => reject(request.error);
    });
  }

  async updateSetting(key: string, value: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['settings'], 'readwrite');
      const store = transaction.objectStore('settings');
      const request = store.put({ key, value, updatedAt: new Date().toISOString() });

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // File operations
  async saveFile(file: File, contactId?: string, messageId?: string): Promise<FileAttachment> {
    const fileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Save file metadata to IndexedDB
    const fileAttachment: FileAttachment = {
      id: fileId,
      name: file.name,
      type: file.type,
      size: file.size,
      contactId,
      messageId,
      createdAt: new Date().toISOString(),
      path: null
    };

    // Save file to File System if available
    if (this.fileHandle && file.size > 1024 * 1024) { // Files larger than 1MB
      try {
        const fileHandle = await this.fileHandle.getFileHandle(fileId, { create: true });
        const writable = await fileHandle.createWritable();
        await writable.write(file);
        await writable.close();
        fileAttachment.path = fileId;
      } catch (error) {
        console.error('Failed to save file to File System:', error);
      }
    }

    // Save file data to IndexedDB (for smaller files or as backup)
    if (!fileAttachment.path || file.size <= 1024 * 1024) {
      const arrayBuffer = await file.arrayBuffer();
      fileAttachment.data = arrayBuffer;
    }

    // Save metadata to IndexedDB
    await new Promise<void>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['files'], 'readwrite');
      const store = transaction.objectStore('files');
      const request = store.add(fileAttachment);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });

    return fileAttachment;
  }

  async getFile(fileId: string): Promise<File | null> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['files'], 'readonly');
      const store = transaction.objectStore('files');
      const request = store.get(fileId);

      request.onsuccess = async () => {
        const fileAttachment = request.result;
        if (!fileAttachment) {
          resolve(null);
          return;
        }

        try {
          let file: File;

          if (fileAttachment.path && this.fileHandle) {
            // Try to get file from File System
            const fileHandle = await this.fileHandle.getFileHandle(fileAttachment.path);
            file = await fileHandle.getFile();
          } else if (fileAttachment.data) {
            // Get file from IndexedDB
            const blob = new Blob([fileAttachment.data], { type: fileAttachment.type });
            file = new File([blob], fileAttachment.name, { type: fileAttachment.type });
          } else {
            resolve(null);
            return;
          }

          resolve(file);
        } catch (error) {
          console.error('Error retrieving file:', error);
          resolve(null);
        }
      };

      request.onerror = () => reject(request.error);
    });
  }

  // Backup and restore operations
  async createBackup(): Promise<string> {
    const backup = {
      contacts: await this.getContacts(),
      whatsappMessages: await this.getWhatsAppMessages(),
      categories: await this.getCategories(),
      settings: await this.getSettings(),
      timestamp: new Date().toISOString(),
      version: '2.0'
    };

    const backupData = JSON.stringify(backup, null, 2);

    // Save backup to IndexedDB
    await new Promise<void>((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = this.db.transaction(['backups'], 'readwrite');
      const store = transaction.objectStore('backups');
      const request = store.add({
        timestamp: backup.timestamp,
        data: backupData
      });

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });

    return backupData;
  }

  async restoreBackup(backupData: string): Promise<void> {
    try {
      const backup = JSON.parse(backupData);

      // Clear existing data
      await this.clearAllData();

      // Restore data
      for (const contact of backup.contacts || []) {
        await this.addContact(contact);
      }

      for (const message of backup.whatsappMessages || []) {
        await this.addWhatsAppMessage(message);
      }

      for (const category of backup.categories || []) {
        await this.addCategory(category);
      }

      for (const [key, value] of Object.entries(backup.settings || {})) {
        await this.updateSetting(key, value as string);
      }

      console.log('Backup restored successfully');
    } catch (error) {
      throw new Error('Invalid backup data format');
    }
  }

  private async clearAllData(): Promise<void> {
    const stores = ['contacts', 'whatsappMessages', 'categories', 'settings', 'files'];
    
    for (const storeName of stores) {
      await new Promise<void>((resolve, reject) => {
        if (!this.db) {
          reject(new Error('Database not initialized'));
          return;
        }

        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.clear();

        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });
    }
  }

  // Export/Import for deployment
  async exportForDeployment(): Promise<string> {
    return await this.createBackup();
  }

  async importFromDeployment(data: string): Promise<void> {
    await this.restoreBackup(data);
  }

  // Storage monitoring
  async getStorageInfo(): Promise<{ used: number; available: number; percentage: number }> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    // Get database size (approximate)
    const transaction = this.db.transaction(['contacts', 'whatsappMessages', 'files'], 'readonly');
    const stores = ['contacts', 'whatsappMessages', 'files'];
    let totalSize = 0;

    for (const storeName of stores) {
      const store = transaction.objectStore(storeName);
      const request = store.getAll();
      
      await new Promise<void>((resolve) => {
        request.onsuccess = () => {
          const data = JSON.stringify(request.result);
          totalSize += new Blob([data]).size;
          resolve();
        };
      });
    }

    // IndexedDB typically has 50MB+ limit
    const available = 50 * 1024 * 1024; // 50MB
    const percentage = (totalSize / available) * 100;

    return {
      used: totalSize,
      available,
      percentage
    };
  }

  // Force re-migration from localStorage (useful for debugging)
  async forceRemigrateFromLocalStorage(): Promise<void> {
    try {
      console.log('Force re-migrating from localStorage...');
      
      // Clear existing data first
      await this.clearAllData();
      
      // Then migrate
      await this.migrateFromLocalStorage();
      
      console.log('Force re-migration completed');
    } catch (error) {
      console.error('Force re-migration failed:', error);
    }
  }
}

// Export the class for type usage
export { EnhancedDatabaseService };

// Export singleton instance
export const enhancedDatabaseService = new EnhancedDatabaseService(); 