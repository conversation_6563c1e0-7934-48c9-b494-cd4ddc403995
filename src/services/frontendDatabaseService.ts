// Frontend Database Service using IndexedDB
// This provides persistent storage that works across devices and users
// The database is stored in the browser but persists across sessions

import { Contact } from '@/types/Contact';

class FrontendDatabaseService {
  private dbName = 'ContactSphereDB';
  private dbVersion = 1;
  private db: IDBDatabase | null = null;

  // Initialize the database
  async initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('Database failed to open');
        reject(new Error('Failed to open database'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ Frontend Database opened successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create contacts object store
        if (!db.objectStoreNames.contains('contacts')) {
          const contactStore = db.createObjectStore('contacts', { keyPath: 'id' });
          contactStore.createIndex('name', 'name', { unique: false });
          contactStore.createIndex('email', 'email', { unique: false });
          contactStore.createIndex('phone', 'phone', { unique: false });
          contactStore.createIndex('city', 'city', { unique: false });
          contactStore.createIndex('state', 'state', { unique: false });
          contactStore.createIndex('category', 'category', { unique: false });
          console.log('📋 Contacts table created');
        }

        // Create deleted contacts object store
        if (!db.objectStoreNames.contains('deletedContacts')) {
          const deletedStore = db.createObjectStore('deletedContacts', { keyPath: 'id' });
          deletedStore.createIndex('deletedAt', 'deletedAt', { unique: false });
          console.log('🗑️ Deleted contacts table created');
        }

        // Create app settings object store
        if (!db.objectStoreNames.contains('appSettings')) {
          db.createObjectStore('appSettings', { keyPath: 'key' });
          console.log('⚙️ App settings table created');
        }

        console.log('🚀 Frontend Database setup complete');
      };
    });
  }

  // Ensure database is initialized
  private async ensureDatabase(): Promise<IDBDatabase> {
    if (!this.db) {
      await this.initializeDatabase();
    }
    return this.db!;
  }

  // Get all contacts
  async getAllContacts(): Promise<Contact[]> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readonly');
      const store = transaction.objectStore('contacts');
      const request = store.getAll();

      request.onsuccess = () => {
        const contacts = request.result || [];
        console.log(`📋 Retrieved ${contacts.length} contacts from database`);
        resolve(contacts);
      };

      request.onerror = () => {
        console.error('❌ Failed to get contacts from database');
        reject(new Error('Failed to get contacts'));
      };
    });
  }

  // Add a new contact
  async addContact(contact: Contact): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.add(contact);

      request.onsuccess = () => {
        console.log('✅ Contact added to database:', contact.name);
        resolve();
      };

      request.onerror = () => {
        console.error('❌ Failed to add contact to database:', contact.name);
        reject(new Error('Failed to add contact'));
      };
    });
  }

  // Update an existing contact
  async updateContact(contact: Contact): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readwrite');
      const store = transaction.objectStore('contacts');
      const request = store.put(contact);

      request.onsuccess = () => {
        console.log('✅ Contact updated in database:', contact.name);
        resolve();
      };

      request.onerror = () => {
        console.error('❌ Failed to update contact in database:', contact.name);
        reject(new Error('Failed to update contact'));
      };
    });
  }

  // Delete a contact (move to deleted contacts)
  async deleteContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    
    // First get the contact
    const contact = await this.getContactById(contactId);
    if (!contact) {
      throw new Error('Contact not found');
    }

    // Add deletedAt timestamp
    const deletedContact = {
      ...contact,
      deletedAt: new Date().toISOString()
    };

    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts'], 'readwrite');
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');

      // Remove from contacts
      const deleteRequest = contactStore.delete(contactId);
      
      deleteRequest.onsuccess = () => {
        // Add to deleted contacts
        const addRequest = deletedStore.add(deletedContact);
        
        addRequest.onsuccess = () => {
          console.log('🗑️ Contact moved to deleted in database:', contact.name);
          resolve();
        };
        
        addRequest.onerror = () => {
          console.error('❌ Failed to move contact to deleted in database');
          reject(new Error('Failed to move contact to deleted'));
        };
      };

      deleteRequest.onerror = () => {
        console.error('❌ Failed to delete contact from database');
        reject(new Error('Failed to delete contact'));
      };
    });
  }

  // Get a contact by ID
  async getContactById(contactId: string): Promise<Contact | null> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts'], 'readonly');
      const store = transaction.objectStore('contacts');
      const request = store.get(contactId);

      request.onsuccess = () => {
        resolve(request.result || null);
      };

      request.onerror = () => {
        reject(new Error('Failed to get contact'));
      };
    });
  }

  // Get all deleted contacts
  async getDeletedContacts(): Promise<Contact[]> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['deletedContacts'], 'readonly');
      const store = transaction.objectStore('deletedContacts');
      const request = store.getAll();

      request.onsuccess = () => {
        const contacts = request.result || [];
        console.log(`🗑️ Retrieved ${contacts.length} deleted contacts from database`);
        resolve(contacts);
      };

      request.onerror = () => {
        console.error('❌ Failed to get deleted contacts from database');
        reject(new Error('Failed to get deleted contacts'));
      };
    });
  }

  // Restore a deleted contact
  async restoreContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts'], 'readwrite');
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');

      // Get from deleted contacts
      const getRequest = deletedStore.get(contactId);
      
      getRequest.onsuccess = () => {
        const deletedContact = getRequest.result;
        if (!deletedContact) {
          reject(new Error('Deleted contact not found'));
          return;
        }

        // Remove deletedAt timestamp
        const { deletedAt, ...restoredContact } = deletedContact;

        // Add back to contacts
        const addRequest = contactStore.add(restoredContact);
        
        addRequest.onsuccess = () => {
          // Remove from deleted contacts
          const deleteRequest = deletedStore.delete(contactId);
          
          deleteRequest.onsuccess = () => {
            console.log('♻️ Contact restored in database:', restoredContact.name);
            resolve();
          };
          
          deleteRequest.onerror = () => {
            reject(new Error('Failed to remove from deleted contacts'));
          };
        };
        
        addRequest.onerror = () => {
          reject(new Error('Failed to restore contact'));
        };
      };

      getRequest.onerror = () => {
        reject(new Error('Failed to get deleted contact'));
      };
    });
  }

  // Permanently delete a contact
  async permanentlyDeleteContact(contactId: string): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['deletedContacts'], 'readwrite');
      const store = transaction.objectStore('deletedContacts');
      const request = store.delete(contactId);

      request.onsuccess = () => {
        console.log('💀 Contact permanently deleted from database');
        resolve();
      };

      request.onerror = () => {
        console.error('❌ Failed to permanently delete contact from database');
        reject(new Error('Failed to permanently delete contact'));
      };
    });
  }

  // Bulk import contacts
  async importContacts(contacts: Contact[]): Promise<{ success: number; failed: number }> {
    const db = await this.ensureDatabase();
    let success = 0;
    let failed = 0;

    console.log(`📥 Starting import of ${contacts.length} contacts to database...`);

    for (const contact of contacts) {
      try {
        await this.addContact(contact);
        success++;
      } catch (error) {
        console.error('❌ Failed to import contact to database:', contact.name, error);
        failed++;
      }
    }

    console.log(`📥 Import complete: ${success} success, ${failed} failed`);
    return { success, failed };
  }

  // Clear all data (for testing/reset)
  async clearAllData(): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['contacts', 'deletedContacts', 'appSettings'], 'readwrite');
      
      const contactStore = transaction.objectStore('contacts');
      const deletedStore = transaction.objectStore('deletedContacts');
      const settingsStore = transaction.objectStore('appSettings');

      const clearContacts = contactStore.clear();
      const clearDeleted = deletedStore.clear();
      const clearSettings = settingsStore.clear();

      let completed = 0;
      const total = 3;

      const checkComplete = () => {
        completed++;
        if (completed === total) {
          console.log('🧹 All database data cleared');
          resolve();
        }
      };

      clearContacts.onsuccess = checkComplete;
      clearDeleted.onsuccess = checkComplete;
      clearSettings.onsuccess = checkComplete;

      clearContacts.onerror = () => reject(new Error('Failed to clear contacts'));
      clearDeleted.onerror = () => reject(new Error('Failed to clear deleted contacts'));
      clearSettings.onerror = () => reject(new Error('Failed to clear settings'));
    });
  }

  // Get database statistics
  async getDatabaseStats(): Promise<{ totalContacts: number; deletedContacts: number }> {
    const [contacts, deleted] = await Promise.all([
      this.getAllContacts(),
      this.getDeletedContacts()
    ]);

    const stats = {
      totalContacts: contacts.length,
      deletedContacts: deleted.length
    };

    console.log('📊 Database stats:', stats);
    return stats;
  }

  // Save app settings
  async saveSetting(key: string, value: any): Promise<void> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appSettings'], 'readwrite');
      const store = transaction.objectStore('appSettings');
      const request = store.put({ key, value });

      request.onsuccess = () => {
        console.log('⚙️ Setting saved to database:', key);
        resolve();
      };

      request.onerror = () => {
        console.error('❌ Failed to save setting to database:', key);
        reject(new Error('Failed to save setting'));
      };
    });
  }

  // Get app setting
  async getSetting(key: string): Promise<any> {
    const db = await this.ensureDatabase();
    return new Promise((resolve, reject) => {
      const transaction = db.transaction(['appSettings'], 'readonly');
      const store = transaction.objectStore('appSettings');
      const request = store.get(key);

      request.onsuccess = () => {
        const result = request.result;
        const value = result ? result.value : null;
        console.log('⚙️ Setting retrieved from database:', key, value);
        resolve(value);
      };

      request.onerror = () => {
        console.error('❌ Failed to get setting from database:', key);
        reject(new Error('Failed to get setting'));
      };
    });
  }

  // Check if database is available and working
  async testConnection(): Promise<boolean> {
    try {
      await this.ensureDatabase();
      const stats = await this.getDatabaseStats();
      console.log('✅ Frontend Database connection test successful:', stats);
      return true;
    } catch (error) {
      console.error('❌ Frontend Database connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const frontendDatabaseService = new FrontendDatabaseService(); 