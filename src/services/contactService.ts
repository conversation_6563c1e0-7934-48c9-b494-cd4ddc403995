import { Contact } from '@/types/Contact';
import { browserSQLiteService } from './browserSQLiteService';

// Browser SQLite Contact type (simple)
interface SQLiteContact {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags?: string;
  created_at?: string;
  updated_at?: string;
}

// Type adapters
function sqliteToFrontendContact(sqliteContact: SQLiteContact): Contact {
  return {
    id: sqliteContact.id?.toString() || '',
    name: sqliteContact.name,
    phone: sqliteContact.phone,
    requirements: sqliteContact.notes || '',
    categories: sqliteContact.tags ? sqliteContact.tags.split(',').map(t => t.trim()) : [],
    state: sqliteContact.company || '',
    city: sqliteContact.position || '',
    date: sqliteContact.created_at || new Date().toISOString(),
    isFavorite: false,
    isPinned: false,
    isShortlisted: false,
    notes: sqliteContact.notes || '',
    attachments: [],
    buildType: 'Residential',
    kanbanStage: 'lead',
    reminders: [],
    hasActiveReminder: false
  };
}

function frontendToSqliteContact(contact: Partial<Contact>): Partial<SQLiteContact> {
  return {
    id: contact.id ? parseInt(contact.id) : undefined,
    name: contact.name || '',
    phone: contact.phone || '',
    email: contact.phone || '', // Use phone as email fallback
    company: contact.state || '',
    position: contact.city || '',
    notes: contact.requirements || contact.notes || '',
    tags: contact.categories?.join(', ') || '',
    created_at: contact.date || undefined,
    updated_at: new Date().toISOString()
  };
}

class ContactService {
  async getAllContacts(): Promise<Contact[]> {
    try {
      await browserSQLiteService.initialize();
      const sqliteContacts = await browserSQLiteService.getAllContacts();
      return sqliteContacts.map(sqliteToFrontendContact);
    } catch (error) {
      console.error('Error fetching contacts:', error);
      return [];
    }
  }

  async getContactById(id: string): Promise<Contact | null> {
    try {
      await browserSQLiteService.initialize();
      const contacts = await this.getAllContacts();
      return contacts.find(contact => contact.id === id) || null;
    } catch (error) {
      console.error('Error fetching contact:', error);
      return null;
    }
  }

  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    try {
      await browserSQLiteService.initialize();
      const sqliteContact = frontendToSqliteContact(contact);
      const addedSqliteContact = await browserSQLiteService.addContact(sqliteContact as Omit<SQLiteContact, 'id'>);
      return sqliteToFrontendContact(addedSqliteContact);
    } catch (error) {
      console.error('Error adding contact:', error);
      throw error;
    }
  }

  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact | null> {
    try {
      await browserSQLiteService.initialize();
      const numericId = parseInt(id);
      const sqliteUpdates = frontendToSqliteContact(updates);
      const updatedSqliteContact = await browserSQLiteService.updateContact(numericId, sqliteUpdates);
      return updatedSqliteContact ? sqliteToFrontendContact(updatedSqliteContact) : null;
    } catch (error) {
      console.error('Error updating contact:', error);
      return null;
    }
  }

  async deleteContact(id: string): Promise<boolean> {
    try {
      await browserSQLiteService.initialize();
      const numericId = parseInt(id);
      return await browserSQLiteService.deleteContact(numericId);
    } catch (error) {
      console.error('Error deleting contact:', error);
      return false;
    }
  }

  async searchContacts(query: string): Promise<Contact[]> {
    try {
      const contacts = await this.getAllContacts();
      const lowercaseQuery = query.toLowerCase();
      
      return contacts.filter(contact =>
        contact.name.toLowerCase().includes(lowercaseQuery) ||
        contact.phone.includes(query) ||
        contact.requirements.toLowerCase().includes(lowercaseQuery) ||
        contact.state.toLowerCase().includes(lowercaseQuery) ||
        contact.city.toLowerCase().includes(lowercaseQuery) ||
        contact.notes.toLowerCase().includes(lowercaseQuery) ||
        contact.categories.some(cat => cat.toLowerCase().includes(lowercaseQuery))
      );
    } catch (error) {
      console.error('Error searching contacts:', error);
      return [];
    }
  }

  async getContactGroups() {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.getContactGroups();
    } catch (error) {
      console.error('Error fetching contact groups:', error);
      return [];
    }
  }

  async addContactGroup(group: { name: string; description?: string }) {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.addContactGroup(group);
    } catch (error) {
      console.error('Error adding contact group:', error);
      throw error;
    }
  }

  async getMediaFolders() {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.getMediaFolders();
    } catch (error) {
      console.error('Error fetching media folders:', error);
      return [];
    }
  }

  async addMediaFolder(folder: { name: string; description?: string }) {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.addMediaFolder(folder);
    } catch (error) {
      console.error('Error adding media folder:', error);
      throw error;
    }
  }

  async getMediaFiles(folderId?: number) {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.getMediaFiles(folderId);
    } catch (error) {
      console.error('Error fetching media files:', error);
      return [];
    }
  }

  async addMediaFile(file: File, folderId: number) {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.addMediaFile(file, folderId);
    } catch (error) {
      console.error('Error adding media file:', error);
      throw error;
    }
  }

  async addSentMessage(message: any) {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.addSentMessage(message);
    } catch (error) {
      console.error('Error adding sent message:', error);
      throw error;
    }
  }

  async getSentMessages() {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.getSentMessages();
    } catch (error) {
      console.error('Error fetching sent messages:', error);
      return [];
    }
  }

  async clearSentMessages() {
    try {
      await browserSQLiteService.initialize();
      return await browserSQLiteService.clearSentMessages();
    } catch (error) {
      console.error('Error clearing sent messages:', error);
      throw error;
    }
  }

  // Database management
  getStorageInfo() {
    return browserSQLiteService.getStorageInfo();
  }

  exportDatabase(): string {
    return browserSQLiteService.exportDatabase();
  }

  async importDatabase(base64Data: string): Promise<void> {
    return await browserSQLiteService.importDatabase(base64Data);
  }
}

export const contactService = new ContactService(); 