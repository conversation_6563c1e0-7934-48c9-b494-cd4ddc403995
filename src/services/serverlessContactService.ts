// Serverless Contact Service - Uses PHP endpoints for server-side storage
import { Contact } from '../types/Contact';

interface Category {
  id: string;
  name: string;
  color: string;
  description: string;
}

interface Settings {
  theme: string;
  language: string;
  notifications: boolean;
  autoBackup: boolean;
}

class ServerlessContactService {
  private readonly API_BASE = '/api'; // PHP endpoints on same server

  // Get all contacts from server
  async getAllContacts(): Promise<Contact[]> {
    try {
      const response = await fetch(`${this.API_BASE}/contacts.php?action=get`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded', data.contacts.length, 'contacts from server');
        return data.contacts;
      }
    } catch (error) {
      console.error('Error loading contacts from server:', error);
    }
    
    // Fallback to localStorage if server fails
    const localContacts = localStorage.getItem('contacts');
    return localContacts ? JSON.parse(localContacts) : [];
  }

  // Add contact to server
  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    try {
      const response = await fetch(`${this.API_BASE}/contacts.php?action=add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(contact)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Contact added to server:', contact.name);
        return result.contact;
      }
    } catch (error) {
      console.error('Error adding contact to server:', error);
    }

    // Fallback to localStorage
    const newContact: Contact = {
      ...contact,
      id: Date.now().toString()
    };
    
    const contacts = await this.getAllContacts();
    contacts.push(newContact);
    localStorage.setItem('contacts', JSON.stringify(contacts));
    console.log('📱 Contact added to localStorage:', contact.name);
    
    return newContact;
  }

  // Update contact on server
  async updateContact(id: string, updates: Partial<Contact>): Promise<Contact> {
    try {
      const response = await fetch(`${this.API_BASE}/contacts.php?action=update&id=${id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Contact updated on server:', updates.name);
        return result.contact;
      }
    } catch (error) {
      console.error('Error updating contact on server:', error);
    }

    // Fallback to localStorage
    const contacts = await this.getAllContacts();
    const index = contacts.findIndex(c => c.id === id);
    if (index !== -1) {
      contacts[index] = { ...contacts[index], ...updates };
      localStorage.setItem('contacts', JSON.stringify(contacts));
      console.log('📱 Contact updated in localStorage:', updates.name);
      return contacts[index];
    }
    
    throw new Error('Contact not found');
  }

  // Delete contact from server
  async deleteContact(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/contacts.php?action=delete&id=${id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        console.log('✅ Contact deleted from server');
        return true;
      }
    } catch (error) {
      console.error('Error deleting contact from server:', error);
    }

    // Fallback to localStorage
    const contacts = await this.getAllContacts();
    const filteredContacts = contacts.filter(c => c.id !== id);
    localStorage.setItem('contacts', JSON.stringify(filteredContacts));
    console.log('📱 Contact deleted from localStorage');
    return true;
  }

  // Upload files to server
  async uploadFiles(files: FileList): Promise<any[]> {
    const uploadedFiles = [];
    
    for (const file of Array.from(files)) {
      try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('folder', this.getFileFolder(file));
        
        const response = await fetch(`${this.API_BASE}/upload.php`, {
          method: 'POST',
          body: formData
        });

        if (response.ok) {
          const result = await response.json();
          uploadedFiles.push({
            id: result.id,
            name: file.name,
            type: file.type,
            size: file.size,
            url: result.url,
            folder: this.getFileFolder(file),
            uploadDate: new Date().toISOString()
          });
          console.log('✅ File uploaded to server:', file.name);
        }
      } catch (error) {
        console.error('Failed to upload file:', file.name, error);
        // Fallback to blob URL
        const url = URL.createObjectURL(file);
        uploadedFiles.push({
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: file.name,
          type: file.type,
          size: file.size,
          url: url,
          folder: this.getFileFolder(file),
          uploadDate: new Date().toISOString()
        });
      }
    }
    
    return uploadedFiles;
  }

  // Get uploaded files from server
  async getUploadedFiles(): Promise<any[]> {
    try {
      const response = await fetch(`${this.API_BASE}/files.php?action=list`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded', data.files.length, 'files from server');
        return data.files;
      }
    } catch (error) {
      console.error('Error loading files from server:', error);
    }
    
    return [];
  }

  // Get categories from server
  async getCategories(): Promise<Category[]> {
    try {
      const response = await fetch(`${this.API_BASE}/categories.php?action=get`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded', data.categories.length, 'categories from server');
        return data.categories;
      }
    } catch (error) {
      console.error('Error loading categories from server:', error);
    }
    
    return [];
  }

  // Add category to server
  async addCategory(category: Omit<Category, 'id'>): Promise<Category> {
    try {
      const response = await fetch(`${this.API_BASE}/categories.php?action=add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(category)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Category added to server:', category.name);
        return result.category;
      }
    } catch (error) {
      console.error('Error adding category to server:', error);
    }

    // Fallback
    const newCategory: Category = {
      ...category,
      id: Date.now().toString()
    };
    return newCategory;
  }

  // Get settings from server
  async getSettings(): Promise<Settings> {
    try {
      const response = await fetch(`${this.API_BASE}/settings.php?action=get`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded settings from server');
        return data.settings;
      }
    } catch (error) {
      console.error('Error loading settings from server:', error);
    }
    
    return {
      theme: 'light',
      language: 'en',
      notifications: true,
      autoBackup: true
    };
  }

  // Update settings on server
  async updateSettings(settings: Partial<Settings>): Promise<Settings> {
    try {
      const response = await fetch(`${this.API_BASE}/settings.php?action=update`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Settings updated on server');
        return result.settings;
      }
    } catch (error) {
      console.error('Error updating settings on server:', error);
    }

    return { ...await this.getSettings(), ...settings };
  }

  // Export data from server
  async exportData(): Promise<any> {
    try {
      const response = await fetch(`${this.API_BASE}/export.php`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Data exported from server');
        return data;
      }
    } catch (error) {
      console.error('Error exporting data from server:', error);
    }
    
    // Fallback to localStorage
    return {
      contacts: await this.getAllContacts(),
      categories: await this.getCategories(),
      settings: await this.getSettings(),
      files: await this.getUploadedFiles()
    };
  }

  // Import data to server
  async importData(data: any): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/import.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        console.log('✅ Data imported to server');
        return true;
      }
    } catch (error) {
      console.error('Error importing data to server:', error);
    }
    
    return false;
  }

  // Helper methods
  private getFileFolder(file: File): string {
    const type = file.type.toLowerCase();
    if (type.startsWith('image/')) return 'Images';
    if (type.startsWith('video/')) return 'Videos';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.includes('pdf')) return 'Documents/PDF';
    if (type.includes('word') || type.includes('doc')) return 'Documents/Word';
    if (type.includes('excel') || type.includes('sheet')) return 'Documents/Excel';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'Documents/PowerPoint';
    if (type.includes('text')) return 'Documents/Text';
    return 'Other';
  }

  // WhatsApp methods (still use localStorage for now)
  async addSentMessage(message: any): Promise<any> {
    const messages = JSON.parse(localStorage.getItem('whatsappMessages') || '[]');
    messages.push(message);
    localStorage.setItem('whatsappMessages', JSON.stringify(messages));
    return message;
  }

  async getSentMessages(): Promise<any[]> {
    return JSON.parse(localStorage.getItem('whatsappMessages') || '[]');
  }

  async clearSentMessages(): Promise<void> {
    localStorage.removeItem('whatsappMessages');
  }

  // Storage info
  getStorageInfo(): any {
    return {
      type: 'server',
      persistent: true,
      fallback: 'localStorage'
    };
  }
}

export const serverlessContactService = new ServerlessContactService();
export default serverlessContactService; 