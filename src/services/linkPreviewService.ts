export interface LinkPreview {
  url: string;
  title: string;
  description: string;
  image: string;
  siteName?: string;
  isEditable?: boolean;
}

class LinkPreviewService {
  private cache = new Map<string, LinkPreview>();

  async fetchPreview(url: string): Promise<LinkPreview> {
    console.log('🔗 Fetching preview for URL:', url);
    
    // Check cache first
    if (this.cache.has(url)) {
      console.log('✅ Using cached preview for:', url);
      return this.cache.get(url)!;
    }

    try {
      // Try multiple approaches to fetch preview (Microlink.io first for real thumbnails)
      const preview = await this.fetchWithMultipleApproaches(url);
      console.log('✅ Preview fetched successfully:', preview);
      this.cache.set(url, preview);
      return preview;
    } catch (error) {
      console.error('❌ Error fetching link preview:', error);
      
      // Try custom social media preview as fallback
      const customPreview = this.createCustomPreview(url);
      if (customPreview) {
        console.log('🔄 Using custom preview as fallback for social media:', customPreview);
        this.cache.set(url, customPreview);
        return customPreview;
      }
      
      // Return a basic fallback preview
      const fallback = this.createFallbackPreview(url);
      console.log('🔄 Using basic fallback preview:', fallback);
      this.cache.set(url, fallback);
      return fallback;
    }
  }

  private async fetchWithMultipleApproaches(url: string): Promise<LinkPreview> {
    // First try Microlink.io API (the working solution from old code)
    try {
      const preview = await this.fetchFromMicrolink(url);
      if (preview.title && preview.title !== 'Link Preview Unavailable') {
        return preview;
      }
    } catch (error) {
      console.log('Microlink.io failed:', error);
    }

    // Try multiple proxy services as fallback
    const proxies = [
      `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`,
      `https://cors-anywhere.herokuapp.com/${url}`,
      `https://thingproxy.freeboard.io/fetch/${url}`
    ];

    for (const proxyUrl of proxies) {
      try {
        const preview = await this.fetchFromProxy(proxyUrl, url);
        if (preview.title && preview.title !== 'Link Preview Unavailable') {
          return preview;
        }
      } catch (error) {
        console.log(`Proxy ${proxyUrl} failed:`, error);
        continue;
      }
    }

    // If all approaches fail, create a basic preview
    return this.createBasicPreview(url);
  }

  private async fetchFromMicrolink(url: string): Promise<LinkPreview> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    try {
      console.log('🔍 Fetching preview from Microlink.io for:', url);
      
      const response = await fetch(`https://api.microlink.io?url=${encodeURIComponent(url)}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.warn(`Microlink.io responded with status ${response.status} for ${url}`);
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('📊 Microlink.io response for', url, ':', data);
      
      if (data.status === 'success' && data.data) {
        // Clean the title - remove hashtags, extra whitespace, and unwanted characters
        let cleanTitle = data.data.title || this.extractTitleFromUrl(url);
        cleanTitle = this.cleanTitle(cleanTitle);
        
        const preview = {
          url,
          title: cleanTitle,
          description: '', // Empty description for clean look
          image: data.data.image?.url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+Cg==',
          siteName: new URL(url).hostname, // Just the hostname, no publisher clutter
          isEditable: true
        };
        
        console.log('✅ Microlink.io preview generated:', preview);
        return preview;
      } else {
        console.warn('Microlink.io returned unsuccessful status:', data);
        throw new Error('Invalid response from Microlink.io');
      }
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Microlink.io fetch failed for', url, ':', error);
      throw error;
    }
  }

  private async fetchFromProxy(proxyUrl: string, originalUrl: string): Promise<LinkPreview> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

    try {
      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (data.contents) {
        const html = data.contents;
        return await this.extractMetadata(html, originalUrl);
      } else {
        throw new Error('No content received from proxy');
      }
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async extractMetadata(html: string, url: string): Promise<LinkPreview> {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Extract title with multiple fallbacks and clean it
    const rawTitle = doc.querySelector('meta[property="og:title"]')?.getAttribute('content') ||
                 doc.querySelector('meta[name="twitter:title"]')?.getAttribute('content') ||
                 doc.querySelector('title')?.textContent ||
                 this.extractTitleFromUrl(url);
    const title = this.cleanTitle(rawTitle || '');

    // Skip description for clean look
    const description = '';

    // Extract image with multiple fallbacks
    const rawImage = doc.querySelector('meta[property="og:image"]')?.getAttribute('content') ||
                 doc.querySelector('meta[name="twitter:image"]')?.getAttribute('content') ||
                 doc.querySelector('meta[name="twitter:image:src"]')?.getAttribute('content') ||
                 this.findFirstImageInContent(doc) ||
                    'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+Cg==';

    // Validate and get the best image URL
    const image = await this.getValidImageUrl(rawImage, url);

    // Extract site name
    const siteName = doc.querySelector('meta[property="og:site_name"]')?.getAttribute('content') ||
                    doc.querySelector('meta[name="application-name"]')?.getAttribute('content') ||
                    new URL(url).hostname;

    return {
      url,
      title: title?.trim() || 'Untitled',
      description: description?.trim() || 'No description available',
      image,
      siteName,
      isEditable: true
    };
  }

  private extractTitleFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        return lastPart.replace(/[-_]/g, ' ').replace(/\.[^/.]+$/, '').replace(/\b\w/g, l => l.toUpperCase());
      }
      return urlObj.hostname.replace('www.', '').replace(/\b\w/g, l => l.toUpperCase());
    } catch {
      return 'Untitled Page';
    }
  }

  private cleanTitle(title: string, isCustomTitle: boolean = false): string {
    if (!title) return 'Untitled';
    
    // Don't clean custom titles - use them as-is with minimal processing
    if (isCustomTitle) {
      return title.trim() || 'Untitled';
    }
    
    // Remove hashtags and mentions only from auto-generated titles
    let cleaned = title.replace(/#\w+/g, '').replace(/@\w+/g, '');
    
    // Remove common social media patterns (less aggressive)
    cleaned = cleaned
      .replace(/\s*-\s*(Instagram|Twitter|Facebook|LinkedIn|YouTube).*$/i, '') // Remove platform names at end
      .replace(/\s*\|\s*.*$/, '') // Remove everything after pipe |
      .replace(/\s*•\s*.*$/, '') // Remove everything after bullet •
      // Removed the colon removal - it was too aggressive
      .replace(/\s*\.\.\.$/, '') // Remove trailing ellipsis
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();
    
    // Limit length to reasonable size (more generous for custom titles)
    const maxLength = 80;
    if (cleaned.length > maxLength) {
      cleaned = cleaned.substring(0, maxLength).trim();
      // Don't add ellipsis if it ends with punctuation
      if (!/[.!?]$/.test(cleaned)) {
        cleaned += '...';
      }
    }
    
    return cleaned || 'Untitled';
  }

  private extractDescriptionFromContent(doc: Document): string {
    // Try to find a meaningful description from the content
    const paragraphs = doc.querySelectorAll('p');
    for (const p of paragraphs) {
      const text = p.textContent?.trim();
      if (text && text.length > 50 && text.length < 200) {
        return text;
      }
    }
    return 'No description available';
  }

  private findFirstImageInContent(doc: Document): string | null {
    const images = doc.querySelectorAll('img');
    for (const img of images) {
      const src = img.getAttribute('src');
      if (src && (src.includes('logo') || src.includes('banner') || img.width > 100)) {
        return src;
      }
    }
    return null;
  }

  private resolveImageUrl(imageUrl: string, baseUrl: string): string {
    if (!imageUrl || imageUrl.startsWith('data:')) {
      return imageUrl;
    }

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }
    
    // Handle protocol-relative URLs
    if (imageUrl.startsWith('//')) {
      return `https:${imageUrl}`;
    }
    
    // Handle absolute paths
    if (imageUrl.startsWith('/')) {
      const url = new URL(baseUrl);
      return `${url.protocol}//${url.host}${imageUrl}`;
    }
    
    // Handle relative URLs
    const url = new URL(baseUrl);
    const pathParts = url.pathname.split('/');
    pathParts.pop(); // Remove the last part (filename)
    const basePath = pathParts.join('/');
    return `${url.protocol}//${url.host}${basePath}/${imageUrl}`;
  }

  private createBasicPreview(url: string): LinkPreview {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.replace('www.', '');
      const pathname = urlObj.pathname;
      
      // Create a meaningful title from the URL
      let title = hostname;
      if (pathname && pathname !== '/') {
        const pathParts = pathname.split('/').filter(part => part.length > 0);
        if (pathParts.length > 0) {
          const lastPart = pathParts[pathParts.length - 1];
          title = lastPart.replace(/[-_]/g, ' ').replace(/\.[^/.]+$/, '').replace(/\b\w/g, l => l.toUpperCase());
        }
      }

      return {
        url,
        title: this.cleanTitle(title || hostname),
        description: '', // Empty for clean look
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ci8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+Cg==',
        siteName: hostname,
        isEditable: true
      };
    } catch {
      return this.createFallbackPreview(url);
    }
  }

  private createCustomPreview(url: string): LinkPreview | null {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // Instagram handling
      if (hostname.includes('instagram.com')) {
        return this.createInstagramPreview(url);
      }
      
      // YouTube handling
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        return this.createYouTubePreview(url);
      }
      
      // Twitter handling
      if (hostname.includes('twitter.com') || hostname.includes('x.com')) {
        return this.createTwitterPreview(url);
      }
      
      // Facebook handling
      if (hostname.includes('facebook.com') || hostname.includes('fb.com')) {
        return this.createFacebookPreview(url);
      }
      
      // LinkedIn handling
      if (hostname.includes('linkedin.com')) {
        return this.createLinkedInPreview(url);
      }
      
      return null; // No custom preview available
    } catch {
      return null;
    }
  }

  private createInstagramPreview(url: string): LinkPreview {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);
      
      let title = 'Instagram Post';
      let description = 'Visit Instagram.com for more information.';
      
      // Extract post type and handle
      if (pathParts.includes('reel')) {
        title = 'Instagram Reel';
        description = 'Watch this reel on Instagram';
      } else if (pathParts.includes('p')) {
        title = 'Instagram Post';
        description = 'View this post on Instagram';
      } else if (pathParts.length > 0) {
        const username = pathParts[0];
        title = `@${username} on Instagram`;
        description = `Visit @${username}'s Instagram profile`;
      }
      
      return {
        url,
        title: this.cleanTitle(title),
        description: '', // Empty for clean look
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImlnR3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjU4NTI5O3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjUwJSIgc3R5bGU9InN0b3AtY29sb3I6I2ZkZDgzNTtzdG9wLW9wYWNpdHk6MSIgLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZmY3YTAwO3N0b3Atb3BhY2l0eToxIiAvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiByeD0iNjAiIGZpbGw9InVybCgjaWdHcmFkaWVudCkiLz4KPHJlY3QgeD0iNzUiIHk9Ijc1IiB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgcng9IjMwIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjgiIGZpbGw9Im5vbmUiLz4KPGNpcmNsZSBjeD0iMjEwIiBjeT0iOTAiIHI9IjE1IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
        siteName: 'Instagram',
        isEditable: true
      };
    } catch {
      return this.createFallbackPreview(url);
    }
  }

  private createYouTubePreview(url: string): LinkPreview {
    try {
      // Extract video ID from YouTube URL
      const videoId = this.extractYouTubeVideoId(url);
      const thumbnailUrl = videoId ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg` : null;
      
      return {
        url,
        title: 'YouTube Video',
        description: '', // Empty for clean look
        image: thumbnailUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRkYwMDAwIi8+Cjxwb2x5Z29uIHBvaW50cz0iMTIwLDcwIDEyMCwxMzAgMTgwLDEwMCIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==',
        siteName: 'YouTube',
        isEditable: true
      };
    } catch {
      return this.createFallbackPreview(url);
    }
  }

  private createTwitterPreview(url: string): LinkPreview {
    return {
      url,
      title: 'Twitter Post',
      description: '', // Empty for clean look
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMWRhMWYyIi8+CjxwYXRoIGQ9Ik0yMzAgODBjLTgtMy0xNi01LTI1LTVzLTE3IDItMjUgNWMtMTYgNi0yOCAxOC0zNCAzNGMtMyA4LTUgMTYtNSAyNXMgMiAxNyA1IDI1YzYgMTYgMTggMjggMzQgMzRjOCAzIDE2IDUgMjUgNXMgMTctMiAyNS01YzE2LTYgMjgtMTggMzQtMzRjMy04IDUtMTYgNS0yNXMtMi0xNy01LTI1Yy02LTE2LTE4LTI4LTM0LTM0eiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==',
      siteName: 'Twitter',
      isEditable: true
    };
  }

  private createFacebookPreview(url: string): LinkPreview {
    return {
      url,
      title: 'Facebook Post',
      description: '', // Empty for clean look
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMTg3N2YyIi8+CjxwYXRoIGQ9Ik0xNzUgMjQwVjE2MEgyMDBMMjA1IDEyNUgxNzVWMTEwQzE3NSAxMDAgMTgwIDk1IDE5MCA5NUgyMDVWNjVIMTkwQzE2NSA2NSAxNDUgODUgMTQ1IDExMFYxMjVIMTIwVjE2MEgxNDVWMjQwSDE3NVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
      siteName: 'Facebook',
      isEditable: true
    };
  }

  private createLinkedInPreview(url: string): LinkPreview {
    return {
      url,
      title: 'LinkedIn Post',
      description: '', // Empty for clean look
      image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjMDA3N2I1Ii8+CjxyZWN0IHg9IjYwIiB5PSIxMjAiIHdpZHRoPSIzMCIgaGVpZ2h0PSIxMDAiIGZpbGw9IndoaXRlIi8+CjxjaXJjbGUgY3g9Ijc1IiBjeT0iOTAiIHI9IjE1IiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMTIwIDEyMFYyMjBIMTUwVjE3MEMxNTAgMTU1IDE2MCAxNDUgMTc1IDE0NUMxOTAgMTQ1IDIwMCAxNTUgMjAwIDE3MFYyMjBIMjMwVjE3MUMyMzAgMTM5IDIwNiAxMTUgMTc0IDExNUMxNTcgMTE1IDEzNyAxMjMgMTIwIDEzNVYxMjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
      siteName: 'LinkedIn',
      isEditable: true
    };
  }

  private extractYouTubeVideoId(url: string): string | null {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/v\/([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  private createFallbackPreview(url: string): LinkPreview {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.replace('www.', '');
      
      return {
        url,
        title: this.cleanTitle(hostname.charAt(0).toUpperCase() + hostname.slice(1)),
        description: '', // Empty for clean look
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5MaW5rPC90ZXh0Pgo8L3N2Zz4K',
        siteName: hostname,
        isEditable: true
      };
    } catch {
      return {
        url,
        title: 'Link Preview',
        description: 'Click to visit this link.',
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5MaW5rPC90ZXh0Pgo8L3N2Zz4K',
        siteName: 'unknown',
        isEditable: true
      };
    }
  }

  // Method to create a preview with custom title (bypasses title cleaning)
  async fetchPreviewWithCustomTitle(url: string, customTitle: string): Promise<LinkPreview> {
    console.log('🔗 Fetching preview with custom title:', { url, customTitle });
    
    try {
      // Fetch the basic preview (for image and other metadata)
      const preview = await this.fetchPreview(url);
      
      // Override with custom title (no cleaning applied)
      return {
        ...preview,
        title: this.cleanTitle(customTitle, true), // Pass true for isCustomTitle
        isEditable: true
      };
    } catch (error) {
      console.error('❌ Error fetching preview with custom title:', error);
      // Return fallback with custom title
      return {
        url,
        title: this.cleanTitle(customTitle, true),
        description: '',
        image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ci8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+Cg==',
        siteName: new URL(url).hostname,
        isEditable: true
      };
    }
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCachedPreview(url: string): LinkPreview | undefined {
    return this.cache.get(url);
  }

  private async testImageUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  private async getValidImageUrl(imageUrl: string, baseUrl: string): Promise<string> {
    const resolvedUrl = this.resolveImageUrl(imageUrl, baseUrl);
    
    // Test if the image URL is accessible
    const isValid = await this.testImageUrl(resolvedUrl);
    if (isValid) {
      return resolvedUrl;
    }
    
    // If not accessible, return the fallback SVG
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiM5QjlCQTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD4KPC9zdmc+Cg==';
  }

  // Test method for debugging
  async testLinkPreview(url: string): Promise<void> {
    console.log('🧪 Testing link preview for:', url);
    try {
      const preview = await this.fetchPreview(url);
      console.log('✅ Test result:', preview);
    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  }
}

export const linkPreviewService = new LinkPreviewService(); 