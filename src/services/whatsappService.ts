import QRCode from 'qrcode';
import config from '@/config/environment';

export interface WhatsAppStatus {
  isConnected: boolean;
  qrCode?: string;
  error?: string;
  phoneNumber?: string;
  profileName?: string;
  clientInfo?: any;
  deviceInfo?: {
    phoneNumber?: string;
    profileName?: string;
    platform?: string;
    businessName?: string | null;
    verifiedName?: string | null;
    status?: string | null;
    deviceType?: string;
  };
  lastSeen?: string;
  serverTime?: string;
  initializationInProgress?: boolean;
}

export interface SendMessageResult {
  success: boolean;
  messageId?: string;
  error?: string;
  timestamp: string;
}

export interface BackendConnectionResult {
  success: boolean;
  message: string;
  url: string;
}

class WhatsAppService {
  private apiBaseUrl: string;
  private status: WhatsAppStatus = { isConnected: false };

  constructor() {
    this.apiBaseUrl = config.apiBaseUrl;
    console.log('🔧 WhatsApp Service initialized with API URL:', this.apiBaseUrl);
    console.log('🔧 Environment config:', {
      apiBaseUrl: config.apiBaseUrl,
      isDevelopment: config.isDevelopment,
      isProduction: config.isProduction,
      environment: config.environment
    });
  }

  // Update API base URL and store in localStorage
  updateApiBaseUrl(newUrl: string) {
    // Clean the URL (remove trailing slash if present)
    const cleanUrl = newUrl.replace(/\/$/, '');
    
    this.apiBaseUrl = cleanUrl;
    localStorage.setItem('backendUrl', cleanUrl);
    console.log('🔄 WhatsApp Service API URL updated to:', cleanUrl);
  }

  // Get current API base URL
  getApiBaseUrl(): string {
    return this.apiBaseUrl;
  }

  // Test backend connectivity
  async testBackendConnection(url?: string): Promise<BackendConnectionResult> {
    const testUrl = url || this.apiBaseUrl;
    const cleanUrl = testUrl.replace(/\/$/, '');
    try {
      console.log('🔍 Testing backend connection to:', cleanUrl);
      const response = await fetch(`${cleanUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000)
      });
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Backend connection successful:', data);
        return {
          success: true,
          message: 'Successfully connected to backend',
          url: cleanUrl
        };
      } else {
        console.log('❌ Backend connection failed with status:', response.status);
        return {
          success: false,
          message: `Backend responded with status: ${response.status}`,
          url: cleanUrl
        };
      }
    } catch (error) {
      console.error('❌ Backend connection error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        message: `Connection failed: ${errorMessage}`,
        url: cleanUrl
      };
    }
  }

  // Initialize WhatsApp connection (simplified for minimal backend)
  async initialize(): Promise<WhatsAppStatus> {
    try {
      console.log('🔗 Initializing WhatsApp connection...');
      console.log('🔗 Using API URL:', this.apiBaseUrl);
      
      // Test backend connection first
      const connectionTest = await this.testBackendConnection();
      if (!connectionTest.success) {
        throw new Error(`Backend not reachable: ${connectionTest.message}`);
      }
      
      // Request WhatsApp connection from backend with timeout
      const response = await fetch(`${this.apiBaseUrl}/api/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ WhatsApp initialization request sent successfully');
        
        // Wait a moment for initialization to start
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Get current status with retry
        let retries = 3;
        while (retries > 0) {
          try {
            const statusResponse = await fetch(`${this.apiBaseUrl}/api/status`, {
              signal: AbortSignal.timeout(10000)
            });
            
            if (!statusResponse.ok) {
              throw new Error(`Status check failed: ${statusResponse.status}`);
            }
            
            const statusData = await statusResponse.json();
            
            this.status = {
              isConnected: statusData.connected || false,
              qrCode: statusData.qrCode || undefined,
              phoneNumber: statusData.connectionInfo?.phoneNumber || 'Unknown',
              profileName: statusData.connectionInfo?.profileName || 'Unknown'
            };
            
            return this.status;
          } catch (error) {
            retries--;
            if (retries === 0) {
              throw error;
            }
            console.log(`⚠️ Status check failed, retrying... (${retries} attempts left)`);
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      } else {
        throw new Error(data.message || 'Failed to initialize WhatsApp');
      }
    } catch (error) {
      console.error('❌ Error initializing WhatsApp:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Failed to initialize WhatsApp';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Connection timeout - backend may be slow or unreachable';
        } else if (error.message.includes('fetch')) {
          errorMessage = 'Cannot connect to backend - check URL and ensure backend is running';
        } else {
          errorMessage = error.message;
        }
      }
      
      this.status = {
        isConnected: false,
        error: errorMessage
      };
      return this.status;
    }
  }

  // Get WhatsApp status with improved error handling
  async getStatus(): Promise<WhatsAppStatus> {
    try {
      console.log('📊 Getting WhatsApp status from:', `${this.apiBaseUrl}/api/whatsapp/status`);
      
      const response = await fetch(`${this.apiBaseUrl}/api/whatsapp/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('📊 Enhanced status response:', data);
      
      this.status = {
        isConnected: data.status === 'ready',
        qrCode: data.qrCode || undefined,
        phoneNumber: data.connectionInfo?.phoneNumber || 'Unknown',
        profileName: data.connectionInfo?.profileName || 'Unknown',
        deviceInfo: data.connectionInfo ? {
          platform: data.connectionInfo.platform,
          deviceType: data.connectionInfo.deviceType,
          businessName: data.connectionInfo.businessName,
          status: data.connectionInfo.status
        } : undefined,
        lastSeen: data.lastSeen,
        serverTime: data.serverTime,
        initializationInProgress: data.initializationInProgress
      };
      
      return this.status;
    } catch (error) {
      console.error('❌ Error getting status:', error);
      
      let errorMessage = 'Failed to get status';
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Status check timeout';
        } else if (error.message.includes('fetch')) {
          errorMessage = 'Cannot reach backend';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        isConnected: false,
        error: errorMessage
      };
    }
  }

  // Check if connected
  async isConnected(): Promise<boolean> {
    try {
      const status = await this.getStatus();
      return status.isConnected;
    } catch (error) {
      return false;
    }
  }

  // Upload files (not implemented for minimal backend)
  async uploadFiles(files: File[]): Promise<any[]> {
    console.warn('File upload not implemented for minimal backend');
    return [];
  }

  // Send message
  async sendMessage(phone: string, message: string, attachments: any[] = [], links: any[] = []): Promise<any> {
    try {
      console.log('📤 Sending WhatsApp message to:', phone);
      console.log('📎 Attachments:', attachments.length);
      console.log('🔗 Links:', links.length);
      
      // Process attachments to convert blob URLs to base64 data
      const processedAttachments = await Promise.all(
        attachments.map(async (att) => {
          if (att.url && att.url.startsWith('blob:')) {
            // Convert blob URL to base64 data
            try {
              console.log('🔄 Converting blob URL to base64 for:', att.name);
              const response = await fetch(att.url);
              const blob = await response.blob();
              
              // Get the correct MIME type
              const mimeType = blob.type || att.type || 'application/octet-stream';
              console.log('📋 MIME type detected:', mimeType);
              
              // Convert to base64 using FileReader
              return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                  const base64Data = reader.result;
                  console.log('✅ Converted to data URL successfully');
                  resolve({
                    ...att,
                    url: base64Data,
                    type: mimeType
                  });
                };
                reader.onerror = () => reject(new Error('FileReader failed'));
                reader.readAsDataURL(blob);
              });
            } catch (error) {
              console.error('❌ Error converting blob URL to base64:', error.message);
              return att; // Return original attachment if conversion fails
            }
          }
          return att; // Return original attachment if not a blob URL
        })
      );
      
      const response = await fetch(`${this.apiBaseUrl}/api/send-message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone,
          message,
          attachments: processedAttachments,
          urls: links.map(link => typeof link === 'string' ? link : link.url).filter(url => url)
        }),
        signal: AbortSignal.timeout(30000) // 30 second timeout
      });

      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Message sent successfully:', data);
        return {
          success: true,
          messageId: data.messageId,
          timestamp: data.timestamp || new Date().toISOString(),
          results: data.results || []
        };
      } else {
        throw new Error(data.message || 'Failed to send message');
      }
    } catch (error) {
      console.error('❌ Error sending message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to send message',
        timestamp: new Date().toISOString()
      };
    }
  }

  // Send bulk messages
  async sendBulkMessages(contacts: any[], message: string, attachments: any[] = [], links: any[] = []): Promise<any> {
    try {
      console.log('📤 Sending bulk WhatsApp messages to', contacts.length, 'contacts');
      console.log('📎 Attachments:', attachments.length);
      console.log('🔗 Links:', links.length);
      
      const response = await fetch(`${this.apiBaseUrl}/api/send-bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contacts,
          message,
          attachments: attachments.map(att => ({
            id: att.id,
            name: att.name,
            type: att.type,
            url: att.url,
            size: att.size
          })),
          urls: links.map(link => typeof link === 'string' ? link : link.url).filter(url => url)
        }),
        signal: AbortSignal.timeout(300000) // 5 minute timeout for bulk
      });

      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Bulk messages sent successfully:', data);
        return data;
      } else {
        throw new Error(data.message || 'Failed to send bulk messages');
      }
    } catch (error) {
      console.error('❌ Error sending bulk messages:', error);
      throw error;
    }
  }

  // Manual disconnect WhatsApp
  async disconnect(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Manually disconnecting WhatsApp...');
      
      const response = await fetch(`${this.apiBaseUrl}/api/whatsapp/disconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Update local status
        this.status = {
          isConnected: false,
          phoneNumber: 'Unknown',
          profileName: 'Unknown'
        };
        
        console.log('✅ WhatsApp disconnected successfully');
        return { success: true, message: data.message || 'WhatsApp disconnected successfully' };
      } else {
        throw new Error(data.message || 'Failed to disconnect WhatsApp');
      }
    } catch (error) {
      console.error('❌ Error disconnecting WhatsApp:', error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to disconnect WhatsApp' 
      };
    }
  }

  // Manual reconnect WhatsApp
  async reconnect(): Promise<{ success: boolean; message: string }> {
    try {
      console.log('🔄 Manually reconnecting WhatsApp...');
      
      const response = await fetch(`${this.apiBaseUrl}/api/whatsapp/reconnect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(15000) // 15 second timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ WhatsApp reconnection started');
        return { success: true, message: data.message || 'WhatsApp reconnection started' };
      } else {
        throw new Error(data.message || 'Failed to reconnect WhatsApp');
      }
    } catch (error) {
      console.error('❌ Error reconnecting WhatsApp:', error);
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 'Failed to reconnect WhatsApp' 
      };
    }
  }

  // Clear sessions (simplified for minimal backend)
  async clearSessions(): Promise<void> {
    try {
      // For minimal backend, just disconnect and reconnect
      await this.disconnect();
      console.log('✅ Sessions cleared (disconnected)');
    } catch (error) {
      console.error('❌ Error clearing sessions:', error);
      throw error;
    }
  }

  // Get uploaded files (not implemented for minimal backend)
  async getUploadedFiles(): Promise<any[]> {
    console.warn('Get uploaded files not implemented for minimal backend');
    return [];
  }

  // Delete file (not implemented for minimal backend)
  async deleteFile(filename: string): Promise<void> {
    console.warn('Delete file not implemented for minimal backend');
  }

  // Health check
  async healthCheck(): Promise<{ status: string; timestamp: string; uptime: number }> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/health`);
      const data = await response.json();
      return {
        status: data.status || 'unknown',
        timestamp: data.timestamp || new Date().toISOString(),
        uptime: 0
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: 0
      };
    }
  }

  // Get socket (not used for minimal backend)
  getSocket(): any {
    console.warn('Socket.IO not available for minimal backend');
    return null;
  }
}

export const whatsAppService = new WhatsAppService(); 