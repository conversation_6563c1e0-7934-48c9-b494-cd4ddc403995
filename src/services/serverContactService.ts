// Server-side Contact Service
// Uses PHP API for persistent storage on server

import { Contact } from '@/types/Contact';

// ContactGroup interface for server-side groups
export interface ContactGroup {
  id: string;
  name: string;
  description: string;
  memberCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// API Configuration
const API_BASE_URL = window.location.origin + '/api';

// Helper function to make API requests
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  const response = await fetch(url, { ...defaultOptions, ...options });
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Network error' }));
    throw new Error(error.error || `HTTP ${response.status}`);
  }
  
  return response.json();
}

// Contact Service Class
export class ServerContactService {
  // Get all contacts
  async getContacts(search?: string, limit = 100, offset = 0): Promise<{ contacts: Contact[], total: number }> {
    const params = new URLSearchParams();
    if (search) params.append('search', search);
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    
    const response = await apiRequest(`/contacts?${params}`);
    return {
      contacts: response.contacts.map(this.transformContact),
      total: response.total
    };
  }
  
  // Get single contact
  async getContact(id: string): Promise<Contact | null> {
    try {
      const response = await apiRequest(`/contacts/${id}`);
      return this.transformContact(response);
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }
  
  // Create contact
  async createContact(contact: Omit<Contact, 'id' | 'createdAt' | 'updatedAt'>): Promise<Contact> {
    const response = await apiRequest('/contacts', {
      method: 'POST',
      body: JSON.stringify(contact),
    });
    return this.transformContact(response);
  }
  
  // Update contact
  async updateContact(id: string, contact: Partial<Contact>): Promise<Contact> {
    const response = await apiRequest(`/contacts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(contact),
    });
    return this.transformContact(response);
  }
  
  // Delete contact
  async deleteContact(id: string): Promise<void> {
    await apiRequest(`/contacts/${id}`, {
      method: 'DELETE',
    });
  }
  
  // Search contacts
  async searchContacts(query: string): Promise<Contact[]> {
    const response = await this.getContacts(query);
    return response.contacts;
  }
  
  // Get contacts by group
  async getContactsByGroup(groupId: string): Promise<Contact[]> {
    const response = await apiRequest(`/groups/${groupId}`);
    return response.members.map(this.transformContact);
  }
  
  // Group Management
  async getGroups(): Promise<ContactGroup[]> {
    const response = await apiRequest('/groups');
    return response.groups.map(this.transformGroup);
  }
  
  async createGroup(group: Omit<ContactGroup, 'id' | 'createdAt' | 'updatedAt'>): Promise<ContactGroup> {
    const response = await apiRequest('/groups', {
      method: 'POST',
      body: JSON.stringify(group),
    });
    return this.transformGroup(response);
  }
  
  async updateGroup(id: string, group: Partial<ContactGroup>): Promise<ContactGroup> {
    const response = await apiRequest(`/groups/${id}`, {
      method: 'PUT',
      body: JSON.stringify(group),
    });
    return this.transformGroup(response);
  }
  
  async deleteGroup(id: string): Promise<void> {
    await apiRequest(`/groups/${id}`, {
      method: 'DELETE',
    });
  }
  
  // File Upload
  async uploadFiles(files: FileList, folderId = 1): Promise<any[]> {
    const formData = new FormData();
    
    for (let i = 0; i < files.length; i++) {
      formData.append('files[]', files[i]);
    }
    formData.append('folder_id', folderId.toString());
    
    const response = await fetch(`${API_BASE_URL}/upload`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Upload failed' }));
      throw new Error(error.error || 'Upload failed');
    }
    
    const result = await response.json();
    return result.files;
  }
  
  // Get uploaded files
  async getFiles(folderId?: number, limit = 50, offset = 0): Promise<{ files: any[], total: number }> {
    const params = new URLSearchParams();
    if (folderId) params.append('folder_id', folderId.toString());
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    
    const response = await apiRequest(`/files?${params}`);
    return {
      files: response.files,
      total: response.total
    };
  }
  
  // Delete file
  async deleteFile(fileId: number): Promise<void> {
    await apiRequest('/files', {
      method: 'DELETE',
      body: JSON.stringify({ file_id: fileId }),
    });
  }
  
  // Message History
  async saveMessage(message: {
    phone: string;
    contact_name?: string;
    message_text?: string;
    attachments?: any[];
    links?: string[];
    status?: string;
    error_message?: string;
  }): Promise<any> {
    const response = await apiRequest('/messages', {
      method: 'POST',
      body: JSON.stringify(message),
    });
    return response;
  }
  
  async getMessages(phone?: string, limit = 50, offset = 0): Promise<{ messages: any[], total: number }> {
    const params = new URLSearchParams();
    if (phone) params.append('phone', phone);
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    
    const response = await apiRequest(`/messages?${params}`);
    return {
      messages: response.messages,
      total: response.total
    };
  }
  
  // Transform database contact to frontend format
  private transformContact(dbContact: any): Contact {
    return {
      id: dbContact.id.toString(),
      name: dbContact.name || '',
      phone: dbContact.phone || '',
      requirements: dbContact.notes || '', // Map notes to requirements
      categories: Array.isArray(dbContact.tags) ? dbContact.tags : [],
      state: dbContact.company || '', // Map company to state for now
      city: dbContact.position || '', // Map position to city for now
      date: new Date(dbContact.created_at).toISOString().split('T')[0],
      isFavorite: false,
      isPinned: false,
      isShortlisted: false,
      notes: dbContact.notes || '',
      attachments: [],
      buildType: '',
      kanbanStage: 'lead',
      reminders: [],
      hasActiveReminder: false,
    };
  }
  
  // Transform database group to frontend format
  private transformGroup(dbGroup: any): ContactGroup {
    return {
      id: dbGroup.id.toString(),
      name: dbGroup.name || '',
      description: dbGroup.description || '',
      memberCount: dbGroup.member_count || 0,
      createdAt: new Date(dbGroup.created_at),
      updatedAt: new Date(dbGroup.updated_at || dbGroup.created_at),
    };
  }
  
  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await apiRequest('/health');
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const serverContactService = new ServerContactService();
export default serverContactService; 