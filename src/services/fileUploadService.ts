// File Upload Service for server-side storage
export interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  folder: string;
  uploadDate: string;
}

class FileUploadService {
  private readonly BASE_FOLDER = 'Uploaded';
  private readonly SERVER_URL = 'http://localhost:3001'; // Default backend URL

  // Get current backend URL from localStorage or default
  private getBackendUrl(): string {
    return localStorage.getItem('whatsapp_backend_url') || this.SERVER_URL;
  }

  // Create folder structure based on file type
  private getFileFolder(file: File): string {
    const type = file.type.toLowerCase();
    
    if (type.startsWith('image/')) {
      return `${this.BASE_FOLDER}/Images`;
    } else if (type.startsWith('video/')) {
      return `${this.BASE_FOLDER}/Videos`;
    } else if (type.startsWith('audio/')) {
      return `${this.BASE_FOLDER}/Audio`;
    } else if (type.includes('pdf')) {
      return `${this.BASE_FOLDER}/Documents/PDF`;
    } else if (type.includes('word') || type.includes('doc')) {
      return `${this.BASE_FOLDER}/Documents/Word`;
    } else if (type.includes('excel') || type.includes('sheet')) {
      return `${this.BASE_FOLDER}/Documents/Excel`;
    } else if (type.includes('powerpoint') || type.includes('presentation')) {
      return `${this.BASE_FOLDER}/Documents/PowerPoint`;
    } else if (type.includes('text')) {
      return `${this.BASE_FOLDER}/Documents/Text`;
    } else {
      return `${this.BASE_FOLDER}/Other`;
    }
  }

  // Convert file to base64 for server upload
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix to get just base64
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  // Upload files to server with proper folder organization
  async uploadFiles(files: FileList): Promise<UploadedFile[]> {
    const uploadedFiles: UploadedFile[] = [];

    for (const file of Array.from(files)) {
      try {
        const folderName = this.getFileFolder(file);
        const base64Data = await this.fileToBase64(file);
        
        // Send file to server
        const response = await fetch(`${this.getBackendUrl()}/api/upload-file`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: file.name,
            type: file.type,
            size: file.size,
            data: base64Data,
            folder: folderName
          })
        });

        if (response.ok) {
          const result = await response.json();
          const uploadedFile: UploadedFile = {
            id: result.id || Date.now().toString(),
            name: file.name,
            type: file.type,
            size: file.size,
            url: result.url || `${this.getBackendUrl()}/uploads/${folderName}/${file.name}`,
            folder: folderName,
            uploadDate: new Date().toISOString()
          };
          uploadedFiles.push(uploadedFile);
          console.log('✅ File uploaded to server:', file.name);
        } else {
          // Fallback to local storage if server upload fails
          console.warn('⚠️ Server upload failed, using local storage for:', file.name);
          const localFile = await this.uploadFileLocally(file, folderName);
          uploadedFiles.push(localFile);
        }
      } catch (error) {
        console.error('Failed to upload file:', file.name, error);
        // Fallback to local storage
        try {
          const folderName = this.getFileFolder(file);
          const localFile = await this.uploadFileLocally(file, folderName);
          uploadedFiles.push(localFile);
        } catch (localError) {
          console.error('Local fallback also failed:', localError);
        }
      }
    }
    
    return uploadedFiles;
  }

  // Fallback: Upload file locally using blob URL
  private async uploadFileLocally(file: File, folderName: string): Promise<UploadedFile> {
    const url = URL.createObjectURL(file);
    return {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name: file.name,
      type: file.type,
      size: file.size,
      url: url,
      folder: folderName,
      uploadDate: new Date().toISOString()
    };
  }

  // Get all uploaded files from server
  async getStoredFiles(): Promise<UploadedFile[]> {
    try {
      const response = await fetch(`${this.getBackendUrl()}/api/uploaded-files`);
      if (response.ok) {
        const files = await response.json();
        return files.map((file: any) => ({
          id: file.id,
          name: file.name,
          type: file.type,
          size: file.size,
          url: file.url,
          folder: file.folder,
          uploadDate: file.uploadDate
        }));
      }
    } catch (error) {
      console.error('Error loading files from server:', error);
    }
    
    // Fallback to local storage
    const localFiles = localStorage.getItem('uploadedFiles');
    return localFiles ? JSON.parse(localFiles) : [];
  }

  // Get all uploaded files organized by folder
  async getFilesByFolder(): Promise<Record<string, UploadedFile[]>> {
    const files = await this.getStoredFiles();
    const folderMap: Record<string, UploadedFile[]> = {};

    files.forEach(file => {
      if (!folderMap[file.folder]) {
        folderMap[file.folder] = [];
      }
      folderMap[file.folder].push(file);
    });

    return folderMap;
  }

  // Get files from specific folder
  async getFilesFromFolder(folder: string): Promise<UploadedFile[]> {
    const files = await this.getStoredFiles();
    return files.filter(file => file.folder === folder);
  }

  // Get all folders
  async getAllFolders(): Promise<string[]> {
    const files = await this.getStoredFiles();
    const folders = new Set<string>();
    
    files.forEach(file => {
      folders.add(file.folder);
    });

    return Array.from(folders).sort();
  }

  // Delete file from server
  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.getBackendUrl()}/api/delete-file/${fileId}`, {
        method: 'DELETE'
      });
      return response.ok;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  // Clear all files
  async clearAllFiles(): Promise<void> {
    try {
      await fetch(`${this.getBackendUrl()}/api/clear-files`, {
        method: 'DELETE'
      });
    } catch (error) {
      console.error('Error clearing files:', error);
    }
  }

  // Get file by ID
  async getFileById(fileId: string): Promise<UploadedFile | null> {
    const files = await this.getStoredFiles();
    return files.find(f => f.id === fileId) || null;
  }

  // Search files
  async searchFiles(query: string): Promise<UploadedFile[]> {
    const files = await this.getStoredFiles();
    const lowercaseQuery = query.toLowerCase();
    
    return files.filter(file =>
      file.name.toLowerCase().includes(lowercaseQuery) ||
      file.folder.toLowerCase().includes(lowercaseQuery) ||
      file.type.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Get all files
  async getAllFiles(): Promise<UploadedFile[]> {
    return await this.getStoredFiles();
  }
}

export const fileUploadService = new FileUploadService();
export default fileUploadService; 