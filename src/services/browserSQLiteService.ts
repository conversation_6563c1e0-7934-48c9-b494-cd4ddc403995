import initSqlJs from 'sql.js';

interface Contact {
  id?: number;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  position?: string;
  notes?: string;
  tags?: string;
  created_at?: string;
  updated_at?: string;
}

interface MediaFile {
  id?: number;
  folder_id: number;
  filename: string;
  original_name: string;
  file_data: string; // Base64 encoded file data
  file_size: number;
  mime_type: string;
  created_at?: string;
}

interface MediaFolder {
  id?: number;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

interface ContactGroup {
  id?: number;
  name: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

interface SentMessage {
  id?: number;
  contact_id?: number;
  phone: string;
  contact_name?: string;
  message_text?: string;
  attachments?: string;
  links?: string;
  status: string;
  error_message?: string;
  sent_at?: string;
}

class BrowserSQLiteService {
  private db: any = null;
  private SQL: any = null;
  private initialized = false;
  private readonly DB_NAME = 'ContactSphereDB';
  private readonly DB_VERSION = 1;
  private readonly STORE_NAME = 'database';

  // IndexedDB operations for persistent storage
  private async openIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          db.createObjectStore(this.STORE_NAME);
        }
      };
    });
  }

  private async loadDatabaseFromIndexedDB(): Promise<Uint8Array | null> {
    try {
      const db = await this.openIndexedDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.STORE_NAME], 'readonly');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.get('sqliteDatabase');
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          if (request.result) {
            resolve(new Uint8Array(request.result));
          } else {
            resolve(null);
          }
        };
      });
    } catch (error) {
      console.error('❌ Error loading database from IndexedDB:', error);
      return null;
    }
  }

  private async saveDatabaseToIndexedDB(data: Uint8Array): Promise<void> {
    try {
      const db = await this.openIndexedDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.STORE_NAME], 'readwrite');
        const store = transaction.objectStore(this.STORE_NAME);
        const request = store.put(Array.from(data), 'sqliteDatabase');
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.error('❌ Error saving database to IndexedDB:', error);
      throw error;
    }
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize SQL.js
      this.SQL = await initSqlJs({
        locateFile: (file: string) => `https://sql.js.org/dist/${file}`
      });

      // Try to load existing database from IndexedDB
      const savedDb = await this.loadDatabaseFromIndexedDB();
      
      if (savedDb) {
        // Load existing database
        this.db = new this.SQL.Database(savedDb);
        console.log('✅ Loaded existing database from IndexedDB (persistent storage)');
      } else {
        // Create new database
        this.db = new this.SQL.Database();
        this.createTables();
        await this.saveDatabase();
        console.log('✅ Created new database in IndexedDB (persistent storage)');
      }

      this.initialized = true;
    } catch (error) {
      console.error('❌ Error initializing browser SQLite:', error);
      throw error;
    }
  }

  private createTables() {
    const tables = [
      // Contacts table
      `CREATE TABLE IF NOT EXISTS contacts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phone TEXT UNIQUE NOT NULL,
        email TEXT,
        company TEXT,
        position TEXT,
        notes TEXT,
        tags TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contact groups table
      `CREATE TABLE IF NOT EXISTS contact_groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contact group members table
      `CREATE TABLE IF NOT EXISTS contact_group_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contact_id INTEGER NOT NULL,
        group_id INTEGER NOT NULL,
        added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE CASCADE,
        FOREIGN KEY (group_id) REFERENCES contact_groups (id) ON DELETE CASCADE,
        UNIQUE(contact_id, group_id)
      )`,

      // Media folders table
      `CREATE TABLE IF NOT EXISTS media_folders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Media files table (stores files as base64 in browser)
      `CREATE TABLE IF NOT EXISTS media_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        folder_id INTEGER NOT NULL,
        filename TEXT NOT NULL,
        original_name TEXT NOT NULL,
        file_data TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (folder_id) REFERENCES media_folders (id) ON DELETE CASCADE
      )`,

      // Sent messages table
      `CREATE TABLE IF NOT EXISTS sent_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        contact_id INTEGER,
        phone TEXT NOT NULL,
        contact_name TEXT,
        message_text TEXT,
        attachments TEXT,
        links TEXT,
        status TEXT DEFAULT 'pending',
        error_message TEXT,
        sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (contact_id) REFERENCES contacts (id) ON DELETE SET NULL
      )`
    ];

    // Execute each table creation
    tables.forEach(sql => {
      this.db.exec(sql);
    });

    // Insert default data
    this.insertDefaultData();
  }

  private insertDefaultData() {
    // Insert default media folder
    this.db.run(
      'INSERT OR IGNORE INTO media_folders (name, description) VALUES (?, ?)',
      ['Default', 'Default folder for uploaded media files']
    );

    // Insert sample contact groups
    const groups = [
      ['Customers', 'Customer contacts'],
      ['Suppliers', 'Supplier contacts'],
      ['Team', 'Team members'],
      ['VIP', 'VIP contacts']
    ];

    groups.forEach(([name, description]) => {
      this.db.run(
        'INSERT OR IGNORE INTO contact_groups (name, description) VALUES (?, ?)',
        [name, description]
      );
    });
  }

  private async saveDatabase() {
    try {
      const data = this.db.export();
      await this.saveDatabaseToIndexedDB(data);
      console.log('💾 Database saved to IndexedDB (persistent storage)');
    } catch (error) {
      console.error('❌ Error saving database:', error);
    }
  }

  // Contact methods
  async getAllContacts(): Promise<Contact[]> {
    await this.initialize();
    const stmt = this.db.prepare('SELECT * FROM contacts ORDER BY name');
    const contacts: Contact[] = [];
    
    while (stmt.step()) {
      contacts.push(stmt.getAsObject() as Contact);
    }
    
    stmt.free();
    return contacts;
  }

  async addContact(contact: Omit<Contact, 'id'>): Promise<Contact> {
    await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT INTO contacts (name, phone, email, company, position, notes, tags)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run([
      contact.name,
      contact.phone,
      contact.email || null,
      contact.company || null,
      contact.position || null,
      contact.notes || null,
      contact.tags || null
    ]);
    
    const id = this.db.exec('SELECT last_insert_rowid()')[0].values[0][0];
    stmt.free();
    
    await this.saveDatabase();
    
    // Return the created contact
    const newContact = this.db.prepare('SELECT * FROM contacts WHERE id = ?');
    newContact.bind([id]);
    newContact.step();
    const result = newContact.getAsObject() as Contact;
    newContact.free();
    
    return result;
  }

  async updateContact(id: number, contact: Partial<Contact>): Promise<Contact | null> {
    await this.initialize();
    
    const updates: string[] = [];
    const values: any[] = [];
    
    if (contact.name !== undefined) {
      updates.push('name = ?');
      values.push(contact.name);
    }
    if (contact.phone !== undefined) {
      updates.push('phone = ?');
      values.push(contact.phone);
    }
    if (contact.email !== undefined) {
      updates.push('email = ?');
      values.push(contact.email);
    }
    if (contact.company !== undefined) {
      updates.push('company = ?');
      values.push(contact.company);
    }
    if (contact.position !== undefined) {
      updates.push('position = ?');
      values.push(contact.position);
    }
    if (contact.notes !== undefined) {
      updates.push('notes = ?');
      values.push(contact.notes);
    }
    if (contact.tags !== undefined) {
      updates.push('tags = ?');
      values.push(contact.tags);
    }
    
    if (updates.length === 0) return null;
    
    updates.push('updated_at = CURRENT_TIMESTAMP');
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE contacts SET ${updates.join(', ')} WHERE id = ?
    `);
    
    stmt.run(values);
    stmt.free();
    
    await this.saveDatabase();
    
    // Return updated contact
    const updatedContact = this.db.prepare('SELECT * FROM contacts WHERE id = ?');
    updatedContact.bind([id]);
    updatedContact.step();
    const result = updatedContact.getAsObject() as Contact;
    updatedContact.free();
    
    return result;
  }

  async deleteContact(id: number): Promise<boolean> {
    await this.initialize();
    
    const stmt = this.db.prepare('DELETE FROM contacts WHERE id = ?');
    const info = stmt.run([id]);
    const changes = info.changes || 0;
    
    stmt.free();
    
    await this.saveDatabase();
    return changes > 0;
  }

  // Media folder methods
  async getMediaFolders(): Promise<MediaFolder[]> {
    await this.initialize();
    const stmt = this.db.prepare('SELECT * FROM media_folders ORDER BY name');
    const folders: MediaFolder[] = [];
    
    while (stmt.step()) {
      folders.push(stmt.getAsObject() as MediaFolder);
    }
    
    stmt.free();
    return folders;
  }

  async addMediaFolder(folder: Omit<MediaFolder, 'id'>): Promise<MediaFolder> {
    await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT INTO media_folders (name, description)
      VALUES (?, ?)
    `);
    
    stmt.run([folder.name, folder.description || null]);
    
    const id = this.db.exec('SELECT last_insert_rowid()')[0].values[0][0];
    stmt.free();
    
    await this.saveDatabase();
    
    // Return the created folder
    const newFolder = this.db.prepare('SELECT * FROM media_folders WHERE id = ?');
    newFolder.bind([id]);
    newFolder.step();
    const result = newFolder.getAsObject() as MediaFolder;
    newFolder.free();
    
    return result;
  }

  // Media file methods
  async getMediaFiles(folderId?: number): Promise<MediaFile[]> {
    await this.initialize();
    
    let sql = `
      SELECT mf.*, fold.name as folder_name 
      FROM media_files mf 
      JOIN media_folders fold ON mf.folder_id = fold.id
    `;
    
    const params: any[] = [];
    if (folderId) {
      sql += ' WHERE mf.folder_id = ?';
      params.push(folderId);
    }
    
    sql += ' ORDER BY mf.created_at DESC';
    
    const stmt = this.db.prepare(sql);
    if (params.length > 0) {
      stmt.bind(params);
    }
    
    const files: MediaFile[] = [];
    while (stmt.step()) {
      files.push(stmt.getAsObject() as MediaFile);
    }
    
    stmt.free();
    return files;
  }

  async addMediaFile(file: File, folderId: number): Promise<MediaFile> {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = async () => {
        try {
          const base64Data = (reader.result as string).split(',')[1];
          
          const stmt = this.db.prepare(`
            INSERT INTO media_files (folder_id, filename, original_name, file_data, file_size, mime_type)
            VALUES (?, ?, ?, ?, ?, ?)
          `);
          
          const filename = `${Date.now()}_${file.name}`;
          
          stmt.run([
            folderId,
            filename,
            file.name,
            base64Data,
            file.size,
            file.type
          ]);
          
          const id = this.db.exec('SELECT last_insert_rowid()')[0].values[0][0];
          stmt.free();
          
          await this.saveDatabase();
          
          // Return the created file
          const newFile = this.db.prepare('SELECT * FROM media_files WHERE id = ?');
          newFile.bind([id]);
          newFile.step();
          const result = newFile.getAsObject() as MediaFile;
          newFile.free();
          
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(reader.error);
      reader.readAsDataURL(file);
    });
  }

  // Contact groups methods
  async getContactGroups(): Promise<ContactGroup[]> {
    await this.initialize();
    const stmt = this.db.prepare('SELECT * FROM contact_groups ORDER BY name');
    const groups: ContactGroup[] = [];
    
    while (stmt.step()) {
      groups.push(stmt.getAsObject() as ContactGroup);
    }
    
    stmt.free();
    return groups;
  }

  async addContactGroup(group: Omit<ContactGroup, 'id'>): Promise<ContactGroup> {
    await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT INTO contact_groups (name, description)
      VALUES (?, ?)
    `);
    
    stmt.run([group.name, group.description || null]);
    
    const id = this.db.exec('SELECT last_insert_rowid()')[0].values[0][0];
    stmt.free();
    
    await this.saveDatabase();
    
    // Return the created group
    const newGroup = this.db.prepare('SELECT * FROM contact_groups WHERE id = ?');
    newGroup.bind([id]);
    newGroup.step();
    const result = newGroup.getAsObject() as ContactGroup;
    newGroup.free();
    
    return result;
  }

  // Sent messages methods
  async addSentMessage(message: Omit<SentMessage, 'id'>): Promise<SentMessage> {
    await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT INTO sent_messages (contact_id, phone, contact_name, message_text, attachments, links, status, error_message)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run([
      message.contact_id || null,
      message.phone,
      message.contact_name || null,
      message.message_text || null,
      message.attachments || null,
      message.links || null,
      message.status,
      message.error_message || null
    ]);
    
    const id = this.db.exec('SELECT last_insert_rowid()')[0].values[0][0];
    stmt.free();
    
    await this.saveDatabase();
    
    // Return the created message
    const newMessage = this.db.prepare('SELECT * FROM sent_messages WHERE id = ?');
    newMessage.bind([id]);
    newMessage.step();
    const result = newMessage.getAsObject() as SentMessage;
    newMessage.free();
    
    return result;
  }

  async getSentMessages(): Promise<SentMessage[]> {
    await this.initialize();
    const stmt = this.db.prepare('SELECT * FROM sent_messages ORDER BY sent_at DESC');
    const messages: SentMessage[] = [];
    
    while (stmt.step()) {
      messages.push(stmt.getAsObject() as SentMessage);
    }
    
    stmt.free();
    return messages;
  }

  async clearSentMessages(): Promise<void> {
    await this.initialize();
    this.db.run('DELETE FROM sent_messages');
    await this.saveDatabase();
  }

  // Export/Import methods for backup
  exportDatabase(): string {
    if (!this.db) return '';
    const data = this.db.export();
    return btoa(String.fromCharCode(...data));
  }

  async importDatabase(base64Data: string): Promise<void> {
    try {
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      if (this.db) {
        this.db.close();
      }
      
      this.db = new this.SQL.Database(bytes);
      await this.saveDatabase();
      console.log('✅ Database imported successfully');
    } catch (error) {
      console.error('❌ Error importing database:', error);
      throw error;
    }
  }

  // Get storage info
  getStorageInfo() {
    const dbData = localStorage.getItem('contacts_database');
    const dbSize = dbData ? new Blob([dbData]).size : 0;
    
    return {
      databaseSize: dbSize,
      databaseSizeFormatted: this.formatBytes(dbSize),
      storageUsed: this.getLocalStorageSize(),
      storageUsedFormatted: this.formatBytes(this.getLocalStorageSize()),
      maxStorage: '5-10 MB (browser limit)',
      isInitialized: this.initialized
    };
  }

  private getLocalStorageSize(): number {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export singleton instance
export const browserSQLiteService = new BrowserSQLiteService(); 