// Environment configuration for Contact Sphere Organizer
// Supports dynamic backend URL switching for local development

interface EnvironmentConfig {
  apiBaseUrl: string;
  isDevelopment: boolean;
  isProduction: boolean;
  environment: string;
}

// Detect environment using Vite's import.meta.env
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

// Backend URL configuration
const getBackendUrl = (): string => {
  // Priority order:
  // 1. Stored backend URL from localStorage (for user preference)
  // 2. Environment variable (for manual override)
  // 3. Default local development URL
  
  // Check for stored backend URL
  const storedBackendUrl = localStorage.getItem('backendUrl');
  if (storedBackendUrl) {
    console.log('🔧 Using stored backend URL:', storedBackendUrl);
    return storedBackendUrl;
  }
  
  // Check for environment variable override
  if (import.meta.env.VITE_API_URL) {
    console.log('🔧 Using VITE_API_URL from environment:', import.meta.env.VITE_API_URL);
    return import.meta.env.VITE_API_URL;
  }
  
  // Default to local development URL
  console.log('🔧 Using default development URL (localhost)');
  return 'http://localhost:3001';
};

// Create configuration object
const config: EnvironmentConfig = {
  apiBaseUrl: getBackendUrl(),
  isDevelopment,
  isProduction,
  environment: isProduction ? 'production' : 'development'
};

// Log configuration (only in development)
if (isDevelopment) {
  console.log('🔧 Environment Configuration:', {
    apiBaseUrl: config.apiBaseUrl,
    environment: config.environment,
    isDevelopment: config.isDevelopment,
    isProduction: config.isProduction,
    VITE_API_URL: import.meta.env.VITE_API_URL,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD
  });
}

export default config;

// Export individual values for convenience
export const { apiBaseUrl, isDevelopment: isDev, isProduction: isProd, environment } = config; 