// API Configuration for Frontend (Hosted) + Backend (Local PC)
export const API_CONFIG = {
  // Default backend URL - user can change this in the app
  DEFAULT_BACKEND_URL: 'http://*************:3001',
  
  // API Endpoints (relative to backend URL)
  ENDPOINTS: {
    // WhatsApp endpoints
    WHATSAPP: {
      STATUS: '/api/status',
      QR: '/api/whatsapp/qr',
      CONNECT: '/api/connect',
      DISCONNECT: '/api/disconnect',
      CLEAR_SESSION: '/api/clear-session',
      SEND_MESSAGE: '/api/send-message',
      SEND_BULK_MESSAGES: '/api/send-bulk-messages',
      REFRESH_DEVICE_INFO: '/api/refresh-device-info',
    },
    
    // File upload endpoints
    UPLOAD: {
      FILES: '/api/upload',
      DELETE: '/api/upload/delete',
    },
    
    // Health check
    HEALTH: '/api/health',
  },
  
  // Request timeouts (in milliseconds)
  TIMEOUTS: {
    DEFAULT: 30000, // 30 seconds
    UPLOAD: 120000, // 2 minutes
    WHATSAPP_SEND: 60000, // 1 minute
    CONNECTION_TEST: 10000, // 10 seconds for testing connection
  },
  
  // File upload configuration
  UPLOAD: {
    MAX_FILE_SIZE: 16 * 1024 * 1024, // 16MB
    ALLOWED_TYPES: ['jpeg', 'jpg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'mp4', 'avi', 'mov', 'mp3', 'wav'],
    MAX_FILES: 10,
  },
};

// Get backend URL from localStorage or use default
export const getBackendUrl = (): string => {
  const savedUrl = localStorage.getItem('backend_url');
  return savedUrl || API_CONFIG.DEFAULT_BACKEND_URL;
};

// Set backend URL in localStorage
export const setBackendUrl = (url: string): void => {
  // Clean the URL (remove trailing slash)
  const cleanUrl = url.replace(/\/$/, '');
  localStorage.setItem('backend_url', cleanUrl);
};

// Helper function to get full API URL
export const getApiUrl = (endpoint: string): string => {
  const backendUrl = getBackendUrl();
  return `${backendUrl}${endpoint}`;
};

// Test if backend URL is reachable
export const testBackendConnection = async (url: string): Promise<boolean> => {
  try {
    const cleanUrl = url.replace(/\/$/, '');
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUTS.CONNECTION_TEST);
    
    const response = await fetch(`${cleanUrl}/api/health`, {
      method: 'GET',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('Backend connection test failed:', error);
    return false;
  }
};

// Environment detection
export const isProduction = (): boolean => {
  return import.meta.env.MODE === 'production';
};

export const isDevelopment = (): boolean => {
  return import.meta.env.MODE === 'development';
};

// App information
export const APP_INFO = {
  NAME: 'Contact Sphere Organizer',
  VERSION: '1.0.0',
  DESCRIPTION: 'A comprehensive contact management and WhatsApp messaging solution',
};

// Common backend URLs for easy selection
export const COMMON_BACKEND_URLS = [
  'http://localhost:3001',
  'http://*************:3001', // Your actual PC IP
  'http://*************:3001', // Example local network IP
  'http://*************:3001',
  'http://**********:3001',
];

// Helper to get local network IP suggestions
export const getLocalNetworkSuggestions = (): string[] => {
  return [
    'http://localhost:3001',
    'http://127.0.0.1:3001',
    'http://192.168.1.XXX:3001', // User needs to replace XXX with their IP
    'http://192.168.0.XXX:3001',
    'http://10.0.0.XXX:3001',
  ];
};

// Validate URL format
export const isValidUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}; 