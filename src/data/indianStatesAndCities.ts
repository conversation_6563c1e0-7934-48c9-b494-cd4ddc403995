// Comprehensive Indian States and Cities Data
export interface StateData {
  state: string;
  cities: string[];
}

export const indianStatesAndCities: StateData[] = [
  {
    state: "Andhra Pradesh",
    cities: [
      "Visakhapatnam", "Vijayawada", "Guntur", "Nellore", "Kurnool", "Rajahmundry", 
      "Tirupati", "Kadapa", "Anantapur", "Vizianagaram", "Eluru", "Ongole", 
      "Chittoor", "Machilipatnam", "Adoni", "Tenali", "Proddatur", "Hindupur",
      "Bhimavaram", "Madanapalle", "Guntakal", "Dharmavaram", "Gudivada", "Narasaraopet"
    ]
  },
  {
    state: "Arunachal Pradesh",
    cities: [
      "Itanagar", "Naharlagun", "Pasighat", "Tezpur", "Ziro", "Bomdila", 
      "Tawang", "Changlang", "Tezu", "Khonsa", "Seppa", "Yingkiong"
    ]
  },
  {
    state: "Assam",
    cities: [
      "Guwahati", "Silchar", "Dibrugarh", "Jorhat", "Nagaon", "Tinsukia", 
      "Tezpur", "Bongaigaon", "Karimganj", "Sivasagar", "Goalpara", "Barpeta",
      "North Lakhimpur", "Mangaldoi", "Haflong", "Diphu", "Golaghat", "Kokrajhar"
    ]
  },
  {
    state: "Bihar",
    cities: [
      "Patna", "Gaya", "Bhagalpur", "Muzaffarpur", "Purnia", "Darbhanga", 
      "Bihar Sharif", "Arrah", "Begusarai", "Katihar", "Munger", "Chhapra",
      "Sasaram", "Hajipur", "Dehri", "Siwan", "Motihari", "Nawada", 
      "Bagaha", "Buxar", "Kishanganj", "Sitamarhi", "Jamalpur", "Jehanabad"
    ]
  },
  {
    state: "Chhattisgarh",
    cities: [
      "Raipur", "Bhilai", "Korba", "Bilaspur", "Durg", "Rajnandgaon", 
      "Jagdalpur", "Raigarh", "Ambikapur", "Mahasamund", "Dhamtari", "Kanker",
      "Bastar", "Jashpur", "Sukma", "Kondagaon"
    ]
  },
  {
    state: "Goa",
    cities: [
      "Panaji", "Vasco da Gama", "Margao", "Mapusa", "Ponda", "Bicholim", 
      "Curchorem", "Sanquelim", "Cuncolim", "Quepem"
    ]
  },
  {
    state: "Gujarat",
    cities: [
      "Ahmedabad", "Surat", "Vadodara", "Rajkot", "Bhavnagar", "Jamnagar", 
      "Junagadh", "Gandhinagar", "Anand", "Navsari", "Morbi", "Nadiad",
      "Surendranagar", "Bharuch", "Mehsana", "Bhuj", "Porbandar", "Palanpur",
      "Valsad", "Vapi", "Gondal", "Veraval", "Godhra", "Patan", "Kalol",
      "Dahod", "Botad", "Amreli", "Deesa", "Jetpur"
    ]
  },
  {
    state: "Haryana",
    cities: [
      "Faridabad", "Gurgaon", "Panipat", "Ambala", "Yamunanagar", "Rohtak", 
      "Hisar", "Karnal", "Sonipat", "Panchkula", "Bhiwani", "Sirsa",
      "Bahadurgarh", "Jind", "Thanesar", "Kaithal", "Rewari", "Narnaul",
      "Pundri", "Kosli", "Palwal", "Hansi"
    ]
  },
  {
    state: "Himachal Pradesh",
    cities: [
      "Shimla", "Dharamshala", "Solan", "Mandi", "Palampur", "Baddi", 
      "Nahan", "Paonta Sahib", "Sundarnagar", "Chamba", "Una", "Kullu",
      "Hamirpur", "Bilaspur", "Yol", "Jubbal", "Chail", "Kasauli"
    ]
  },
  {
    state: "Jharkhand",
    cities: [
      "Ranchi", "Jamshedpur", "Dhanbad", "Bokaro", "Deoghar", "Phusro", 
      "Hazaribagh", "Giridih", "Ramgarh", "Medininagar", "Chirkunda", "Pakaur",
      "Chaibasa", "Dumka", "Sahibganj", "Gumla", "Chatra", "Godda"
    ]
  },
  {
    state: "Karnataka",
    cities: [
      "Bangalore", "Mysore", "Hubli", "Mangalore", "Belgaum", "Gulbarga", 
      "Davanagere", "Bellary", "Bijapur", "Shimoga", "Tumkur", "Raichur",
      "Bidar", "Hospet", "Hassan", "Gadag", "Udupi", "Robertsonpet",
      "Bhadravati", "Chitradurga", "Kolar", "Mandya", "Chikmagalur", "Gangavati",
      "Bagalkot", "Ranebennuru", "Karwar", "Yadgir", "Koppal", "Haveri"
    ]
  },
  {
    state: "Kerala",
    cities: [
      "Thiruvananthapuram", "Kochi", "Kozhikode", "Kollam", "Thrissur", "Alappuzha", 
      "Palakkad", "Kannur", "Kasaragod", "Kottayam", "Malappuram", "Pathanamthitta",
      "Idukki", "Ernakulam", "Wayanad", "Nedumangad", "Kayamkulam", "Mattancherry",
      "Punalur", "Nilambur", "Cherthala", "Perinthalmanna", "Chalakudy", "Payyannur"
    ]
  },
  {
    state: "Madhya Pradesh",
    cities: [
      "Bhopal", "Indore", "Gwalior", "Jabalpur", "Ujjain", "Sagar", 
      "Dewas", "Satna", "Ratlam", "Rewa", "Murwara", "Singrauli",
      "Burhanpur", "Khandwa", "Bhind", "Chhindwara", "Guna", "Shivpuri",
      "Vidisha", "Chhatarpur", "Damoh", "Mandsaur", "Khargone", "Neemuch",
      "Pithampur", "Narmadapuram", "Itarsi", "Sehore", "Betul", "Seoni"
    ]
  },
  {
    state: "Maharashtra",
    cities: [
      "Mumbai", "Pune", "Nagpur", "Thane", "Nashik", "Aurangabad", 
      "Solapur", "Amravati", "Kolhapur", "Sangli", "Malegaon", "Jalgaon",
      "Akola", "Latur", "Dhule", "Ahmednagar", "Chandrapur", "Parbhani",
      "Ichalkaranji", "Jalna", "Ambarnath", "Bhusawal", "Panvel", "Badlapur",
      "Beed", "Gondia", "Satara", "Barshi", "Yavatmal", "Achalpur",
      "Osmanabad", "Nandurbar", "Wardha", "Udgir", "Hinganghat"
    ]
  },
  {
    state: "Manipur",
    cities: [
      "Imphal", "Thoubal", "Lilong", "Mayang Imphal", "Kakching", "Bishnupur",
      "Churachandpur", "Senapati", "Ukhrul", "Tamenglong", "Jiribam", "Moreh"
    ]
  },
  {
    state: "Meghalaya",
    cities: [
      "Shillong", "Tura", "Nongstoin", "Jowai", "Baghmara", "Williamnagar",
      "Nongpoh", "Mairang", "Resubelpara"
    ]
  },
  {
    state: "Mizoram",
    cities: [
      "Aizawl", "Lunglei", "Saiha", "Champhai", "Kolasib", "Serchhip",
      "Mamit", "Lawngtlai", "Bairabi"
    ]
  },
  {
    state: "Nagaland",
    cities: [
      "Kohima", "Dimapur", "Mokokchung", "Tuensang", "Wokha", "Zunheboto",
      "Phek", "Kiphire", "Longleng", "Peren", "Mon"
    ]
  },
  {
    state: "Odisha",
    cities: [
      "Bhubaneswar", "Cuttack", "Rourkela", "Brahmapur", "Sambalpur", "Puri", 
      "Balasore", "Bhadrak", "Baripada", "Jharsuguda", "Jeypore", "Barbil",
      "Khordha", "Balangir", "Rayagada", "Bhawanipatna", "Dhenkanal", "Kendujhar",
      "Sunabeda", "Rajgangpur", "Paradip", "Kendrapara"
    ]
  },
  {
    state: "Punjab",
    cities: [
      "Ludhiana", "Amritsar", "Jalandhar", "Patiala", "Bathinda", "Mohali", 
      "Firozpur", "Batala", "Pathankot", "Moga", "Abohar", "Malerkotla",
      "Khanna", "Phagwara", "Muktsar", "Barnala", "Rajpura", "Hoshiarpur",
      "Kapurthala", "Faridkot", "Sunam", "Sangrur", "Fazilka", "Gurdaspur",
      "Kharar", "Gobindgarh", "Mansa", "Malout", "Nabha", "Tarn Taran"
    ]
  },
  {
    state: "Rajasthan",
    cities: [
      "Jaipur", "Jodhpur", "Kota", "Bikaner", "Ajmer", "Udaipur", 
      "Bhilwara", "Alwar", "Bharatpur", "Sikar", "Pali", "Sri Ganganagar",
      "Kishangarh", "Baran", "Dhaulpur", "Tonk", "Beawar", "Hanumangarh",
      "Gangapur City", "Banswara", "Makrana", "Sujangarh", "Sardarshahar", "Ladnu",
      "Deshnok", "Didwana", "Karauli", "Nokha", "Rawatbhata", "Mandawa"
    ]
  },
  {
    state: "Sikkim",
    cities: [
      "Gangtok", "Namchi", "Geyzing", "Mangan", "Jorethang", "Nayabazar",
      "Rangpo", "Singtam", "Pakyong"
    ]
  },
  {
    state: "Tamil Nadu",
    cities: [
      "Chennai", "Coimbatore", "Madurai", "Tiruchirappalli", "Salem", "Tirunelveli", 
      "Tiruppur", "Vellore", "Erode", "Thoothukkudi", "Dindigul", "Thanjavur",
      "Ranipet", "Sivakasi", "Karur", "Udhagamandalam", "Hosur", "Nagercoil",
      "Kanchipuram", "Kumarakonam", "Karaikkudi", "Neyveli", "Cuddalore", "Kumbakonam",
      "Tiruvannamalai", "Pollachi", "Rajapalayam", "Gudiyatham", "Vaniyambadi", "Ambur"
    ]
  },
  {
    state: "Telangana",
    cities: [
      "Hyderabad", "Warangal", "Nizamabad", "Khammam", "Karimnagar", "Ramagundam", 
      "Mahabubnagar", "Nalgonda", "Adilabad", "Suryapet", "Miryalaguda", "Jagtial",
      "Mancherial", "Nirmal", "Kothagudem", "Bodhan", "Sangareddy", "Metpally",
      "Zahirabad", "Medak", "Kamareddy", "Vikarabad", "Jangaon", "Mandamarri"
    ]
  },
  {
    state: "Tripura",
    cities: [
      "Agartala", "Dharmanagar", "Udaipur", "Kailasahar", "Belonia", "Khowai",
      "Pratapgarh", "Ranirbazar", "Kumarghat", "Sonamura", "Panisagar", "Amarpur"
    ]
  },
  {
    state: "Uttar Pradesh",
    cities: [
      "Lucknow", "Kanpur", "Ghaziabad", "Agra", "Varanasi", "Meerut", 
      "Allahabad", "Bareilly", "Aligarh", "Moradabad", "Saharanpur", "Gorakhpur",
      "Noida", "Firozabad", "Jhansi", "Muzaffarnagar", "Mathura", "Rampur",
      "Shahjahanpur", "Farrukhabad", "Mau", "Hapur", "Etawah", "Mirzapur",
      "Bulandshahr", "Sambhal", "Amroha", "Hardoi", "Fatehpur", "Raebareli",
      "Orai", "Sitapur", "Bahraich", "Modinagar", "Unnao", "Jaunpur",
      "Lakhimpur", "Hathras", "Banda", "Pilibhit", "Barabanki", "Khurja",
      "Gonda", "Mainpuri", "Lalitpur", "Etah", "Deoria", "Ujhani", "Ghazipur",
      "Sultanpur", "Azamgarh", "Bijnor", "Sahaswan", "Basti", "Chandausi",
      "Akbarpur", "Ballia", "Tanda", "Greater Noida", "Shikohabad", "Jasrana",
      "Kasganj", "Kannauj", "Kirakat", "Sherkot", "Vrindavan", "Najibabad"
    ]
  },
  {
    state: "Uttarakhand",
    cities: [
      "Dehradun", "Haridwar", "Roorkee", "Haldwani", "Rudrapur", "Kashipur", 
      "Rishikesh", "Ramnagar", "Pithoragarh", "Jaspur", "Kichha", "Sitarganj",
      "Bageshwar", "Almora", "Pauri", "Kotdwar", "Nainital", "Mussoorie",
      "Tehri", "Lansdowne", "Srinagar", "Gopeshwar", "Joshimath", "Chamoli"
    ]
  },
  {
    state: "West Bengal",
    cities: [
      "Kolkata", "Howrah", "Durgapur", "Asansol", "Siliguri", "Malda", 
      "Bardhaman", "Baharampur", "Habra", "Kharagpur", "Shantipur", "Dankuni",
      "Dhulian", "Ranaghat", "Haldia", "Raiganj", "Krishnanagar", "Nabadwip",
      "Medinipur", "Jalpaiguri", "Balurghat", "Basirhat", "Bankura", "Chakdaha",
      "Darjeeling", "Alipurduar", "Purulia", "Jangipur", "Bangaon", "Cooch Behar"
    ]
  },
  // Union Territories
  {
    state: "Andaman and Nicobar Islands",
    cities: [
      "Port Blair", "Bamboo Flat", "Garacharma", "Diglipur", "Rangat",
      "Mayabunder", "Campbell Bay", "Car Nicobar", "Hut Bay"
    ]
  },
  {
    state: "Chandigarh",
    cities: ["Chandigarh"]
  },
  {
    state: "Dadra and Nagar Haveli and Daman and Diu",
    cities: [
      "Daman", "Diu", "Silvassa", "Naroli", "Dadra", "Khanvel", "Samarvarni"
    ]
  },
  {
    state: "Delhi",
    cities: [
      "New Delhi", "Delhi", "North Delhi", "South Delhi", "East Delhi", "West Delhi",
      "Central Delhi", "North East Delhi", "North West Delhi", "South East Delhi",
      "South West Delhi", "Shahdara", "Dwarka"
    ]
  },
  {
    state: "Jammu and Kashmir",
    cities: [
      "Srinagar", "Jammu", "Baramulla", "Anantnag", "Sopore", "KathuaKupwara", 
      "Punch", "Rajouri", "Udhampur", "Doda", "Kishtwar", "Ramban",
      "Reasi", "Samba", "Bandipora", "Ganderbal", "Kulgam", "Pulwama", "Shopian"
    ]
  },
  {
    state: "Ladakh",
    cities: [
      "Leh", "Kargil", "Nubra", "Zanskar", "Drass", "Sankoo", "Turtuk", "Diskit"
    ]
  },
  {
    state: "Lakshadweep",
    cities: [
      "Kavaratti", "Agatti", "Minicoy", "Amini", "Andrott", "Kalpeni",
      "Kadmat", "Kiltan", "Chetlat", "Bitra"
    ]
  },
  {
    state: "Puducherry",
    cities: [
      "Puducherry", "Karaikal", "Mahe", "Yanam", "Villianur", "Ariyankuppam",
      "Mannadipet", "Bahour"
    ]
  }
];

// Helper functions
export const getAllStates = (): string[] => {
  return indianStatesAndCities.map(item => item.state).sort();
};

export const getCitiesByState = (state: string): string[] => {
  const stateData = indianStatesAndCities.find(item => item.state === state);
  return stateData ? stateData.cities.sort() : [];
};

export const getAllCities = (): string[] => {
  const allCities: string[] = [];
  indianStatesAndCities.forEach(item => {
    allCities.push(...item.cities);
  });
  return [...new Set(allCities)].sort();
}; 