import { Contact } from '@/types/Contact';

export interface WhatsAppMessageHistory {
  id: string;
  contactId: string;
  contactName: string;
  contactPhone: string;
  contactCity: string;
  contactState: string;
  messages: WhatsAppMessage[];
  totalMessages: number;
  lastMessageDate: string;
  firstMessageDate: string;
  attachments: MessageAttachment[];
  links: string[];
  status: 'active' | 'inactive';
}

export interface WhatsAppMessage {
  id: string;
  text: string;
  attachments: MessageAttachment[];
  links: string[];
  sentAt: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  retryCount: number;
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'audio';
  url: string;
  size: number;
  mimeType: string;
}

// Mock data for WhatsApp message history
export const mockWhatsAppHistory: WhatsAppMessageHistory[] = [];

// Helper functions for filtering and sorting
export const filterWhatsAppHistory = (
  history: WhatsAppMessageHistory[],
  filters: {
    dateRange?: { start: string; end: string };
    city?: string;
    state?: string;
    minMessages?: number;
    maxMessages?: number;
    status?: 'active' | 'inactive';
    hasAttachments?: boolean;
    hasLinks?: boolean;
  }
) => {
  return history.filter(item => {
    // Date range filter
    if (filters.dateRange) {
      const lastMessageDate = new Date(item.lastMessageDate);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      if (lastMessageDate < startDate || lastMessageDate > endDate) {
        return false;
      }
    }

    // City filter
    if (filters.city && item.contactCity.toLowerCase() !== filters.city.toLowerCase()) {
      return false;
    }

    // State filter
    if (filters.state && item.contactState.toLowerCase() !== filters.state.toLowerCase()) {
      return false;
    }

    // Message count filter
    if (filters.minMessages && item.totalMessages < filters.minMessages) {
      return false;
    }
    if (filters.maxMessages && item.totalMessages > filters.maxMessages) {
      return false;
    }

    // Status filter
    if (filters.status && item.status !== filters.status) {
      return false;
    }

    // Has attachments filter
    if (filters.hasAttachments && item.attachments.length === 0) {
      return false;
    }

    // Has links filter
    if (filters.hasLinks && item.links.length === 0) {
      return false;
    }

    return true;
  });
};

export const sortWhatsAppHistory = (
  history: WhatsAppMessageHistory[],
  sortBy: 'name' | 'city' | 'messages' | 'lastMessage' | 'firstMessage',
  sortOrder: 'asc' | 'desc' = 'asc'
) => {
  return [...history].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.contactName.localeCompare(b.contactName);
        break;
      case 'city':
        comparison = a.contactCity.localeCompare(b.contactCity);
        break;
      case 'messages':
        comparison = a.totalMessages - b.totalMessages;
        break;
      case 'lastMessage':
        comparison = new Date(a.lastMessageDate).getTime() - new Date(b.lastMessageDate).getTime();
        break;
      case 'firstMessage':
        comparison = new Date(a.firstMessageDate).getTime() - new Date(b.firstMessageDate).getTime();
        break;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

// Helper function to get all contacts with WhatsApp history
export const getAllWhatsAppContacts = (): WhatsAppMessageHistory[] => {
  return mockWhatsAppHistory;
};

// Helper function to get total number of WhatsApp contacts
export const getTotalWhatsAppContacts = (): number => {
  return mockWhatsAppHistory.length;
};

// Helper function to get message count for a specific contact
export const getMessageCountForContact = (phoneNumber: string): number => {
  const contact = mockWhatsAppHistory.find(c => c.contactPhone === phoneNumber);
  return contact ? contact.totalMessages : 0;
};

// Helper function to get all contacts with their message counts
export const getContactsWithMessageCounts = (): { phone: string; messageCount: number }[] => {
  return mockWhatsAppHistory.map(contact => ({
    phone: contact.contactPhone,
    messageCount: contact.totalMessages
  }));
}; 