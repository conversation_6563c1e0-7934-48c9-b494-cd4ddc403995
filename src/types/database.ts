// Database types for Contact Sphere Organizer

export interface Contact {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  category?: string;
  state?: string;
  city?: string;
  buildType?: string;
  address?: string;
  notes?: string;
  isFavorite: boolean;
  isPinned: boolean;
  isShortlisted: boolean;
  hasReminder: boolean;
  reminderDate?: string;
  reminderNotes?: string;
  createdAt: string;
  updatedAt: string;
  isDeleted: boolean;
  deletedAt?: string;
}

export interface WhatsAppMessage {
  id: string;
  contactId?: string;
  phoneNumber: string;
  message: string;
  messageType: 'text' | 'image' | 'video' | 'document';
  attachments: any[];
  status: 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: string;
  messageId?: string;
}

export interface Category {
  id: string;
  name: string;
  color: string;
  createdAt: string;
}

export interface Settings {
  [key: string]: string;
}

export interface FileAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  contactId?: string;
  messageId?: string;
  createdAt: string;
  path?: string | null;
  data?: ArrayBuffer | null;
}

export interface DatabaseBackup {
  contacts: Contact[];
  whatsappMessages: WhatsAppMessage[];
  categories: Category[];
  settings: Settings;
  timestamp: string;
  version: string;
} 