
export interface Contact {
  id: string;
  name: string;
  phone: string;
  requirements: string;
  categories: string[];
  state: string;
  city: string;
  date: string;
  isFavorite: boolean;
  isPinned: boolean;
  isShortlisted: boolean;
  notes: string;
  attachments: FileAttachment[];
  buildType: string;
  kanbanStage: 'lead' | 'contacted' | 'profile-sent' | 'followup' | 'meeting-done' | 'client-interested' | 'advance-received';
  reminders: Reminder[];
  hasActiveReminder: boolean;
}

export interface FileAttachment {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadDate: string;
}

export interface Reminder {
  id: string;
  title: string;
  date: string;
  time: string;
  notes: string;
}

export interface WhatsAppMessage {
  id: string;
  text: string;
  attachments: FileAttachment[];
  timestamp: string;
  status: 'sent' | 'failed' | 'pending';
  errorMessage?: string;
}

export interface WhatsAppContactHistory {
  contactId: string;
  contactName: string;
  contactPhone: string;
  messages: WhatsAppMessage[];
  lastSentDate: string;
  totalMessagesSent: number;
}

export interface WhatsAppHistory {
  id: string;
  batchId: string;
  sentDate: string;
  contacts: WhatsAppContactHistory[];
  totalContacts: number;
  successfulSends: number;
  failedSends: number;
}
