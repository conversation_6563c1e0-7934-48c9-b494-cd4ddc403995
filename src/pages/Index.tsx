import React, { useState, useEffect } from 'react';
import { Upload, Download, Sun, Moon, Trash2, Search, MessageCircle, Home, X, ChevronLeft, ChevronRight, Plus, Users, UserPlus } from 'lucide-react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { ContactList } from '../components/ContactList';
import { ContactDetail } from '../components/ContactDetail';
import { ImportModal } from '../components/ImportModal';
import { ExportModal } from '../components/ExportModal';
import { AddContactModal } from '../components/AddContactModal';
import { AppSidebar } from '../components/AppSidebar';
import { OverviewCards } from '../components/OverviewCards';
import { KanbanBoard } from '../components/KanbanBoard';
import { LogoutButton } from '../components/auth/LogoutButton';

import { Contact } from '../types/Contact';
import { contactService } from '../services/contactService';
import { whatsappMessageService } from '../services/whatsappMessageService';
import { useContacts } from '../contexts/ContactContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  SidebarProvider,
  SidebarInset,
  useSidebar,
} from '@/components/ui/sidebar';

const Index = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { contacts, deletedContacts, isLoading: isLoadingContacts, addContact, updateContact, deleteContact, restoreContact, importContacts } = useContacts();

  // Custom Sidebar Toggle Component
  const SidebarToggle = () => {
    const { state, toggleSidebar } = useSidebar();
    const isExpanded = state === 'expanded';
    
    return (
      <Button
        onClick={toggleSidebar}
        variant="outline"
        size="sm"
        className="relative bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-md hover:shadow-lg transition-all duration-300 ease-in-out group"
      >
        <div className="flex items-center gap-2">
          <div className="relative w-5 h-5 flex items-center justify-center">
            <ChevronLeft 
              className={`w-4 h-4 transition-all duration-300 ease-in-out ${
                isExpanded 
                  ? 'rotate-0 opacity-100' 
                  : 'rotate-180 opacity-0'
              }`}
            />
            <ChevronRight 
              className={`w-4 h-4 absolute transition-all duration-300 ease-in-out ${
                isExpanded 
                  ? 'rotate-180 opacity-0' 
                  : 'rotate-0 opacity-100'
              }`}
            />
          </div>
          <span className="text-sm font-medium transition-all duration-300 ease-in-out">
            {isExpanded ? 'Hide Filters' : 'Show Filters'}
          </span>
        </div>
        
        {/* Animated arrow indicator */}
        <div className="absolute -right-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out" />
      </Button>
    );
  };

  // Load dark mode preference from localStorage
  const loadDarkModeFromStorage = (): boolean => {
    try {
      const savedDarkMode = localStorage.getItem('contactSphereDarkMode');
      return savedDarkMode ? JSON.parse(savedDarkMode) : false;
    } catch (error) {
      console.error('Error loading dark mode from localStorage:', error);
      return false;
    }
  };

  // Save dark mode preference to localStorage
  const saveDarkModeToStorage = (isDarkMode: boolean) => {
    try {
      localStorage.setItem('contactSphereDarkMode', JSON.stringify(isDarkMode));
    } catch (error) {
      console.error('Error saving dark mode to localStorage:', error);
    }
  };

  // Contacts and deleted contacts state
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStates, setSelectedStates] = useState<string[]>([]);
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [selectedBuildTypes, setSelectedBuildTypes] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string; end: string } | null>(null);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showRecycleBin, setShowRecycleBin] = useState(false);
  const [selectedAlphabet, setSelectedAlphabet] = useState<string>('');
  const [activeView, setActiveView] = useState<'list' | 'kanban'>('list');
  const [isCompactView, setIsCompactView] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned'>('all');
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // WhatsApp contacts and message counts state
  const [whatsappContacts, setWhatsappContacts] = useState<Array<{ contactId: string; contactName: string; contactPhone: string; contactCity: string; contactState: string }>>([]);
  const [contactMessageCounts, setContactMessageCounts] = useState<Map<string, number>>(new Map());

  // Load dark mode preference from localStorage
  const loadedDarkMode = loadDarkModeFromStorage();
  useEffect(() => {
    setIsDarkMode(loadedDarkMode);
    if (loadedDarkMode) {
      document.documentElement.classList.add('dark');
    }
  }, [loadedDarkMode]);

  // Show toast only if data was loaded from localStorage, it's not empty, and it's not the initial load
  useEffect(() => {
    if (contacts.length > 0 && !isInitialLoad) {
      toast({
        title: "Data Loaded",
        description: `${contacts.length} contacts loaded from saved data.`,
      });
    }
    
    // Mark initial load as complete
    setIsInitialLoad(false);
  }, [toast, isInitialLoad, contacts]);

  // Debug effect to track when selectedContact changes
  useEffect(() => {
    if (selectedContact) {
      console.log('🟡 selectedContact state changed to:', selectedContact.name);
      console.log('🟡 Stack trace:', new Error().stack);
    } else {
      console.log('🟡 selectedContact state changed to: null');
    }
  }, [selectedContact]);

  // Debug effect to track contact loading
  useEffect(() => {
    console.log('🟢 Contacts state updated:', {
      totalContacts: contacts.length,
      totalDeletedContacts: deletedContacts.length,
      filteredContacts: filteredContacts.length,
      isLoadingContacts,
      location: location.pathname
    });
  }, [contacts.length, deletedContacts.length, filteredContacts.length, isLoadingContacts, location.pathname]);

  // Get WhatsApp history data from the message service
  useEffect(() => {
    const loadWhatsAppData = async () => {
      try {
        const [contacts, counts] = await Promise.all([
          whatsappMessageService.getAllWhatsAppContacts(),
          whatsappMessageService.getContactsWithMessageCounts()
        ]);
        setWhatsappContacts(contacts);
        const countsMap = new Map();
        counts.forEach(({ contactPhone, messageCount }) => {
          countsMap.set(contactPhone, messageCount);
        });
        setContactMessageCounts(countsMap);
      } catch (error) {
        console.error('Error loading WhatsApp data:', error);
      }
    };
    loadWhatsAppData();
  }, [contacts, deletedContacts]);

  // Calculate summary statistics
  const totalContacts = contacts.length;
  const favoriteContacts = contacts.filter(c => c.isFavorite).length;
  const shortlistedContacts = contacts.filter(c => c.isShortlisted).length;
  const pinnedContacts = contacts.filter(c => c.isPinned).length;
  const reminderContacts = contacts.filter(c => c.hasActiveReminder);

  // Generate alphabet array
  const alphabets = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

  // Check if we're in a special view
  const isSpecialView = showRecycleBin;

  // Keyboard shortcut to return to main view
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showRecycleBin) {
        handleBackToMainView();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [showRecycleBin]);

  // Filter and search contacts
  useEffect(() => {
    const contactsToFilter = showRecycleBin ? deletedContacts : contacts;
    let filtered = contactsToFilter;

    // Apply current filter first
    if (currentFilter !== 'all') {
      if (currentFilter === 'favorites') {
        filtered = filtered.filter(contact => contact.isFavorite);
      } else if (currentFilter === 'shortlisted') {
        filtered = filtered.filter(contact => contact.isShortlisted);
      } else if (currentFilter === 'reminders') {
        filtered = filtered.filter(contact => contact.hasActiveReminder);
      } else if (currentFilter === 'pinned') {
        filtered = filtered.filter(contact => contact.isPinned);
      }
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.phone.includes(searchTerm) ||
        contact.requirements.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.state.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.categories.some(cat => cat.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (contact.buildType && contact.buildType.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply alphabet filter
    if (selectedAlphabet) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().startsWith(selectedAlphabet.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(contact =>
        contact.categories.some(cat => selectedCategories.includes(cat))
      );
    }

    // Apply state filter
    if (selectedStates.length > 0) {
      filtered = filtered.filter(contact =>
        selectedStates.includes(contact.state)
      );
    }

    // Apply city filter
    if (selectedCities.length > 0) {
      filtered = filtered.filter(contact =>
        selectedCities.includes(contact.city)
      );
    }

    // Apply build type filter
    if (selectedBuildTypes.length > 0) {
      filtered = filtered.filter(contact =>
        selectedBuildTypes.includes(contact.buildType || 'Residential')
      );
    }

    // Apply date range filter
    if (dateRange) {
      filtered = filtered.filter(contact => {
        const contactDate = new Date(contact.date);
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);
        return contactDate >= startDate && contactDate <= endDate;
      });
    }

    // Sort by name alphabetically
    filtered = [...filtered].sort((a, b) => a.name.localeCompare(b.name));

    setFilteredContacts(filtered);
  }, [contacts, deletedContacts, searchTerm, selectedCategories, selectedStates, selectedCities, selectedBuildTypes, dateRange, showRecycleBin, selectedAlphabet, currentFilter]);

  const handleCategoryClick = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleStateClick = (state: string) => {
    setSelectedStates(prev =>
      prev.includes(state)
        ? prev.filter(s => s !== state)
        : [...prev, state]
    );
  };

  const handleCityClick = (city: string) => {
    setSelectedCities(prev =>
      prev.includes(city)
        ? prev.filter(c => c !== city)
        : [...prev, city]
    );
  };

  const handleBuildTypeClick = (buildType: string) => {
    setSelectedBuildTypes(prev =>
      prev.includes(buildType)
        ? prev.filter(b => b !== buildType)
        : [...prev, buildType]
    );
  };

  const handleQuickFilter = (filter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned') => {
    // Reset all filters first
    setSelectedCategories([]);
    setSelectedStates([]);
    setSelectedCities([]);
    setSelectedBuildTypes([]);
    setSearchTerm('');
    setSelectedAlphabet('');
    setDateRange(null);
    setShowRecycleBin(false);
    
    // Set the current filter
    setCurrentFilter(filter);
  };

  const handleBackToMainView = () => {
    setShowRecycleBin(false);
  };

  // Function to add contact to WhatsApp sender
  const addContactToWhatsAppSender = (contact: Contact) => {
    // Store the contact in localStorage so WhatsApp sender can access it
    const existingContacts = JSON.parse(localStorage.getItem('whatsappSenderContacts') || '[]');
    const contactExists = existingContacts.find((c: Contact) => c.id === contact.id);
    
    if (!contactExists) {
      const updatedContacts = [...existingContacts, contact];
      localStorage.setItem('whatsappSenderContacts', JSON.stringify(updatedContacts));
      
      toast({
        title: "Contact Added",
        description: `${contact.name} has been added to WhatsApp sender.`,
      });
    } else {
      toast({
        title: "Contact Already Added",
        description: `${contact.name} is already in the WhatsApp sender list.`,
        variant: "destructive",
      });
    }
  };

  // Function to open WhatsApp sender with individual contact
  const openWhatsAppSenderWithContact = (contact: Contact) => {
    // Store the single contact in localStorage
    localStorage.setItem('whatsappSenderContacts', JSON.stringify([contact]));
    
    // Navigate to WhatsApp sender
    navigate('/whatsapp-sender');
    
    toast({
      title: "Opening WhatsApp Sender",
      description: `Preparing to send message to ${contact.name}.`,
    });
  };

  // Toggle dark mode
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    document.documentElement.classList.toggle('dark');
    saveDarkModeToStorage(newDarkMode);
  };

  const getBuildTypeColor = (buildType: string) => {
    const colors = [
      'bg-blue-100 text-blue-800 hover:bg-blue-200',
      'bg-green-100 text-green-800 hover:bg-green-200',
      'bg-purple-100 text-purple-800 hover:bg-purple-200',
      'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      'bg-pink-100 text-pink-800 hover:bg-pink-200',
      'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
      'bg-red-100 text-red-800 hover:bg-red-200',
      'bg-teal-100 text-teal-800 hover:bg-teal-200',
    ];
    const index = buildType.length % colors.length;
    return colors[index];
  };

  const permanentlyDeleteContact = async (id: string) => {
    try {
      const contact = deletedContacts.find(c => c.id === id);
      if (contact) {
        // For now, just delete from the deleted contacts list
        // In a real implementation, you'd have a permanent delete method
        await deleteContact(id);
        
        toast({
          title: "Contact Permanently Deleted",
          description: `${contact.name} has been permanently deleted.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error permanently deleting contact:', error);
    }
  };

  const addNewContact = async (newContact: Contact) => {
    await addContact(newContact);
    setShowAddContactModal(false);
  };

  // Get all unique categories, states, cities, and build types
  const allCategories = Array.from(
    new Set(contacts.flatMap(contact => contact.categories))
  );
  const allStates = Array.from(new Set(contacts.map(contact => contact.state))).sort();
  const allCities = Array.from(new Set(contacts.map(contact => contact.city))).sort();
  const allBuildTypes = Array.from(new Set(contacts.map(contact => contact.buildType || 'Residential'))).sort();

  return (
    <div className={isDarkMode ? 'dark' : ''}>
      <SidebarProvider>
        <div className="min-h-screen flex w-full bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
          <AppSidebar 
            selectedCategories={selectedCategories}
            handleCategoryClick={handleCategoryClick}
            setSelectedCategories={setSelectedCategories}
            selectedStates={selectedStates}
            handleStateClick={handleStateClick}
            setSelectedStates={setSelectedStates}
            selectedCities={selectedCities}
            handleCityClick={handleCityClick}
            setSelectedCities={setSelectedCities}
            selectedBuildTypes={selectedBuildTypes}
            handleBuildTypeClick={handleBuildTypeClick}
            setSelectedBuildTypes={setSelectedBuildTypes}
            allCategories={allCategories}
            allStates={allStates}
            allCities={allCities}
            allBuildTypes={allBuildTypes}
            getCategoryColor={getBuildTypeColor}
            showRecycleBin={showRecycleBin}
            setShowRecycleBin={setShowRecycleBin}
            deletedContactsCount={deletedContacts.length}
            totalContacts={totalContacts}
            favoriteContacts={favoriteContacts}
            shortlistedContacts={shortlistedContacts}
            onQuickFilter={handleQuickFilter}
            filteredContacts={filteredContacts}
            dateRange={dateRange}
            setDateRange={setDateRange}
          />
          <SidebarInset className="flex-1 flex flex-col min-w-0">
            {/* Header Panel */}
            <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <SidebarToggle />
                </div>
                
                <div className="flex items-center gap-2">
                  {/* Back to Main View Button - Show only in recycle bin view */}
                  {showRecycleBin && (
                    <Button
                      onClick={handleBackToMainView}
                      variant="outline"
                      size="sm"
                      className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      Back to Main View
                    </Button>
                  )}

                  {/* Clear Recycle Bin Filter Button - Show only in recycle bin view */}
                  {showRecycleBin && (
                    <Button
                      onClick={() => setShowRecycleBin(false)}
                      variant="outline"
                      size="sm"
                      className="bg-red-50 hover:bg-red-100 border-red-300 text-red-700"
                    >
                      <X className="w-4 h-4 mr-2" />
                      Exit Recycle Bin
                    </Button>
                  )}

                  {/* WhatsApp Sender Button */}
                  {!showRecycleBin && (
                    <Link to="/whatsapp-sender">
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        WhatsApp Sender
                      </Button>
                    </Link>
                  )}

                  {/* Recycle Bin Toggle */}
                  <Button
                    onClick={() => setShowRecycleBin(!showRecycleBin)}
                    variant={showRecycleBin ? "default" : "outline"}
                    size="sm"
                    className={`transition-all duration-300 ease-in-out transform hover:scale-105 ${
                      showRecycleBin 
                        ? "bg-red-600 hover:bg-red-700 shadow-lg hover:shadow-xl" 
                        : "hover:bg-red-50 hover:border-red-300 hover:text-red-700 shadow-sm hover:shadow-md"
                    }`}
                  >
                    <Trash2 className={`w-4 h-4 mr-2 transition-all duration-300 ${
                      showRecycleBin ? 'animate-pulse' : ''
                    }`} />
                    <span className="transition-all duration-300">
                      Recycle Bin
                    </span>
                    {deletedContacts.length > 0 && (
                      <Badge 
                        variant="secondary" 
                        className={`ml-2 transition-all duration-300 ease-in-out transform ${
                          showRecycleBin ? 'bg-white text-red-600 scale-110' : 'bg-red-100 text-red-700'
                        }`}
                      >
                        {deletedContacts.length}
                      </Badge>
                    )}
                  </Button>

                  {/* View Toggle */}
                  {!showRecycleBin && (
                    <Tabs value={activeView} onValueChange={(value) => setActiveView(value as 'list' | 'kanban')}>
                      <TabsList>
                        <TabsTrigger value="list">List</TabsTrigger>
                        <TabsTrigger value="kanban">Kanban</TabsTrigger>
                      </TabsList>
                    </Tabs>
                  )}

                  {/* Action Buttons */}
                  {!showRecycleBin && (
                    <div className="flex gap-2">
                      <Button
                        onClick={() => setShowAddContactModal(true)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Contact
                      </Button>
                      <Button
                        onClick={() => setShowImportModal(true)}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Import
                      </Button>
                      <Button
                        onClick={() => setShowExportModal(true)}
                        size="sm"
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Export CSV
                      </Button>
                    </div>
                  )}
                  
                  {/* Dark Mode Toggle */}
                  <div className="flex items-center gap-2 ml-2">
                    <Sun className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                    <Switch checked={isDarkMode} onCheckedChange={toggleDarkMode} />
                    <Moon className="w-4 h-4 text-gray-600 dark:text-gray-300" />
                  </div>
                  
                  {/* Logout Button */}
                  <LogoutButton />
                </div>
              </div>
            </div>

            {/* Overview Cards - Only show when there are contacts */}
            {!showRecycleBin && contacts.length > 0 && (
              <OverviewCards
                pinnedContacts={pinnedContacts}
                favoriteContacts={favoriteContacts}
                shortlistedContacts={shortlistedContacts}
                reminderContacts={reminderContacts.length}
                allContacts={contacts.length}
                filteredContacts={filteredContacts}
                allCategories={allCategories}
                currentFilter={currentFilter}
                onQuickFilter={handleQuickFilter}
                selectedCategories={selectedCategories}
                onCategoryClick={handleCategoryClick}
              />
            )}

            {/* Build Types and Alphabet Navigation - Only show when there are contacts */}
            {!showRecycleBin && contacts.length > 0 && (
              <div className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-4 flex-shrink-0">
                {/* Build Types */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Build Types</h3>
                  <div className="flex flex-wrap gap-1">
                    {allBuildTypes.slice(0, 8).map((buildType) => (
                      <Badge
                        key={buildType}
                        onClick={() => handleBuildTypeClick(buildType)}
                        className={`text-xs px-2 py-1 cursor-pointer transition-colors ${
                          selectedBuildTypes.includes(buildType)
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : getBuildTypeColor(buildType)
                        }`}
                      >
                        {buildType}
                      </Badge>
                    ))}
                    {allBuildTypes.length > 8 && (
                      <Badge className="text-xs px-2 py-1 bg-gray-100 text-gray-600">
                        +{allBuildTypes.length - 8} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Alphabet Navigation */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    <Button
                      onClick={() => setSelectedAlphabet('')}
                      variant={selectedAlphabet === '' ? "default" : "ghost"}
                      size="sm"
                      className="h-6 px-2 text-xs"
                    >
                      All
                    </Button>
                    {alphabets.map((letter) => (
                      <Button
                        key={letter}
                        onClick={() => setSelectedAlphabet(letter)}
                        variant={selectedAlphabet === letter ? "default" : "ghost"}
                        size="sm"
                        className="h-6 px-2 text-xs"
                      >
                        {letter}
                      </Button>
                    ))}
                    <Button
                      onClick={() => setIsCompactView(!isCompactView)}
                      variant={isCompactView ? "default" : "outline"}
                      size="sm"
                      className="h-6 px-2 text-xs ml-4"
                    >
                      {isCompactView ? 'Compact' : 'Detailed'}
                    </Button>
                  </div>
                  
                  {/* Search - Moved from header */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search contacts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Contact Detail Modal */}
            {selectedContact && (
              <ContactDetail
                contact={selectedContact}
                onClose={() => setSelectedContact(null)}
                onUpdate={updateContact}
                onDelete={deleteContact}
                onAddToWhatsAppSender={addContactToWhatsAppSender}
                onOpenWhatsAppSenderWithContact={openWhatsAppSenderWithContact}
              />
            )}

            {/* Import Modal */}
            {showImportModal && (
              <ImportModal
                onClose={() => setShowImportModal(false)}
                onImport={async (importedContacts) => {
                  console.log('🔄 Starting import of', importedContacts.length, 'contacts');
                  
                  // Use the ContactContext's importContacts method for proper state management
                  try {
                    const result = await importContacts(importedContacts);
                    console.log('✅ Import completed successfully:', result);
                  } catch (error) {
                    console.error('❌ Import failed:', error);
                    toast({
                      title: "Import Failed",
                      description: "Some contacts could not be imported. Please try again.",
                      variant: "destructive",
                    });
                  }
                  
                  setShowImportModal(false);
                }}
              />
            )}

            {/* Export Modal */}
            {showExportModal && (
              <ExportModal
                contacts={filteredContacts}
                allContacts={contacts}
                onClose={() => setShowExportModal(false)}
              />
            )}

            {/* Add Contact Modal */}
            {showAddContactModal && (
              <AddContactModal
                onClose={() => setShowAddContactModal(false)}
                onAdd={addContact}
                existingCategories={allCategories}
                existingBuildTypes={allBuildTypes}
              />
            )}

            {/* Content Area */}
            <div className="flex-1 p-6 overflow-auto min-h-0">
              {isLoadingContacts ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-400">Loading contacts...</p>
                  </div>
                </div>
              ) : contacts.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <UserPlus className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                    <h3 className="text-xl font-medium text-gray-600 mb-2">No contacts yet</h3>
                    <p className="text-gray-500 mb-6">Start by adding your first contact or importing from CSV</p>
                    <div className="flex gap-3 justify-center">
                      <Button
                        onClick={() => setShowAddContactModal(true)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add Contact
                      </Button>
                      <Button
                        onClick={() => setShowImportModal(true)}
                        variant="outline"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Import CSV
                      </Button>
                    </div>
                  </div>
                </div>
              ) : activeView === 'list' ? (
                <ContactList
                  contacts={filteredContacts}
                  onContactClick={(contact) => {
                    console.log('🟡 setSelectedContact called from ContactList - Contact:', contact.name);
                    console.log('🟡 Stack trace:', new Error().stack);
                    setSelectedContact(contact);
                  }}
                  onUpdateContact={updateContact}
                  onDeleteContact={showRecycleBin ? permanentlyDeleteContact : deleteContact}
                  onRestoreContact={showRecycleBin ? restoreContact : undefined}
                  isRecycleBin={showRecycleBin}
                  isListView={true}
                  isCompactView={isCompactView}
                  onAddToWhatsAppSender={addContactToWhatsAppSender}
                  onOpenWhatsAppSenderWithContact={openWhatsAppSenderWithContact}
                  contactMessageCounts={contactMessageCounts}
                />
              ) : (
                <KanbanBoard
                  contacts={filteredContacts}
                  onContactClick={(contact) => {
                    console.log('🟡 setSelectedContact called from KanbanBoard - Contact:', contact.name);
                    console.log('🟡 Stack trace:', new Error().stack);
                    setSelectedContact(contact);
                  }}
                  onUpdateContact={updateContact}
                  isCompactView={isCompactView}
                  onAddToWhatsAppSender={addContactToWhatsAppSender}
                  onOpenWhatsAppSenderWithContact={openWhatsAppSenderWithContact}
                  contactMessageCounts={contactMessageCounts}
                />
              )}
            </div>
          </SidebarInset>
        </div>
      </SidebarProvider>
    </div>
  );
};

export default Index;
