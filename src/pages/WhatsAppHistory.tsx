import React, { useState, useEffect } from 'react';
import { ArrowLeft, History, MessageCircle, User, AlertTriangle } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SentMessage } from '@/pages/WhatsAppSender';
import { whatsappMessageService } from '@/services/whatsappMessageService';
import { contactService } from '@/services/contactService';
import { Contact } from '@/types/Contact';

const WhatsAppHistory: React.FC = () => {
  const location = useLocation();
  const [sentMessages, setSentMessages] = useState<SentMessage[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Load sent messages and contacts
  useEffect(() => {
    const loadData = async () => {
      try {
        const allMessages = whatsappMessageService.getSentMessages();
        const allContacts = await contactService.getContacts();
        
        console.log('📊 WhatsApp History - Loading data:');
        console.log('📊 Total messages loaded:', allMessages.length);
        console.log('📊 Messages with dates:', allMessages.map(msg => ({
          id: msg.id,
          contactName: msg.contactName,
          sentAt: msg.sentAt,
          date: new Date(msg.sentAt).toDateString()
        })));
        
        setSentMessages(allMessages);
        setContacts(allContacts);
        
        // Sync contact information
        await whatsappMessageService.syncContactInformation();
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();

    // Subscribe to updates
    const unsubscribeMessages = whatsappMessageService.subscribe('messages-updated', (event) => {
      loadData();
    });

    const unsubscribeMessageSent = whatsappMessageService.subscribe('message-sent', (event) => {
      loadData();
    });

    const unsubscribeMessageFailed = whatsappMessageService.subscribe('message-failed', (event) => {
      loadData();
    });

    const unsubscribeContacts = contactService.addEventListener('contacts-updated', (event) => {
      loadData();
    });

    return () => {
      unsubscribeMessages();
      unsubscribeMessageSent();
      unsubscribeMessageFailed();
      // Note: contactService doesn't have a removeEventListener method, so we can't unsubscribe
    };
  }, [location.pathname]);

  const filteredMessages = sentMessages.filter(msg => {
    const matchesSearch = msg.contactName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         msg.contactPhone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || msg.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const groupedMessages = filteredMessages.reduce((groups, message) => {
    const date = new Date(message.sentAt).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {} as Record<string, SentMessage[]>);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'not_delivered': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return '✓✓';
      case 'failed': return '✗';
      case 'not_delivered': return '⚠️';
      default: return '?';
    }
  };

  const totalSent = sentMessages.filter(m => m.status === 'sent').length;
  const totalFailed = sentMessages.filter(m => m.status === 'failed').length;
  const totalNotDelivered = sentMessages.filter(m => m.status === 'not_delivered').length;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <History className="w-6 h-6 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                WhatsApp History
              </h1>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={async () => {
                console.log('🔄 Manual refresh triggered');
                const allMessages = whatsappMessageService.getSentMessages();
                console.log('🔄 Manual refresh - messages found:', allMessages.length);
                setSentMessages(allMessages);
              }}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => {
                const savedMessages = localStorage.getItem('whatsappSentMessages');
                if (savedMessages) {
                  const parsed = JSON.parse(savedMessages);
                  console.log('💾 localStorage messages:', parsed.length);
                  console.log('💾 localStorage message dates:', parsed.map((msg: any) => ({
                    id: msg.id,
                    contactName: msg.contactName,
                    sentAt: msg.sentAt,
                    date: new Date(msg.sentAt).toDateString()
                  })));
                } else {
                  console.log('💾 No messages in localStorage');
                }
              }}
            >
              Check localStorage
            </Button>
            
            <Link to="/whatsapp-sender">
              <Button className="bg-green-600 hover:bg-green-700">
                <MessageCircle className="w-4 h-4 mr-2" />
                Send New Messages
              </Button>
            </Link>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">{sentMessages.length}</div>
                <div className="text-sm text-blue-700">Total Messages</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{totalSent}</div>
                <div className="text-sm text-green-700">Successfully Sent</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{totalFailed}</div>
                <div className="text-sm text-red-700">Failed</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{totalNotDelivered}</div>
                <div className="text-sm text-yellow-700">Not Delivered</div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <Input
                      placeholder="Search by contact name or phone..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="sent">Successfully Sent</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="not_delivered">Not Delivered</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Messages List */}
          <Card>
            <CardHeader>
              <CardTitle>Message History ({filteredMessages.length} messages)</CardTitle>
            </CardHeader>
            <CardContent>
              {Object.keys(groupedMessages).length === 0 ? (
                <div className="text-center py-8">
                  <History className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No messages found matching your criteria.</p>
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <div className="space-y-6">
                    {Object.entries(groupedMessages)
                      .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                      .map(([date, messages]) => (
                        <div key={date}>
                          <div className="flex items-center gap-2 mb-3">
                            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h3 className="font-medium text-gray-900">
                              {new Date(date).toLocaleDateString('en-US', { 
                                weekday: 'long', 
                                year: 'numeric', 
                                month: 'long', 
                                day: 'numeric' 
                              })}
                            </h3>
                            <Badge variant="outline">{messages.length} messages</Badge>
                          </div>
                          
                          <div className="space-y-2 pl-6">
                            {messages.map(message => {
                              // Check if contact is deleted
                              const contactExists = contacts.find(c => c.phone === message.contactPhone);
                              const isDeleted = !contactExists;
                              
                              return (
                                <div key={message.id} className={`flex items-center justify-between p-3 rounded-lg ${
                                  isDeleted ? 'bg-gray-100 border border-gray-300' : 'bg-gray-50'
                                }`}>
                                  <div className="flex items-center gap-3">
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                      isDeleted ? 'bg-gray-200' : 'bg-blue-100'
                                    }`}>
                                      {isDeleted ? (
                                        <AlertTriangle className="w-4 h-4 text-gray-500" />
                                      ) : (
                                        <User className="w-4 h-4 text-blue-600" />
                                      )}
                                    </div>
                                    <div>
                                      <div className={`font-medium ${
                                        isDeleted ? 'text-gray-500 line-through' : ''
                                      }`}>
                                        {message.contactName}
                                        {isDeleted && (
                                          <Badge variant="outline" className="ml-2 text-xs bg-gray-200 text-gray-600">
                                            Deleted
                                          </Badge>
                                        )}
                                      </div>
                                      <div className={`text-sm ${
                                        isDeleted ? 'text-gray-400' : 'text-gray-600'
                                      }`}>
                                        {message.contactPhone}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {new Date(message.sentAt).toLocaleTimeString()}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="flex items-center gap-2">
                                    <Badge 
                                      variant="secondary" 
                                      className={getStatusColor(message.status)}
                                    >
                                      {getStatusIcon(message.status)} {message.status.replace('_', ' ')}
                                    </Badge>
                                    
                                    {message.retryCount > 0 && (
                                      <Badge variant="outline" className="text-xs">
                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                        </svg>
                                        {message.retryCount} retries
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>

          {sentMessages.length === 0 && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-6 text-center">
                <MessageCircle className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-blue-900 mb-2">No Messages Sent Yet</h3>
                <p className="text-blue-700 mb-4">
                  Start by sending your first bulk WhatsApp message to your contacts.
                </p>
                <Link to="/whatsapp-sender">
                  <Button className="bg-green-600 hover:bg-green-700">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Send Your First Message
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppHistory; 