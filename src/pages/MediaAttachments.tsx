import React, { useState, useEffect } from 'react';
import { ArrowLeft, Folder, File, Image as ImageIcon, Video, FileText, Grid, List, Plus, Upload, Search, MoreVertical, Download, Trash2, Edit3, FolderPlus, X, ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { fileUploadService } from '@/services/fileUploadService';

export interface MediaFile {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'pdf' | 'excel' | 'other';
  size: number;
  url: string;
  thumbnail?: string;
  uploadedAt: string;
  folderId?: string;
  isDeleted?: boolean;
  deletedAt?: string;
}

export interface MediaFolder {
  id: string;
  name: string;
  parentId?: string;
  createdAt: string;
  files: MediaFile[];
  subfolders: MediaFolder[];
  isDeleted?: boolean;
  deletedAt?: string;
}

const MediaAttachments: React.FC = () => {
  const [currentFolder, setCurrentFolder] = useState<MediaFolder | null>(null);
  const [folders, setFolders] = useState<MediaFolder[]>([]);
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [fileViewMode, setFileViewMode] = useState<'small' | 'large' | 'list'>('large');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showNewFolderDialog, setShowNewFolderDialog] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [breadcrumb, setBreadcrumb] = useState<MediaFolder[]>([]);
  const [previewFile, setPreviewFile] = useState<MediaFile | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showRecycleBin, setShowRecycleBin] = useState(false);
  const [deletedFiles, setDeletedFiles] = useState<MediaFile[]>([]);
  const [deletedFolders, setDeletedFolders] = useState<MediaFolder[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Load files from database
  const loadFilesFromDatabase = async () => {
    setIsLoading(true);
    try {
      console.log('🔄 Loading files from database...');
      const response = await fetch('https://saamrajyam.com/api/files.php');
      console.log('📡 API Response status:', response.status);
      console.log('📡 API Response headers:', response.headers);

      if (response.ok) {
        const responseText = await response.text();
        console.log('📡 Raw response text:', responseText);

        if (!responseText.trim()) {
          console.warn('⚠️ Empty response from API');
          return;
        }

        const result = JSON.parse(responseText);
        console.log('📡 Parsed API result:', result);

        if (result.success && result.files) {
          // Convert database files to MediaFile format
          const dbFiles: MediaFile[] = result.files.map((dbFile: any) => ({
            id: dbFile.id.toString(),
            name: dbFile.original_name,
            type: dbFile.file_type,
            size: parseInt(dbFile.file_size),
            url: `https://saamrajyam.com${dbFile.file_path}`,
            uploadedAt: dbFile.created_at,
            folderId: getFolderIdByType(dbFile.file_type),
          }));

          // Merge with existing files from localStorage (for backward compatibility)
          const existingFiles = files.filter(f => !f.url.startsWith('https://saamrajyam.com'));
          const allFiles = [...dbFiles, ...existingFiles];
          setFiles(allFiles);

          // Update folders to include the files in their respective folders
          setFolders(prevFolders => {
            return prevFolders.map(folder => ({
              ...folder,
              files: allFiles.filter(file => file.folderId === folder.id)
            }));
          });

          console.log('✅ Loaded files from database:', dbFiles.length);
        } else {
          console.warn('⚠️ API returned success=false or no files:', result);
        }
      } else {
        const errorText = await response.text();
        console.warn('⚠️ Failed to load files from database. Status:', response.status, 'Response:', errorText);
      }
    } catch (error) {
      console.error('❌ Error loading files from database:', error);
      if (error instanceof SyntaxError) {
        console.error('❌ JSON parsing error - API might be returning HTML or empty response');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get folder ID by file type
  const getFolderIdByType = (fileType: string): string => {
    switch (fileType) {
      case 'image': return '1';
      case 'video': return '2';
      case 'document': return '3';
      case 'audio': return '4';
      default: return '5';
    }
  };

  // Load data from localStorage and database on component mount
  useEffect(() => {
    // Initialize default folders if none exist
    const defaultFolders: MediaFolder[] = [
      {
        id: '1',
        name: 'Images',
        description: 'Photo files',
        color: '#3B82F6',
        createdAt: new Date().toISOString(),
        files: [],
        subfolders: []
      },
      {
        id: '2',
        name: 'Videos',
        description: 'Video files',
        color: '#EF4444',
        createdAt: new Date().toISOString(),
        files: [],
        subfolders: []
      },
      {
        id: '3',
        name: 'Documents',
        description: 'PDF and document files',
        color: '#10B981',
        createdAt: new Date().toISOString(),
        files: [],
        subfolders: []
      },
      {
        id: '4',
        name: 'Audio',
        description: 'Audio files',
        color: '#F59E0B',
        createdAt: new Date().toISOString(),
        files: [],
        subfolders: []
      },
      {
        id: '5',
        name: 'Other',
        description: 'Other file types',
        color: '#6B7280',
        createdAt: new Date().toISOString(),
        files: [],
        subfolders: []
      }
    ];

    const savedFolders = localStorage.getItem('mediaFolders');
    const savedFiles = localStorage.getItem('mediaFiles');
    const savedDeletedFiles = localStorage.getItem('deletedMediaFiles');
    const savedDeletedFolders = localStorage.getItem('deletedMediaFolders');

    if (savedFolders) {
      try {
        const parsedFolders = JSON.parse(savedFolders);
        // Filter out deleted folders and their contents
        const filterDeletedFolders = (folders: MediaFolder[]): MediaFolder[] => {
          return folders
            .filter((folder: MediaFolder) => !folder.isDeleted)
            .map((folder: MediaFolder) => ({
              ...folder,
              files: folder.files.filter((file: MediaFile) => !file.isDeleted),
              subfolders: filterDeletedFolders(folder.subfolders)
            }));
        };
        const activeFolders = filterDeletedFolders(parsedFolders);
        setFolders(activeFolders);
      } catch (error) {
        console.error('Error loading folders:', error);
        setFolders(defaultFolders);
      }
    } else {
      // Set default folders if none are saved
      setFolders(defaultFolders);
    }
    
    if (savedFiles) {
      try {
        const parsedFiles = JSON.parse(savedFiles);
        console.log('MediaAttachments - Raw files loaded:', parsedFiles.length);
        // Filter out deleted files
        const activeFiles = parsedFiles.filter((file: MediaFile) => !file.isDeleted);
        console.log('MediaAttachments - Active files after filtering:', activeFiles.length);
        setFiles(activeFiles);
      } catch (error) {
        console.error('Error loading files:', error);
      }
    }

    if (savedDeletedFiles) {
      try {
        const parsedDeletedFiles = JSON.parse(savedDeletedFiles);
        setDeletedFiles(parsedDeletedFiles);
      } catch (error) {
        console.error('Error loading deleted files:', error);
      }
    }

    if (savedDeletedFolders) {
      try {
        const parsedDeletedFolders = JSON.parse(savedDeletedFolders);
        setDeletedFolders(parsedDeletedFolders);
      } catch (error) {
        console.error('Error loading deleted folders:', error);
      }
    }

    // Load files from database
    loadFilesFromDatabase();
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('mediaFolders', JSON.stringify(folders));
  }, [folders]);

  useEffect(() => {
    localStorage.setItem('mediaFiles', JSON.stringify(files));
  }, [files]);

  useEffect(() => {
    localStorage.setItem('deletedMediaFiles', JSON.stringify(deletedFiles));
  }, [deletedFiles]);

  useEffect(() => {
    localStorage.setItem('deletedMediaFolders', JSON.stringify(deletedFolders));
  }, [deletedFolders]);

  // Update breadcrumb when current folder changes
  useEffect(() => {
    if (!currentFolder) {
      setBreadcrumb([]);
      return;
    }

    const buildBreadcrumb = (folder: MediaFolder, allFolders: MediaFolder[]): MediaFolder[] => {
      if (!folder.parentId) {
        return [folder];
      }
      
      const parent = allFolders.find(f => f.id === folder.parentId);
      if (!parent) {
        return [folder];
      }
      
      return [...buildBreadcrumb(parent, allFolders), folder];
    };

    setBreadcrumb(buildBreadcrumb(currentFolder, folders));
  }, [currentFolder, folders]);

  // Handle keyboard navigation for preview
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!showPreview || !previewFile) return;
      
      if (event.key === 'Escape') {
        setShowPreview(false);
        setPreviewFile(null);
      }
      
      if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
        const currentFiles = getCurrentItems().files.filter(f => 
          f.type === 'image' || f.type === 'pdf'
        );
        const currentIndex = currentFiles.findIndex(f => f.id === previewFile.id);
        
        if (event.key === 'ArrowLeft' && currentIndex > 0) {
          setPreviewFile(currentFiles[currentIndex - 1]);
        } else if (event.key === 'ArrowRight' && currentIndex < currentFiles.length - 1) {
          setPreviewFile(currentFiles[currentIndex + 1]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [showPreview, previewFile]);

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="w-6 h-6 text-blue-500" />;
      case 'video': return <Video className="w-6 h-6 text-red-500" />;
      case 'pdf': return <FileText className="w-6 h-6 text-red-600" />;
      case 'excel': return <FileText className="w-6 h-6 text-green-600" />;
      case 'document': return <FileText className="w-6 h-6 text-blue-600" />;
      default: return <File className="w-6 h-6 text-gray-500" />;
    }
  };

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image': return 'bg-blue-100 text-blue-800';
      case 'video': return 'bg-red-100 text-red-800';
      case 'pdf': return 'bg-red-100 text-red-800';
      case 'excel': return 'bg-green-100 text-green-800';
      case 'document': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFolderThumbnail = (folder: MediaFolder): string | undefined => {
    // Get the first image file in the folder or its subfolders
    const findFirstImage = (f: MediaFolder): MediaFile | null => {
      const imageFile = f.files.find(file => file.type === 'image');
      if (imageFile) return imageFile;
      
      for (const subfolder of f.subfolders) {
        const found = findFirstImage(subfolder);
        if (found) return found;
      }
      return null;
    };

    const firstImage = findFirstImage(folder);
    return firstImage?.thumbnail;
  };

  const createNewFolder = () => {
    if (!newFolderName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a folder name.",
        variant: "destructive",
      });
      return;
    }

    const newFolder: MediaFolder = {
      id: Date.now().toString(),
      name: newFolderName.trim(),
      parentId: currentFolder?.id,
      createdAt: new Date().toISOString(),
      files: [],
      subfolders: [],
    };

    if (currentFolder) {
      // Add to current folder's subfolders
      setFolders(prev => {
        const updateFolder = (folders: MediaFolder[]): MediaFolder[] => {
          return folders.map(folder => {
            if (folder.id === currentFolder.id) {
              return { ...folder, subfolders: [...folder.subfolders, newFolder] };
            }
            return { ...folder, subfolders: updateFolder(folder.subfolders) };
          });
        };
        return updateFolder(prev);
      });
    } else {
      // Add to root level
      setFolders(prev => [...prev, newFolder]);
    }

    setNewFolderName('');
    setShowNewFolderDialog(false);
    
    toast({
      title: "Folder Created",
      description: `Folder "${newFolder.name}" has been created successfully.`,
    });
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = event.target.files;
    if (!uploadedFiles || uploadedFiles.length === 0) return;

    setIsUploading(true);

    try {
      console.log('📤 Starting file upload to server...', uploadedFiles.length, 'files');

      // Upload files to server using the existing fileUploadService
      const serverFiles = await fileUploadService.uploadFiles(uploadedFiles);
      console.log('✅ Server upload completed:', serverFiles.length, 'files');

      // Convert server files to MediaFile format
      const newFiles: MediaFile[] = serverFiles.map(serverFile => {
        const fileType = serverFile.type.startsWith('image/') ? 'image' :
                        serverFile.type.startsWith('video/') ? 'video' :
                        serverFile.type === 'application/pdf' ? 'pdf' :
                        serverFile.type.includes('excel') || serverFile.type.includes('spreadsheet') ? 'excel' :
                        serverFile.type.includes('document') || serverFile.type.includes('text') ? 'document' : 'other';

        return {
          id: serverFile.id,
          name: serverFile.name,
          type: fileType,
          size: serverFile.size,
          url: serverFile.url, // ✅ Server URL instead of blob URL
          thumbnail: fileType === 'image' ? serverFile.url : undefined,
          uploadedAt: serverFile.uploadDate,
          folderId: currentFolder?.id,
        };
      });

      // Add files to state
      setFiles(prev => [...prev, ...newFiles]);

      toast({
        title: "Files Uploaded Successfully",
        description: `${newFiles.length} file(s) have been uploaded to server and are ready for WhatsApp sending.`,
      });

      console.log('✅ Files added to MediaAttachments:', newFiles.length);

    } catch (error) {
      console.error('❌ File upload failed:', error);

      // Fallback to local storage (blob URLs) if server upload fails
      console.log('⚠️ Falling back to local storage...');

      const fallbackFiles: MediaFile[] = Array.from(uploadedFiles).map(file => {
        const fileType = file.type.startsWith('image/') ? 'image' :
                        file.type.startsWith('video/') ? 'video' :
                        file.type === 'application/pdf' ? 'pdf' :
                        file.type.includes('excel') || file.type.includes('spreadsheet') ? 'excel' :
                        file.type.includes('document') || file.type.includes('text') ? 'document' : 'other';

        return {
          id: Date.now().toString() + Math.random(),
          name: file.name,
          type: fileType,
          size: file.size,
          url: URL.createObjectURL(file), // Fallback to blob URL
          thumbnail: fileType === 'image' ? URL.createObjectURL(file) : undefined,
          uploadedAt: new Date().toISOString(),
          folderId: currentFolder?.id,
        };
      });

      setFiles(prev => [...prev, ...fallbackFiles]);

      toast({
        title: "Upload Warning",
        description: `Files saved locally. Server upload failed: ${error instanceof Error ? error.message : 'Unknown error'}. Files may not work with WhatsApp sending.`,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      // Clear the input so the same files can be uploaded again if needed
      event.target.value = '';
    }
  };

  const openFolder = (folder: MediaFolder) => {
    setCurrentFolder(folder);
    setSelectedItems([]);
  };

  const navigateToParent = () => {
    if (currentFolder?.parentId) {
      const parentFolder = folders.find(f => f.id === currentFolder.parentId && !f.isDeleted);
      if (parentFolder) {
        setCurrentFolder(parentFolder);
      }
    } else {
      setCurrentFolder(null);
    }
    setSelectedItems([]);
  };

  const navigateToBreadcrumb = (folder: MediaFolder) => {
    setCurrentFolder(folder);
    setSelectedItems([]);
  };

  const getCurrentItems = () => {
    if (showRecycleBin) {
      console.log('MediaAttachments - Recycle bin mode, deleted files:', deletedFiles.length, 'deleted folders:', deletedFolders.length);
      return {
        folders: deletedFolders,
        files: deletedFiles
      };
    }

    if (!currentFolder) {
      // Root level - show folders without parent and files without folder
      const rootFolders = folders.filter(f => !f.parentId && !f.isDeleted);
      const rootFiles = files.filter(f => !f.folderId && !f.isDeleted);
      console.log('MediaAttachments - Root level, folders:', rootFolders.length, 'files:', rootFiles.length);
      return { folders: rootFolders, files: rootFiles };
    }

    // Current folder level
    const subfolders = currentFolder.subfolders.filter(f => !f.isDeleted);
    const folderFiles = currentFolder.files.filter(f => !f.isDeleted);
    console.log(`MediaAttachments - Folder "${currentFolder.name}", subfolders:`, subfolders.length, 'files:', folderFiles.length);
    return { folders: subfolders, files: folderFiles };
  };

  const filteredItems = () => {
    const { files: currentFiles, folders: currentFolders } = getCurrentItems();
    
    const filteredFiles = currentFiles.filter(file =>
      file.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    const filteredFolders = currentFolders.filter(folder =>
      folder.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    return { files: filteredFiles, folders: filteredFolders };
  };

  const openPreview = (file: MediaFile) => {
    if (file.type === 'image' || file.type === 'pdf') {
      setPreviewFile(file);
      setShowPreview(true);
    }
  };

  const closePreview = () => {
    setShowPreview(false);
    setPreviewFile(null);
  };

  const navigatePreview = (direction: 'prev' | 'next') => {
    if (!previewFile) return;
    
    const currentFiles = getCurrentItems().files.filter(f => 
      f.type === 'image' || f.type === 'pdf'
    );
    const currentIndex = currentFiles.findIndex(f => f.id === previewFile.id);
    
    if (direction === 'prev' && currentIndex > 0) {
      setPreviewFile(currentFiles[currentIndex - 1]);
    } else if (direction === 'next' && currentIndex < currentFiles.length - 1) {
      setPreviewFile(currentFiles[currentIndex + 1]);
    }
  };

  const getThumbnailSize = () => {
    switch (fileViewMode) {
      case 'small': return 'w-12 h-12';
      case 'large': return 'w-32 h-32';
      case 'list': return 'w-8 h-8';
      default: return 'w-20 h-20';
    }
  };

  const { files: displayFiles, folders: displayFolders } = filteredItems();

  const deleteFile = (fileId: string) => {
    const file = files.find(f => f.id === fileId);
    if (!file) return;

    const updatedFile = { ...file, isDeleted: true, deletedAt: new Date().toISOString() };
    setDeletedFiles(prev => [...prev, updatedFile]);
    setFiles(prev => prev.filter(f => f.id !== fileId));

    toast({
      title: "File Deleted",
      description: `File "${file.name}" has been moved to recycle bin.`,
    });
  };

  const deleteFolder = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    if (!folder) return;

    const updatedFolder = { ...folder, isDeleted: true, deletedAt: new Date().toISOString() };
    setDeletedFolders(prev => [...prev, updatedFolder]);
    setFolders(prev => prev.filter(f => f.id !== folderId));

    toast({
      title: "Folder Deleted",
      description: `Folder "${folder.name}" has been moved to recycle bin.`,
    });
  };

  const restoreFile = (fileId: string) => {
    const file = deletedFiles.find(f => f.id === fileId);
    if (!file) return;

    const restoredFile = { ...file, isDeleted: false, deletedAt: undefined };
    setFiles(prev => [...prev, restoredFile]);
    setDeletedFiles(prev => prev.filter(f => f.id !== fileId));

    toast({
      title: "File Restored",
      description: `File "${file.name}" has been restored.`,
    });
  };

  const restoreFolder = (folderId: string) => {
    const folder = deletedFolders.find(f => f.id === folderId);
    if (!folder) return;

    const restoredFolder = { ...folder, isDeleted: false, deletedAt: undefined };
    setFolders(prev => [...prev, restoredFolder]);
    setDeletedFolders(prev => prev.filter(f => f.id !== folderId));

    toast({
      title: "Folder Restored",
      description: `Folder "${folder.name}" has been restored.`,
    });
  };

  const permanentlyDeleteFile = (fileId: string) => {
    const file = deletedFiles.find(f => f.id === fileId);
    if (!file) return;

    setDeletedFiles(prev => prev.filter(f => f.id !== fileId));

    toast({
      title: "File Permanently Deleted",
      description: `File "${file.name}" has been permanently deleted.`,
    });
  };

  const permanentlyDeleteFolder = (folderId: string) => {
    const folder = deletedFolders.find(f => f.id === folderId);
    if (!folder) return;

    setDeletedFolders(prev => prev.filter(f => f.id !== folderId));

    toast({
      title: "Folder Permanently Deleted",
      description: `Folder "${folder.name}" has been permanently deleted.`,
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Link to="/whatsapp-sender">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to WhatsApp Sender
              </Button>
            </Link>
            <div className="flex items-center gap-2">
              <Folder className="w-6 h-6 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Media & Attachments
              </h1>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={loadFilesFromDatabase}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Loading...' : 'Refresh'}
            </Button>

            {/* Recycle Bin Toggle */}
            <Button
              variant={showRecycleBin ? 'destructive' : 'outline'}
              size="sm"
              onClick={() => setShowRecycleBin(!showRecycleBin)}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {showRecycleBin ? 'Exit Recycle Bin' : 'Recycle Bin'}
              {!showRecycleBin && (deletedFiles.length + deletedFolders.length) > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {deletedFiles.length + deletedFolders.length}
                </Badge>
              )}
            </Button>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>

            {/* File Upload - Only show when not in recycle bin */}
            {!showRecycleBin && (
              <div className="relative">
                <input
                  type="file"
                  multiple
                  onChange={handleFileUpload}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx"
                  disabled={isUploading}
                />
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                  disabled={isUploading}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {isUploading ? 'Uploading...' : 'Upload Files'}
                </Button>
              </div>
            )}

            {/* New Folder - Only show when not in recycle bin */}
            {!showRecycleBin && (
              <Dialog open={showNewFolderDialog} onOpenChange={setShowNewFolderDialog}>
                <DialogTrigger asChild>
                  <Button size="sm" className="bg-green-600 hover:bg-green-700">
                    <FolderPlus className="w-4 h-4 mr-2" />
                    New Folder
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Create New Folder</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="folderName">Folder Name</Label>
                      <Input
                        id="folderName"
                        value={newFolderName}
                        onChange={(e) => setNewFolderName(e.target.value)}
                        placeholder="Enter folder name..."
                        onKeyPress={(e) => e.key === 'Enter' && createNewFolder()}
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowNewFolderDialog(false)}>
                        Cancel
                      </Button>
                      <Button onClick={createNewFolder}>
                        Create Folder
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>

        {/* Breadcrumb Navigation */}
        {!showRecycleBin && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentFolder(null)}
                  className="text-blue-600 hover:text-blue-700"
                >
                  Home
                </Button>
                
                {breadcrumb.map((folder, index) => (
                  <div key={folder.id} className="flex items-center gap-2">
                    <span className="text-gray-400">/</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigateToBreadcrumb(folder)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      {folder.name}
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder={showRecycleBin ? "Search deleted files and folders..." : "Search files and folders..."}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              {!showRecycleBin && currentFolder && (
                <Button variant="outline" onClick={navigateToParent}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Parent
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>
                {showRecycleBin ? 'Recycle Bin' : (currentFolder ? currentFolder.name : 'Home')} 
                <span className="text-sm text-gray-500 ml-2">
                  ({displayFiles.length} files, {displayFolders.length} folders)
                </span>
              </CardTitle>
              
              {displayFiles.length > 0 && (
                <Select value={fileViewMode} onValueChange={(value: 'small' | 'large' | 'list') => setFileViewMode(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                    <SelectItem value="list">List</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {displayFiles.length === 0 && displayFolders.length === 0 ? (
              <div className="text-center py-12">
                {showRecycleBin ? (
                  <>
                    <Trash2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Recycle Bin is Empty
                    </h3>
                    <p className="text-gray-500 mb-4">
                      No deleted files or folders to restore.
                    </p>
                  </>
                ) : (
                  <>
                    <Folder className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {currentFolder ? 'This folder is empty' : 'No files or folders yet'}
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {currentFolder 
                        ? 'Upload files or create subfolders to get started.'
                        : 'Upload files or create folders to organize your media.'
                      }
                    </p>
                    <div className="flex justify-center gap-2">
                      <div className="relative">
                        <input
                          type="file"
                          multiple
                          onChange={handleFileUpload}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                          accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx"
                          disabled={isUploading}
                        />
                        <Button
                          className="bg-blue-600 hover:bg-blue-700"
                          disabled={isUploading}
                        >
                          <Upload className="w-4 h-4 mr-2" />
                          {isUploading ? 'Uploading...' : 'Upload Files'}
                        </Button>
                      </div>
                      <Button 
                        variant="outline" 
                        onClick={() => setShowNewFolderDialog(true)}
                      >
                        <FolderPlus className="w-4 h-4 mr-2" />
                        New Folder
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ) : (
              <ScrollArea className="h-96">
                <div className={viewMode === 'grid' ? 'grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4' : 'space-y-2'}>
                  {/* Folders */}
                  {displayFolders.map(folder => (
                    <div
                      key={folder.id}
                      className={`${
                        viewMode === 'grid' 
                          ? 'p-4 border rounded-lg hover:bg-gray-50 cursor-pointer relative group' 
                          : 'flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer relative group'
                      }`}
                      onClick={() => openFolder(folder)}
                    >
                      {/* Action Buttons */}
                      <div className={`absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity ${
                        viewMode === 'grid' ? 'flex gap-1' : 'flex gap-1'
                      }`}>
                        {showRecycleBin ? (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                restoreFolder(folder.id);
                              }}
                              className="h-6 w-6 p-0 bg-green-100 hover:bg-green-200 text-green-700"
                            >
                              <span className="text-xs">↻</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                permanentlyDeleteFolder(folder.id);
                              }}
                              className="h-6 w-6 p-0 bg-red-100 hover:bg-red-200 text-red-700"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteFolder(folder.id);
                            }}
                            className="h-6 w-6 p-0 bg-red-100 hover:bg-red-200 text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>

                      {viewMode === 'grid' ? (
                        <div className="text-center">
                          <div className="w-16 h-16 mx-auto mb-2 bg-blue-100 rounded-lg flex items-center justify-center">
                            {getFolderThumbnail(folder) ? (
                              <img 
                                src={getFolderThumbnail(folder)} 
                                alt={folder.name}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            ) : (
                              <Folder className="w-8 h-8 text-blue-600" />
                            )}
                          </div>
                          <div className="text-sm font-medium truncate">{folder.name}</div>
                          <div className="text-xs text-gray-500">
                            {folder.files.length} files, {folder.subfolders.length} folders
                          </div>
                          {showRecycleBin && folder.deletedAt && (
                            <div className="text-xs text-red-500 mt-1">
                              Deleted {new Date(folder.deletedAt).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      ) : (
                        <>
                          <Folder className="w-6 h-6 text-blue-600 mr-3" />
                          <div className="flex-1">
                            <div className="font-medium">{folder.name}</div>
                            <div className="text-sm text-gray-500">
                              {folder.files.length} files, {folder.subfolders.length} folders
                            </div>
                            {showRecycleBin && folder.deletedAt && (
                              <div className="text-xs text-red-500">
                                Deleted {new Date(folder.deletedAt).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  ))}

                  {/* Files */}
                  {displayFiles.map(file => (
                    <div
                      key={file.id}
                      className={`${
                        viewMode === 'grid' 
                          ? 'p-4 border rounded-lg hover:bg-gray-50 cursor-pointer relative group' 
                          : 'flex items-center p-3 border rounded-lg hover:bg-gray-50 cursor-pointer relative group'
                      }`}
                      onClick={() => openPreview(file)}
                    >
                      {/* Action Buttons */}
                      <div className={`absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity ${
                        viewMode === 'grid' ? 'flex gap-1' : 'flex gap-1'
                      }`}>
                        {showRecycleBin ? (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                restoreFile(file.id);
                              }}
                              className="h-6 w-6 p-0 bg-green-100 hover:bg-green-200 text-green-700"
                            >
                              <span className="text-xs">↻</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                permanentlyDeleteFile(file.id);
                              }}
                              className="h-6 w-6 p-0 bg-red-100 hover:bg-red-200 text-red-700"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </>
                        ) : (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteFile(file.id);
                            }}
                            className="h-6 w-6 p-0 bg-red-100 hover:bg-red-200 text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        )}
                      </div>

                      {viewMode === 'grid' ? (
                        <div className="text-center">
                          <div className={`${getThumbnailSize()} mx-auto mb-2 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden`}>
                            {file.thumbnail ? (
                              <img 
                                src={file.thumbnail} 
                                alt={file.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              getFileIcon(file.type)
                            )}
                          </div>
                          <div className="text-sm font-medium truncate">{file.name}</div>
                          <div className="text-xs text-gray-500">{formatFileSize(file.size)}</div>
                          {showRecycleBin && file.deletedAt && (
                            <div className="text-xs text-red-500 mt-1">
                              Deleted {new Date(file.deletedAt).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      ) : (
                        <>
                          <div className={`${getThumbnailSize()} flex items-center justify-center overflow-hidden bg-gray-100 rounded`}>
                            {file.thumbnail ? (
                              <img 
                                src={file.thumbnail} 
                                alt={file.name}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              getFileIcon(file.type)
                            )}
                          </div>
                          <div className="flex-1 ml-3">
                            <div className="font-medium">{file.name}</div>
                            <div className="text-sm text-gray-500">
                              {formatFileSize(file.size)} • {new Date(file.uploadedAt).toLocaleDateString()}
                            </div>
                            {showRecycleBin && file.deletedAt && (
                              <div className="text-xs text-red-500">
                                Deleted {new Date(file.deletedAt).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                          <Badge variant="secondary" className={getFileTypeColor(file.type)}>
                            {file.type.toUpperCase()}
                          </Badge>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Preview Modal */}
      {showPreview && previewFile && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50">
              <div className="flex items-center gap-2">
                {getFileIcon(previewFile.type)}
                <div>
                  <h3 className="font-medium">{previewFile.name}</h3>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(previewFile.size)} • {new Date(previewFile.uploadedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={closePreview}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="relative">
              {previewFile.type === 'image' ? (
                <img 
                  src={previewFile.url} 
                  alt={previewFile.name}
                  className="max-w-full max-h-[70vh] object-contain mx-auto"
                />
              ) : previewFile.type === 'pdf' ? (
                <div className="w-full h-[70vh] flex items-center justify-center bg-gray-100">
                  <div className="text-center">
                    <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">PDF Preview</p>
                    <p className="text-sm text-gray-500">{previewFile.name}</p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => window.open(previewFile.url, '_blank')}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download PDF
                    </Button>
                  </div>
                </div>
              ) : null}

              {/* Navigation Arrows */}
              <Button
                variant="ghost"
                size="sm"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100"
                onClick={() => navigatePreview('prev')}
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-white bg-opacity-80 hover:bg-opacity-100"
                onClick={() => navigatePreview('next')}
              >
                <ChevronRight className="w-6 h-6" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaAttachments; 