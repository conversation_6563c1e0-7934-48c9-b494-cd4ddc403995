import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Upload,
  Search,
  Filter,
  Grid,
  List,
  Folder,
  File,
  Image,
  Video,
  Music,
  FileText,
  Trash2,
  Download,
  Eye,
  Plus,
  FolderOpen
} from 'lucide-react';

export interface MediaFile {
  id: string;
  name: string;
  filename: string;
  path: string;
  size: number;
  type: 'image' | 'video' | 'document' | 'audio' | 'other';
  mimeType: string;
  folder: string;
  uploadedAt: string;
  url: string;
}

const MediaAttachments: React.FC = () => {
  const [files, setFiles] = useState<Record<string, MediaFile[]>>({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [uploading, setUploading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    try {
      setLoading(true);

      // Use the proper API URL for production
      const apiUrl = window.location.hostname === 'localhost'
        ? '/api/files.php'
        : 'https://saamrajyam.com/api/files.php';

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error('Failed to fetch files');
      }

      const data = await response.json();

      if (data.success) {
        setFiles(data.files || {});
      } else {
        throw new Error(data.error || 'Failed to load files');
      }
    } catch (error) {
      console.error('Error loading files:', error);
      toast({
        title: "Error",
        description: `Failed to load files: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
      // Initialize with empty state on error
      setFiles({});
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadFiles = event.target.files;
    if (!uploadFiles || uploadFiles.length === 0) return;

    setUploading(true);
    const folder = selectedFolder || 'Default';

    try {
      for (const file of Array.from(uploadFiles)) {
        // Convert file to base64
        const base64Data = await fileToBase64(file);

        // Use the proper API URL for production
        const apiUrl = window.location.hostname === 'localhost'
          ? '/api/files.php'
          : 'https://saamrajyam.com/api/files.php';

        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            filename: file.name,
            mimeType: file.type,
            data: base64Data,
            folder: folder
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error || `Failed to upload ${file.name}`);
        }
      }

      toast({
        title: "Success",
        description: `${uploadFiles.length} file(s) uploaded successfully`,
      });

      // Reload files
      await loadFiles();
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      // Reset file input
      event.target.value = '';
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix to get just the base64 data
        const base64Data = result.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = error => reject(error);
    });
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return Image;
      case 'video': return Video;
      case 'audio': return Music;
      case 'document': return FileText;
      default: return File;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredFiles = React.useMemo(() => {
    let allFiles: MediaFile[] = [];

    if (selectedFolder) {
      allFiles = files[selectedFolder] || [];
    } else {
      // Flatten all files from all folders
      allFiles = Object.values(files).flat();
    }

    if (searchTerm) {
      allFiles = allFiles.filter(file =>
        file.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return allFiles;
  }, [files, selectedFolder, searchTerm]);

  const folderNames = Object.keys(files);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading files...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Media & Attachments</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>

          <input
            type="file"
            multiple
            onChange={handleFileUpload}
            className="hidden"
            id="file-upload"
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          />
          <Button
            onClick={() => document.getElementById('file-upload')?.click()}
            disabled={uploading}
          >
            <Upload className="w-4 h-4 mr-2" />
            {uploading ? 'Uploading...' : 'Upload Files'}
          </Button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={selectedFolder === null ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedFolder(null)}
          >
            All Files
          </Button>
          {folderNames.map(folder => (
            <Button
              key={folder}
              variant={selectedFolder === folder ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFolder(folder)}
            >
              <Folder className="w-4 h-4 mr-1" />
              {folder} ({files[folder]?.length || 0})
            </Button>
          ))}
        </div>
      </div>

      {/* Files Display */}
      {filteredFiles.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <FolderOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'No files found' : 'No files uploaded yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? `No files match "${searchTerm}"`
                : 'Upload your first files to get started'
              }
            </p>
            {!searchTerm && (
              <Button
                onClick={() => document.getElementById('file-upload')?.click()}
                disabled={uploading}
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Files
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className={viewMode === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
          : 'space-y-2'
        }>
          {filteredFiles.map((file) => {
            const FileIcon = getFileIcon(file.type);

            if (viewMode === 'grid') {
              return (
                <Card key={file.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <FileIcon className="w-8 h-8 text-blue-600" />
                      <Badge variant="secondary" className="text-xs">
                        {file.type}
                      </Badge>
                    </div>

                    <h3 className="font-medium text-sm mb-2 truncate" title={file.name}>
                      {file.name}
                    </h3>

                    <div className="text-xs text-gray-500 space-y-1">
                      <p>Size: {formatFileSize(file.size)}</p>
                      <p>Folder: {file.folder}</p>
                      <p>Uploaded: {new Date(file.uploadedAt).toLocaleDateString()}</p>
                    </div>

                    <div className="flex items-center gap-2 mt-3">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-3 h-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Download className="w-3 h-3" />
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            } else {
              return (
                <Card key={file.id} className="hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <FileIcon className="w-6 h-6 text-blue-600" />
                        <div>
                          <h3 className="font-medium text-sm">{file.name}</h3>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(file.size)} • {file.folder} • {new Date(file.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {file.type}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Eye className="w-3 h-3 mr-1" />
                          View
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }
          })}
        </div>
      )}
    </div>
  );
};

export default MediaAttachments;