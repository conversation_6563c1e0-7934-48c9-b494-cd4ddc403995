import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Contact } from '@/types/Contact';
import { contactService } from '@/services/contactService';
import { useContacts } from '@/contexts/ContactContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { OverviewCards } from '@/components/OverviewCards';
import { ContactSelector } from '@/components/whatsapp/ContactSelector';
import { MessageComposer } from '@/components/whatsapp/MessageComposer';
import { MessagePreview } from '@/components/whatsapp/MessagePreview';
import { MessageReview } from '@/components/whatsapp/MessageReview';
import { WhatsAppConnection } from '@/components/whatsapp/WhatsAppConnection';
import { BulkSender } from '@/components/whatsapp/BulkSender';
import { SentMessages } from '@/components/whatsapp/SentMessages';
import { 
  MessageCircle, 
  Users, 
  Edit3, 
  Eye, 
  Send, 
  CheckCircle, 
  ArrowLeft, 
  ArrowRight,
  Search,
  Filter,
  X,
  QrCode,
  History,
  Folder
} from 'lucide-react';
import { BackendUrlConfig } from '@/components/whatsapp/BackendUrlConfig';
import { LogoutButton } from '@/components/auth/LogoutButton';
import { whatsAppService } from '@/services/whatsappService';

export interface LinkPreview {
  url: string;
  title: string;
  description: string;
  image: string;
  siteName?: string;
  isEditable?: boolean;
}

export interface WhatsAppMessage {
  id: string;
  text: string;
  attachments: MessageAttachment[];
  links: LinkPreview[];
}

export interface MessageAttachment {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document' | 'audio';
  url: string;
  size: number;
}

export interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  category: string;
  createdAt: string;
  attachments: MessageAttachment[];
  links: LinkPreview[];
}

export interface SentMessage {
  id: string;
  contactId: string;
  contactName: string;
  contactPhone: string;
  message: WhatsAppMessage;
  status: 'sent' | 'failed' | 'not_delivered' | 'contact_not_found' | 'network_error' | 'rate_limited';
  sentAt: string;
  retryCount: number;
  error?: string;
}

const WhatsAppSender: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { contacts, isLoading: isLoadingContacts } = useContacts();
  
  const [currentStep, setCurrentStep] = useState<'connect' | 'select' | 'compose' | 'review' | 'send' | 'history'>('select');
  const [isConnected, setIsConnected] = useState(false);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [composedMessage, setComposedMessage] = useState<WhatsAppMessage>({
    id: '',
    text: '',
    attachments: [],
    links: []
  });
  const [sentMessages, setSentMessages] = useState<SentMessage[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [sendProgress, setSendProgress] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentFilter, setCurrentFilter] = useState<'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned'>('all');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStates, setSelectedStates] = useState<string[]>([]);
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [selectedBuildTypes, setSelectedBuildTypes] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string; end: string } | null>(null);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const [backendConnected, setBackendConnected] = useState(false);

  // Add timeout for loading state to prevent infinite loading
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoadingContacts) {
        setLoadingTimeout(true);
        console.log('⚠️ Loading timeout reached, proceeding without contacts');
      }
    }, 5000); // Reduced to 5 seconds

    return () => clearTimeout(timer);
  }, [isLoadingContacts]);

  // Initialize contacts if not loaded when component mounts
  useEffect(() => {
    const initializeContacts = async () => {
      if (contacts.length === 0 && !isLoadingContacts && !loadingTimeout) {
        console.log('🔄 WhatsApp Sender: No contacts loaded, initializing...');
        try {
          // Force a contact refresh if needed
          const { refreshContacts } = useContacts();
          await refreshContacts();
        } catch (error) {
          console.error('❌ Failed to initialize contacts in WhatsApp Sender:', error);
          setLoadingTimeout(true);
        }
      }
    };

    // Delay initialization to avoid race conditions
    const timer = setTimeout(initializeContacts, 1000);
    return () => clearTimeout(timer);
  }, [contacts.length, isLoadingContacts, loadingTimeout]);

  // Check WhatsApp connection status on component mount
  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Check backend connection
        const backendResult = await whatsAppService.testBackendConnection();
        setBackendConnected(backendResult.success);
        
        // Check actual WhatsApp connection status
        if (backendResult.success) {
          const whatsappStatus = await whatsAppService.getStatus();
          setIsConnected(whatsappStatus.isConnected);
        } else {
          setIsConnected(false);
        }
      } catch (error) {
        console.error('Error checking connections:', error);
        setBackendConnected(false);
        setIsConnected(false);
      }
    };

    checkConnection();

    // Set up periodic connection check every 10 seconds
    const interval = setInterval(checkConnection, 10000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  // Filter contacts based on all criteria
  useEffect(() => {
    let filtered = contacts;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.phone.includes(searchTerm) ||
        contact.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
        contact.state.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply preset filters
    switch (currentFilter) {
      case 'favorites':
        filtered = filtered.filter(c => c.isFavorite);
        break;
      case 'pinned':
        filtered = filtered.filter(c => c.isPinned);
        break;
      case 'shortlisted':
        filtered = filtered.filter(c => c.isShortlisted);
        break;
      case 'reminders':
        filtered = filtered.filter(c => c.hasActiveReminder);
        break;
    }

    // Apply advanced filters
    if (selectedCategories.length > 0) {
      filtered = filtered.filter(c => 
        c.categories.some(cat => selectedCategories.includes(cat))
      );
    }
    if (selectedStates.length > 0) {
      filtered = filtered.filter(c => selectedStates.includes(c.state));
    }
    if (selectedCities.length > 0) {
      filtered = filtered.filter(c => selectedCities.includes(c.city));
    }
    if (selectedBuildTypes.length > 0) {
      filtered = filtered.filter(c => selectedBuildTypes.includes(c.buildType || 'Residential'));
    }

    // Apply date range filter
    if (dateRange?.start && dateRange?.end) {
      filtered = filtered.filter(c => {
        const contactDate = new Date(c.date);
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);
        return contactDate >= startDate && contactDate <= endDate;
      });
    }

    setFilteredContacts(filtered);
  }, [contacts, searchTerm, currentFilter, selectedCategories, selectedStates, selectedCities, selectedBuildTypes, dateRange]);

  // Load pre-selected contacts from localStorage
  useEffect(() => {
    const savedContacts = localStorage.getItem('whatsappSenderContacts');
    if (savedContacts) {
      try {
        const parsedContacts = JSON.parse(savedContacts);
        if (parsedContacts.length > 0) {
          setSelectedContacts(parsedContacts);
          // Clear the localStorage after loading
          localStorage.removeItem('whatsappSenderContacts');
          
          toast({
            title: "Contact Loaded",
            description: parsedContacts.length === 1 
              ? `Ready to send message to ${parsedContacts[0].name}.`
              : `${parsedContacts.length} contact(s) have been added from the contact page.`,
          });
          
          // Automatically go to compose step if contacts are loaded
          setCurrentStep('compose');
        }
      } catch (error) {
        console.error('Error loading contacts from localStorage:', error);
      }
    }
  }, [toast]);

  // Load sent messages from localStorage on component mount
  useEffect(() => {
    try {
      const savedMessages = localStorage.getItem('whatsapp_sent_messages');
      if (savedMessages) {
        const parsedMessages = JSON.parse(savedMessages);
        setSentMessages(parsedMessages);
        console.log('✅ Loaded', parsedMessages.length, 'sent messages from localStorage');
      } else {
        // For testing - add some sample data if no messages exist
        console.log('⚠️ No messages found in localStorage - you can send some messages to test the history');
      }
    } catch (error) {
      console.error('❌ Error loading sent messages from localStorage:', error);
    }
  }, []);

  const handleConnectionSuccess = () => {
    setIsConnected(true);
    toast({
      title: "WhatsApp Connected",
      description: "You can now send messages to your contacts.",
    });
  };

  const handleContactsSelected = (contacts: Contact[]) => {
    setSelectedContacts(contacts);
    // Don't automatically go to compose step - let user click "Next" button
  };

  const handleContinueToComposer = () => {
    if (selectedContacts.length > 0) {
      setCurrentStep('compose');
    }
  };

  const handleMessageComposed = (composedMessage: WhatsAppMessage) => {
    setComposedMessage(composedMessage);
    setCurrentStep('review');
  };

  const handleReviewComplete = () => {
    setCurrentStep('send');
  };

  const handleSendComplete = async (results: SentMessage[]) => {
    setSentMessages(prev => [...prev, ...results]);
    setIsSending(false);
    setSendProgress(0);
    
    // Save messages to localStorage for persistence
    try {
      const existingMessages = localStorage.getItem('whatsapp_sent_messages');
      const allMessages = existingMessages ? JSON.parse(existingMessages) : [];
      const updatedMessages = [...allMessages, ...results];
      localStorage.setItem('whatsapp_sent_messages', JSON.stringify(updatedMessages));
      console.log('✅ Messages saved to localStorage:', results.length);
    } catch (error) {
      console.error('❌ Error saving messages to localStorage:', error);
    }
    
    const successCount = results.filter(r => r.status === 'sent').length;
    const failureCount = results.length - successCount;
    
    if (successCount > 0) {
      toast({
        title: "Messages Sent",
        description: `Successfully sent ${successCount} message(s)${failureCount > 0 ? `, ${failureCount} failed` : ''}.`,
      });
    }
      
    if (failureCount > 0) {
      toast({
        title: "Some Messages Failed",
        description: `${failureCount} message(s) failed to send. Check the results below for details.`,
        variant: "destructive",
      });
    }
    
    // Stay on the send page to show results - don't navigate away
    // setCurrentStep('select'); // Removed this line
  };

  const handleBackToStep = (step: typeof currentStep) => {
    setCurrentStep(step);
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const handleStateClick = (state: string) => {
    setSelectedStates(prev => 
      prev.includes(state) 
        ? prev.filter(s => s !== state)
        : [...prev, state]
    );
  };

  const handleCityClick = (city: string) => {
    setSelectedCities(prev => 
      prev.includes(city) 
        ? prev.filter(c => c !== city)
        : [...prev, city]
    );
  };

  const handleBuildTypeClick = (buildType: string) => {
    setSelectedBuildTypes(prev => 
      prev.includes(buildType) 
        ? prev.filter(b => b !== buildType)
        : [...prev, buildType]
    );
  };

  const handleQuickFilter = (filter: 'all' | 'favorites' | 'shortlisted' | 'reminders' | 'pinned') => {
    setCurrentFilter(filter);
  };

  const handleClearHistory = () => {
    setSentMessages([]);
    localStorage.removeItem('whatsapp_sent_messages');
    toast({
      title: "History Cleared",
      description: "All message history has been cleared.",
    });
  };

  const getBuildTypeColor = (buildType: string) => {
    const colors = [
      'bg-blue-100 text-blue-800 hover:bg-blue-200',
      'bg-green-100 text-green-800 hover:bg-green-200',
      'bg-purple-100 text-purple-800 hover:bg-purple-200',
      'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      'bg-pink-100 text-pink-800 hover:bg-pink-200',
      'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',
      'bg-red-100 text-red-800 hover:bg-red-200',
      'bg-teal-100 text-teal-800 hover:bg-teal-200',
    ];
    const index = buildType.length % colors.length;
    return colors[index];
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'connect':
        return (
          <WhatsAppConnection onConnectionSuccess={handleConnectionSuccess} />
        );
      case 'select':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Select Contacts</h2>
              <Button
                onClick={() => setShowFilters(!showFilters)}
                variant="outline"
                size="sm"
              >
                <Filter className="w-4 h-4 mr-2" />
                {showFilters ? 'Hide' : 'Show'} Filters
              </Button>
            </div>
            
            {showFilters && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Filters</CardTitle>
                  </CardHeader>
                  <CardContent>
                  <div className="space-y-4">
                    {/* Quick Filters */}
                    <div>
                      <h3 className="text-sm font-medium mb-2">Quick Filters</h3>
                      <div className="flex flex-wrap gap-2">
                        {(['all', 'favorites', 'shortlisted', 'reminders', 'pinned'] as const).map((filter) => (
                          <Button
                            key={filter}
                            variant={currentFilter === filter ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleQuickFilter(filter)}
                          >
                            {filter.charAt(0).toUpperCase() + filter.slice(1)}
                          </Button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Search */}
                    <div>
                      <h3 className="text-sm font-medium mb-2">Search</h3>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <Input
                          placeholder="Search contacts..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                  </div>
                  </CardContent>
                </Card>
            )}
            
                    <ContactSelector
                      contacts={filteredContacts}
                      selectedContacts={selectedContacts}
                      onContactsSelected={handleContactsSelected}
                      onContinueToComposer={handleContinueToComposer}
                      sentMessages={sentMessages}
                      onBack={() => setCurrentStep('select')}
                      isLoading={isLoadingContacts && !loadingTimeout}
                    />
            
            {selectedContacts.length > 0 && (
              <div className="flex justify-end">
                <Button onClick={handleContinueToComposer} size="lg">
                  Continue to Compose
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            )}
          </div>
        );
      case 'compose':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Compose Message</h2>
              <Button
                variant="outline"
                onClick={() => setCurrentStep('select')}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Selection
              </Button>
            </div>
            
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <MessageComposer
                message={composedMessage}
                onMessageChange={setComposedMessage}
              onCompose={handleMessageComposed}
              onBack={() => setCurrentStep('select')}
              selectedContacts={selectedContacts}
            />
              <MessagePreview message={composedMessage} />
            </div>
          </div>
        );
      case 'review':
        return (
          <MessageReview
            message={composedMessage}
            selectedContacts={selectedContacts}
            onConfirm={handleReviewComplete}
            onBack={() => setCurrentStep('compose')}
            isConnected={isConnected}
          />
        );
      case 'send':
        return (
          <BulkSender
            message={composedMessage}
            contacts={selectedContacts}
            onSendComplete={handleSendComplete}
            onBack={() => setCurrentStep('review')}
          />
        );
      case 'history':
        return (
          <SentMessages
            sentMessages={sentMessages}
            onStartNew={() => setCurrentStep('select')}
            onBack={() => setCurrentStep('select')}
            onClearHistory={handleClearHistory}
          />
        );
      default:
        return null;
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 'connect': return 'Connect WhatsApp';
      case 'select': return 'Select Contacts';
      case 'compose': return 'Compose Message';
      case 'review': return 'Review & Send';
      case 'send': return 'Sending Messages';
      case 'history': return 'Message History';
      default: return 'WhatsApp Sender';
    }
  };

  const getStepNumber = () => {
    switch (currentStep) {
      case 'connect': return 1;
      case 'select': return 2;
      case 'compose': return 3;
      case 'review': return 4;
      case 'send': return 5;
      default: return 1;
    }
  };

  const canGoToStep = (step: typeof currentStep) => {
    if (step === 'select') return true;
    if (step === 'compose' && selectedContacts.length > 0) return true;
    if (step === 'review' && composedMessage?.text.trim()) return true;
    
    return true;
  };

  useEffect(() => {
    return () => {
      // Cleanup if needed
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6 flex-wrap gap-4">
          <div className="flex items-center gap-4 flex-wrap">
            <Link to="/">
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2"
                size="lg"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Contacts
              </Button>
            </Link>
            <div className="flex items-center gap-2">
              <MessageCircle className="w-6 h-6 text-green-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                WhatsApp Sender
              </h1>
            </div>
          </div>
          
          {/* Connection Status & Quick History Access */}
          <div className="flex items-center gap-3 flex-wrap">
            {/* WhatsApp Connection Status - Matching Backend Setup Style */}
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-gray-600">
                WhatsApp: {isConnected ? 'Connected' : 'Not Connected'}
              </span>
            </div>
            
            <BackendUrlConfig />
            
            <Button
              variant="outline"
              className="flex items-center gap-2 text-sm"
              onClick={() => setCurrentStep('history')}
            >
              <History className="w-4 h-4" />
              History ({sentMessages.length})
            </Button>
            <Link to="/media-attachments">
              <Button
                variant="outline"
                className="flex items-center gap-2 text-sm"
              >
                <Folder className="w-4 h-4" />
                Media
              </Button>
            </Link>
            
            <LogoutButton />
          </div>
        </div>

        {/* Step Navigation */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {[
                  { key: 'connect', icon: QrCode, label: 'Connect', number: 1 },
                  { key: 'select', icon: Users, label: 'Select Contacts', number: 2 },
                  { key: 'compose', icon: MessageCircle, label: 'Compose', number: 3 },
                  { key: 'review', icon: Send, label: 'Review', number: 4 },
                  { key: 'send', icon: CheckCircle, label: 'Send', number: 5 },
                ].map((step, index) => {
                  const isActive = currentStep === step.key;
                  const isCompleted = getStepNumber() > step.number;
                  const canAccess = canGoToStep(step.key as typeof currentStep);
                  
                  return (
                    <div key={step.key} className="flex items-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => canAccess && setCurrentStep(step.key as typeof currentStep)}
                        disabled={!canAccess}
                        className={`flex items-center gap-2 ${
                          isActive ? 'bg-green-100 text-green-700 border border-green-300' :
                          isCompleted ? 'text-green-600' :
                          canAccess ? 'hover:bg-gray-100' :
                          'text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        <div className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium ${
                          isActive ? 'bg-green-600 text-white' :
                          isCompleted ? 'bg-green-600 text-white' :
                          'bg-gray-300 text-gray-600'
                        }`}>
                          {isCompleted ? '✓' : step.number}
                        </div>
                        <span className="hidden sm:inline">{step.label}</span>
                      </Button>
                      {index < 4 && (
                        <div className={`w-8 h-px mx-2 ${
                          isCompleted ? 'bg-green-600' : 'bg-gray-300'
                        }`} />
                      )}
                    </div>
                  );
                })}
              </div>
              
              <div className="text-sm text-gray-600">
                Step {getStepNumber()} of 5
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {currentStep === 'select' ? (
          renderStepContent()
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>{getStepTitle()}</CardTitle>
            </CardHeader>
            <CardContent>
              {renderStepContent()}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default WhatsAppSender;
