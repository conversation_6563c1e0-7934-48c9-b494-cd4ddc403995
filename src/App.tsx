import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ContactProvider } from "@/contexts/ContactContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Index from "./pages/Index";
import WhatsAppSender from "./pages/WhatsAppSender";
import WhatsAppHistory from "./pages/WhatsAppHistory";
import NotFound from "./pages/NotFound";
import MediaAttachments from "./pages/MediaAttachments";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <ProtectedRoute>
          <ContactProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/whatsapp-sender" element={<WhatsAppSender />} />
                <Route path="/whatsapp-history" element={<WhatsAppHistory />} />
                <Route path="/media-attachments" element={<MediaAttachments />} />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </ContactProvider>
        </ProtectedRoute>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
