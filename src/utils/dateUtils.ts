// Date utility functions for Contact Sphere Organizer

/**
 * Parse various date formats and return ISO date string (YYYY-MM-DD)
 */
export const parseDate = (dateString: string): string => {
  if (!dateString || dateString.trim() === '') {
    return new Date().toISOString().split('T')[0];
  }

  const trimmedDate = dateString.trim();
  
  // Try to parse Excel date numbers (Excel stores dates as numbers)
  if (!isNaN(Number(trimmedDate)) && Number(trimmedDate) > 1000) {
    try {
      // Excel dates are days since 1900-01-01
      const excelDate = new Date((Number(trimmedDate) - 25569) * 86400 * 1000);
      return excelDate.toISOString().split('T')[0];
    } catch (error) {
      console.warn('Failed to parse Excel date:', trimmedDate);
    }
  }

  // Common date formats
  const dateFormats = [
    // YYYY-MM-DD
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
    // DD/MM/YYYY
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // MM/DD/YYYY
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // DD-MM-YYYY
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
    // MM-DD-YYYY
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
    // YYYY/MM/DD
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
    // DD.MM.YYYY
    /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
    // MM.DD.YYYY
    /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/,
  ];

  for (const format of dateFormats) {
    const match = trimmedDate.match(format);
    if (match) {
      const [, first, second, third] = match;
      let year, month, day;

      // Determine format based on position
      if (first.length === 4) {
        // YYYY-MM-DD or YYYY/MM/DD
        year = parseInt(first);
        month = parseInt(second);
        day = parseInt(third);
      } else if (third.length === 4) {
        // DD/MM/YYYY, MM/DD/YYYY, DD-MM-YYYY, MM-DD-YYYY, DD.MM.YYYY, MM.DD.YYYY
        year = parseInt(third);
        // Try to determine if it's DD/MM or MM/DD format
        const firstNum = parseInt(first);
        const secondNum = parseInt(second);
        
        // If first number is > 12, it's likely DD/MM format
        if (firstNum > 12) {
          day = firstNum;
          month = secondNum;
        } else {
          // Assume MM/DD format
          month = firstNum;
          day = secondNum;
        }
      } else {
        continue; // Skip this format
      }

      // Validate date
      if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
        const date = new Date(year, month - 1, day);
        if (date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day) {
          return date.toISOString().split('T')[0];
        }
      }
    }
  }

  // If no format matches, try native Date parsing
  try {
    const date = new Date(trimmedDate);
    if (!isNaN(date.getTime())) {
      return date.toISOString().split('T')[0];
    }
  } catch (error) {
    console.warn('Failed to parse date:', trimmedDate);
  }

  // Return current date as fallback
  console.warn(`Could not parse date: "${trimmedDate}". Using current date.`);
  return new Date().toISOString().split('T')[0];
};

/**
 * Format date for display based on user preference
 */
export const formatDateForDisplay = (dateString: string, format: 'local' | 'dd/mm/yyyy' | 'mm/dd/yyyy' | 'yyyy-mm-dd' = 'local'): string => {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    switch (format) {
      case 'dd/mm/yyyy':
        return date.toLocaleDateString('en-GB');
      case 'mm/dd/yyyy':
        return date.toLocaleDateString('en-US');
      case 'yyyy-mm-dd':
        return date.toISOString().split('T')[0];
      case 'local':
      default:
        return date.toLocaleDateString();
    }
  } catch (error) {
    console.warn('Error formatting date:', error);
    return dateString;
  }
};

/**
 * Get supported date formats for import
 */
export const getSupportedDateFormats = () => [
  'YYYY-MM-DD (e.g., 2024-01-15)',
  'DD/MM/YYYY (e.g., 15/01/2024)',
  'MM/DD/YYYY (e.g., 01/15/2024)',
  'DD-MM-YYYY (e.g., 15-01-2024)',
  'MM-DD-YYYY (e.g., 01-15-2024)',
  'YYYY/MM/DD (e.g., 2024/01/15)',
  'DD.MM.YYYY (e.g., 15.01.2024)',
  'MM.DD.YYYY (e.g., 01.15.2024)',
  'Excel date numbers'
];

/**
 * Validate if a date string is in a supported format
 */
export const isValidDate = (dateString: string): boolean => {
  if (!dateString || dateString.trim() === '') return false;
  
  const parsed = parseDate(dateString);
  const original = dateString.trim();
  
  // If parsing didn't change the string and it's already in YYYY-MM-DD format, it's valid
  if (parsed === original && /^\d{4}-\d{2}-\d{2}$/.test(original)) {
    return true;
  }
  
  // If parsing produced a different result, the original was in a different format
  return parsed !== new Date().toISOString().split('T')[0];
}; 