<?php
/**
 * Database Connection Test Script
 * This script tests the database connection and API functionality
 */

echo "=== Database Connection Test ===\n";

try {
    require_once 'database/config.php';
    
    echo "1. Testing database connection...\n";
    $db = Database::getInstance();
    echo "✅ Database connection successful!\n\n";
    
    echo "2. Testing contacts table...\n";
    $contacts = $db->fetchAll("SELECT COUNT(*) as count FROM contacts");
    echo "✅ Contacts table accessible. Total contacts: " . $contacts[0]['count'] . "\n\n";
    
    echo "3. Testing files table...\n";
    // Try to create files table if it doesn't exist
    $db->execute("
        CREATE TABLE IF NOT EXISTS files (
            id INT AUTO_INCREMENT PRIMARY KEY,
            filename VARCHAR(255) NOT NULL,
            original_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size BIGINT NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            file_type ENUM('image', 'video', 'document', 'audio', 'other') NOT NULL,
            folder VARCHAR(100) DEFAULT 'Default',
            uploaded_by INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_file_type (file_type),
            INDEX idx_folder (folder)
        )
    ");
    
    $files = $db->fetchAll("SELECT COUNT(*) as count FROM files");
    echo "✅ Files table accessible. Total files: " . $files[0]['count'] . "\n\n";
    
    echo "4. Testing categories table...\n";
    $categories = $db->fetchAll("SELECT COUNT(*) as count FROM categories");
    echo "✅ Categories table accessible. Total categories: " . $categories[0]['count'] . "\n\n";
    
    echo "=== All tests passed! ===\n";
    echo "Database is ready for use.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Database connection failed!\n";
}
?>
