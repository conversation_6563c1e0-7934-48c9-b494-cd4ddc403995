# 🗄️ MySQL Database Setup Guide for Contact Sphere Organizer

This guide will help you set up the MySQL database for your Contact Sphere Organizer project on shared hosting.

## 📋 **Prerequisites**

1. **Shared Hosting Account** with MySQL database support
2. **cPanel** or similar hosting control panel access
3. **Database credentials** from your hosting provider
4. **File Manager** access or FTP client

## 🚀 **Step-by-Step Setup**

### **Step 1: Create Database in cPanel**

1. **Login to cPanel**
   - Go to your hosting provider's login page
   - Enter your username and password
   - Access cPanel

2. **Create MySQL Database**
   - In cPanel, find "**MySQL Databases**" or "**Databases**"
   - Click on it to open the database management section

3. **Create New Database**
   - Enter database name: `contact_sphere_organizer`
   - Click "**Create Database**"
   - Note down the full database name (usually includes your username prefix)

4. **Create Database User**
   - Go to "**MySQL Users**" section
   - Create a new user with:
     - Username: `contact_user` (or your preferred name)
     - Password: Generate a strong password
   - Click "**Create User**"

5. **Add User to Database**
   - Go to "**Add User To Database**"
   - Select your database and user
   - Grant "**ALL PRIVILEGES**"
   - Click "**Add**"

### **Step 2: Upload Database Files**

1. **Access File Manager**
   - In cPanel, find "**File Manager**"
   - Click to open it

2. **Navigate to Your Website**
   - Go to `public_html` or your website's root directory
   - Create a folder called `database` if it doesn't exist

3. **Upload Schema File**
   - Upload `database/schema.sql` to your `database` folder
   - Upload `database/config.php` to your `database` folder

### **Step 3: Import Database Schema**

1. **Access phpMyAdmin**
   - In cPanel, find "**phpMyAdmin**"
   - Click to open it

2. **Select Your Database**
   - From the left sidebar, click on your database name
   - Make sure you're in the correct database

3. **Import Schema**
   - Click the "**Import**" tab
   - Click "**Choose File**" and select `schema.sql`
   - Click "**Go**" to import
   - Wait for the import to complete

4. **Verify Import**
   - Check that all tables were created:
     - `contacts`
     - `categories`
     - `contact_categories`
     - `files`
     - `contact_files`
     - `reminders`
     - `whatsapp_messages`
     - `message_attachments`
     - `message_templates`
     - `template_attachments`
     - `activity_log`
     - `settings`
     - `users`

### **Step 4: Configure Database Connection**

1. **Edit Config File**
   - In File Manager, open `database/config.php`
   - Update the database credentials:

```php
// Replace these with your actual database details
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_username_contact_sphere_organizer'); // Your full database name
define('DB_USER', 'your_username_contact_user'); // Your database username
define('DB_PASS', 'your_database_password'); // Your database password
```

2. **Update JWT Secret**
   - Change the JWT secret to a unique value:
```php
define('JWT_SECRET', 'your-unique-secret-key-here');
```

### **Step 5: Create Upload Directory**

1. **Create Uploads Folder**
   - In File Manager, go to your website root
   - Create a folder called `uploads`
   - Set permissions to `755`

2. **Create Subdirectories**
   - Inside `uploads`, create these folders:
     - `images`
     - `videos`
     - `documents`
     - `audio`
     - `temp`
   - Set all permissions to `755`

### **Step 6: Upload API Files**

1. **Create API Folder**
   - In your website root, create a folder called `api`

2. **Upload API Files**
   - Upload all PHP API files to the `api` folder:
     - `contacts.php`
     - `categories.php`
     - `files.php`
     - `reminders.php`
     - `messages.php`
     - `templates.php`
     - `export.php`

### **Step 7: Test Database Connection**

1. **Create Test File**
   - Create a file called `test_db.php` in your website root:

```php
<?php
require_once 'database/config.php';

try {
    $db = Database::getInstance();
    echo "✅ Database connection successful!<br>";
    
    // Test query
    $result = $db->fetchOne("SELECT COUNT(*) as count FROM contacts");
    echo "✅ Contacts table accessible. Count: " . $result['count'] . "<br>";
    
    // Test categories
    $categories = $db->fetchAll("SELECT * FROM categories");
    echo "✅ Categories loaded: " . count($categories) . " categories<br>";
    
    echo "<br>🎉 Database setup is complete!";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
```

2. **Test the Connection**
   - Visit `yourdomain.com/test_db.php`
   - If you see success messages, your database is working
   - **Delete this test file** after confirming it works

## 🔧 **Troubleshooting**

### **Common Issues:**

1. **"Access denied" Error**
   - Check database username and password
   - Verify user has proper permissions
   - Ensure database name is correct

2. **"Table doesn't exist" Error**
   - Re-import the schema file
   - Check if import completed successfully
   - Verify you're in the correct database

3. **"Connection failed" Error**
   - Check if MySQL is enabled on your hosting
   - Verify database host (usually 'localhost')
   - Contact hosting support if needed

4. **"Permission denied" Error**
   - Set upload directory permissions to 755
   - Check file ownership
   - Contact hosting support for permission issues

### **Security Checklist:**

- ✅ Change default JWT secret
- ✅ Use strong database password
- ✅ Set proper file permissions
- ✅ Delete test files after setup
- ✅ Enable error logging in production
- ✅ Use HTTPS for your website

## 📊 **Database Structure Overview**

### **Main Tables:**
- **`contacts`** - Store all contact information
- **`categories`** - Contact categories and tags
- **`files`** - Uploaded files and attachments
- **`reminders`** - Follow-up reminders and tasks
- **`whatsapp_messages`** - WhatsApp message history
- **`message_templates`** - Reusable message templates
- **`activity_log`** - User activity tracking
- **`settings`** - Application configuration

### **Relationship Tables:**
- **`contact_categories`** - Links contacts to categories
- **`contact_files`** - Links contacts to files
- **`message_attachments`** - Links messages to files
- **`template_attachments`** - Links templates to files

## 🎯 **Next Steps**

After completing the database setup:

1. **Update Frontend Configuration**
   - Update API endpoints in your React app
   - Point to your hosted PHP API

2. **Test All Features**
   - Add a test contact
   - Upload a test file
   - Send a test WhatsApp message

3. **Backup Strategy**
   - Set up regular database backups
   - Export data periodically

4. **Monitoring**
   - Monitor database performance
   - Check error logs regularly

## 📞 **Need Help?**

If you encounter issues:

1. **Check hosting documentation** for MySQL setup
2. **Contact hosting support** for database issues
3. **Review error logs** in cPanel
4. **Test with simple PHP scripts** first

---

**🎉 Congratulations!** Your MySQL database is now set up and ready to store all your contact data, files, and WhatsApp messages. 