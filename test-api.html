<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Files API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Files API Test</h1>
    
    <div class="section">
        <h2>Test Database Connection</h2>
        <button onclick="testGetFiles()">Get Files from Database</button>
        <div id="getFilesResult"></div>
    </div>
    
    <div class="section">
        <h2>Test File Creation</h2>
        <button onclick="testCreateFile()">Create Test File Record</button>
        <div id="createFileResult"></div>
    </div>
    
    <div class="section">
        <h2>Test File Deletion</h2>
        <input type="number" id="deleteFileId" placeholder="File ID to delete">
        <button onclick="testDeleteFile()">Delete File</button>
        <div id="deleteFileResult"></div>
    </div>

    <script>
        async function testGetFiles() {
            const resultDiv = document.getElementById('getFilesResult');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch('https://saamrajyam.com/api/files.php');
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Success!</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Error: ${response.status}</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Network Error: ${error.message}</div>
                `;
            }
        }
        
        async function testCreateFile() {
            const resultDiv = document.getElementById('createFileResult');
            resultDiv.innerHTML = 'Creating...';
            
            const testFile = {
                filename: 'test-file-' + Date.now() + '.txt',
                original_name: 'Test File.txt',
                file_path: '/uploads/Documents/test-file-' + Date.now() + '.txt',
                file_size: 1024,
                mime_type: 'text/plain'
            };
            
            try {
                const response = await fetch('https://saamrajyam.com/api/files.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testFile)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ File created successfully!</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Error: ${response.status}</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Network Error: ${error.message}</div>
                `;
            }
        }
        
        async function testDeleteFile() {
            const fileId = document.getElementById('deleteFileId').value;
            const resultDiv = document.getElementById('deleteFileResult');
            
            if (!fileId) {
                resultDiv.innerHTML = '<div class="error">❌ Please enter a file ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Deleting...';
            
            try {
                const response = await fetch(`https://saamrajyam.com/api/files.php?id=${fileId}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ File deleted successfully!</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Error: ${response.status}</div>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">❌ Network Error: ${error.message}</div>
                `;
            }
        }
    </script>
</body>
</html>
