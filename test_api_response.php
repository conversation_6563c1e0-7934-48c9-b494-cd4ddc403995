<?php
/**
 * API Response Test - Debug what the files API is actually returning
 */

echo "<h2>🔍 API Response Test</h2>";

// Test the files API endpoint
$apiUrl = 'https://saamrajyam.com/api/files.php';

echo "<h3>Testing: $apiUrl</h3>";

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HEADER, true); // Include headers in output

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

if (curl_error($ch)) {
    echo "<p style='color: red;'>❌ cURL Error: " . curl_error($ch) . "</p>";
} else {
    // Split headers and body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "<h4>📊 Response Details:</h4>";
    echo "<p><strong>HTTP Status Code:</strong> $httpCode</p>";
    
    echo "<h4>📋 Response Headers:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($headers);
    echo "</pre>";
    
    echo "<h4>📄 Response Body:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($body);
    echo "</pre>";
    
    echo "<h4>🔍 JSON Validation:</h4>";
    $jsonData = json_decode($body, true);
    $jsonError = json_last_error();
    
    if ($jsonError === JSON_ERROR_NONE) {
        echo "<p style='color: green;'>✅ Valid JSON response</p>";
        echo "<h5>Parsed JSON:</h5>";
        echo "<pre style='background: #e8f5e8; padding: 10px; border: 1px solid #4CAF50;'>";
        echo htmlspecialchars(json_encode($jsonData, JSON_PRETTY_PRINT));
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
        echo "<p><strong>JSON Error:</strong> ";
        
        switch ($jsonError) {
            case JSON_ERROR_DEPTH:
                echo "Maximum stack depth exceeded";
                break;
            case JSON_ERROR_STATE_MISMATCH:
                echo "Invalid or malformed JSON";
                break;
            case JSON_ERROR_CTRL_CHAR:
                echo "Control character error";
                break;
            case JSON_ERROR_SYNTAX:
                echo "Syntax error, malformed JSON";
                break;
            case JSON_ERROR_UTF8:
                echo "Malformed UTF-8 characters";
                break;
            default:
                echo "Unknown JSON error ($jsonError)";
                break;
        }
        echo "</p>";
        
        echo "<h5>🔧 Possible Issues:</h5>";
        echo "<ul>";
        echo "<li>PHP error/warning output before JSON</li>";
        echo "<li>HTML content mixed with JSON</li>";
        echo "<li>Syntax error in PHP code</li>";
        echo "<li>Incorrect Content-Type header</li>";
        echo "</ul>";
        
        // Check for common issues
        if (strpos($body, '<') !== false) {
            echo "<p style='color: orange;'>⚠️ <strong>HTML detected in response!</strong> This is likely the cause.</p>";
        }
        
        if (strpos($body, 'Warning:') !== false || strpos($body, 'Notice:') !== false || strpos($body, 'Error:') !== false) {
            echo "<p style='color: orange;'>⚠️ <strong>PHP error/warning detected!</strong> This is likely the cause.</p>";
        }
    }
}

curl_close($ch);

echo "<hr>";
echo "<h3>🧪 Direct File Access Test</h3>";
echo "<p>Let's also test direct access to the files API:</p>";
echo "<p><a href='$apiUrl' target='_blank'>Open API directly in new tab</a></p>";

echo "<hr>";
echo "<h3>🔧 Next Steps:</h3>";
echo "<ol>";
echo "<li>Check the response body above for any PHP errors or HTML content</li>";
echo "<li>If you see HTML or PHP errors, we need to fix the API file</li>";
echo "<li>If the JSON is valid, the issue might be in the frontend code</li>";
echo "</ol>";
?>
